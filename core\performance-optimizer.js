/**
 * @file performance-optimizer.js - OTA订单处理系统性能优化和测试模块
 * @description 提供全面的性能监控、优化和测试功能
 * <AUTHOR> IDE
 * @created_at 2025-01-08
 * @updated_at 2025-01-08
 * @version v4.2.0
 * @dependencies logger.js, app-state.js
 */

/**
 * @class PerformanceOptimizer - 性能优化管理器
 * @description 管理系统性能监控、优化和测试功能
 */
class PerformanceOptimizer {
    /**
     * @function constructor - 构造函数
     * @description 初始化性能优化管理器
     * @param {object} appState - 应用状态管理器
     * @param {object} apiService - API服务管理器
     */
    constructor(appState, apiService) {
        this.appState = appState;
        this.apiService = apiService;
        
        // 性能监控配置
        this.performanceConfig = {
            // API加载性能目标
            targetLoadTime: 10000,      // 目标总加载时间：10秒
            targetApiTime: 2000,        // 目标单API时间：2秒
            targetCacheHitRate: 85,     // 目标缓存命中率：85%
            targetMemoryUsage: 120,     // 目标内存使用：120MB
            
            // 监控间隔
            monitoringInterval: 30000,  // 监控间隔：30秒
            testingInterval: 300000,    // 测试间隔：5分钟
            
            // 性能阈值
            thresholds: {
                slowApiTime: 5000,      // 慢API阈值：5秒
                highMemoryUsage: 150,   // 高内存使用：150MB
                lowCacheHitRate: 70     // 低缓存命中率：70%
            }
        };
        
        // 性能统计数据
        this.performanceStats = {
            apiLoadTimes: new Map(),
            cacheStats: {
                hits: 0,
                misses: 0,
                hitRate: 0
            },
            memoryStats: {
                currentUsage: 0,
                peakUsage: 0,
                gcCount: 0
            },
            systemHealth: {
                overallScore: 100,
                lastCheck: Date.now(),
                issues: []
            }
        };
        
        // 测试结果历史
        this.testHistory = [];
        
        // 优化建议
        this.optimizationSuggestions = [];
        
        // 监控定时器
        this.monitoringTimer = null;
        this.testingTimer = null;
        
        logger.info('PerformanceOptimizer', '性能优化器初始化完成');
    }

    /**
     * @function initialize - 初始化性能优化器
     * @description 启动性能监控和自动测试
     */
    async initialize() {
        logger.info('PerformanceOptimizer', '启动性能监控和优化');
        
        try {
            // 启动实时监控
            this.startPerformanceMonitoring();
            
            // 启动定期测试
            this.startPerformanceTesting();
            
            // 初始化内存监控
            this.initializeMemoryMonitoring();
            
            // 运行初始性能测试
            await this.runComprehensivePerformanceTest();
            
            logger.success('PerformanceOptimizer', '性能优化器启动完成');
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '性能优化器启动失败', error);
        }
    }

    /**
     * @function startPerformanceMonitoring - 启动性能监控
     * @description 开始实时性能监控
     */
    startPerformanceMonitoring() {
        logger.info('PerformanceOptimizer', '启动实时性能监控');
        
        this.monitoringTimer = setInterval(() => {
            this.collectPerformanceMetrics();
            this.analyzePerformanceHealth();
            this.updatePerformanceUI();
        }, this.performanceConfig.monitoringInterval);
    }

    /**
     * @function startPerformanceTesting - 启动定期性能测试
     * @description 开始定期自动化性能测试
     */
    startPerformanceTesting() {
        logger.info('PerformanceOptimizer', '启动定期性能测试');
        
        this.testingTimer = setInterval(async () => {
            try {
                await this.runAutomatedPerformanceTest();
            } catch (error) {
                logger.error('PerformanceOptimizer', '自动性能测试失败', error);
            }
        }, this.performanceConfig.testingInterval);
    }

    /**
     * @function collectPerformanceMetrics - 收集性能指标
     * @description 收集当前系统性能指标
     */
    collectPerformanceMetrics() {
        try {
            // 收集内存使用情况
            this.collectMemoryStats();
            
            // 收集缓存统计
            this.collectCacheStats();
            
            // 收集API性能统计
            this.collectApiStats();
            
            logger.debug('PerformanceOptimizer', '性能指标收集完成', {
                memory: this.performanceStats.memoryStats.currentUsage,
                cacheHitRate: this.performanceStats.cacheStats.hitRate,
                apiCount: this.performanceStats.apiLoadTimes.size
            });
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '性能指标收集失败', error);
        }
    }

    /**
     * @function collectMemoryStats - 收集内存统计
     * @description 收集浏览器内存使用统计
     */
    collectMemoryStats() {
        if (performance.memory) {
            const memoryInfo = performance.memory;
            const currentUsage = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
            
            this.performanceStats.memoryStats = {
                currentUsage,
                peakUsage: Math.max(this.performanceStats.memoryStats.peakUsage, currentUsage),
                totalHeapSize: Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024),
                heapSizeLimit: Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024),
                lastUpdated: Date.now()
            };
        }
    }

    /**
     * @function collectCacheStats - 收集缓存统计
     * @description 收集系统缓存命中率统计
     */
    collectCacheStats() {
        try {
            const cacheInfo = this.appState.getCacheInfo();
            const validation = this.appState.validateDataIntegrity();
            
            // 计算缓存命中率
            const totalRequests = this.performanceStats.cacheStats.hits + this.performanceStats.cacheStats.misses;
            const hitRate = totalRequests > 0 ? 
                Math.round((this.performanceStats.cacheStats.hits / totalRequests) * 100) : 0;
            
            this.performanceStats.cacheStats = {
                ...this.performanceStats.cacheStats,
                hitRate,
                cacheSize: cacheInfo.totalSize,
                validData: validation.isValid,
                lastUpdated: Date.now()
            };
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '缓存统计收集失败', error);
        }
    }

    /**
     * @function collectApiStats - 收集API性能统计
     * @description 收集API调用性能统计
     */
    collectApiStats() {
        try {
            const loadingStats = this.appState.getLoadingStatsHistory();
            
            if (loadingStats && loadingStats.length > 0) {
                const latestStats = loadingStats[loadingStats.length - 1];
                
                // 记录各API的平均响应时间
                this.performanceStats.apiLoadTimes.set('total', latestStats.totalTime);
                this.performanceStats.apiLoadTimes.set('average', latestStats.averageApiTime);
                this.performanceStats.apiLoadTimes.set('successRate', latestStats.successRate);
                this.performanceStats.apiLoadTimes.set('lastUpdate', Date.now());
            }
            
        } catch (error) {
            logger.error('PerformanceOptimizer', 'API统计收集失败', error);
        }
    }

    /**
     * @function analyzePerformanceHealth - 分析系统性能健康度
     * @description 分析当前系统性能并生成健康度评分
     */
    analyzePerformanceHealth() {
        try {
            let healthScore = 100;
            const issues = [];
            
            // 检查内存使用
            const memoryUsage = this.performanceStats.memoryStats.currentUsage;
            if (memoryUsage > this.performanceConfig.thresholds.highMemoryUsage) {
                healthScore -= 20;
                issues.push({
                    type: 'memory',
                    severity: 'warning',
                    message: `内存使用过高: ${memoryUsage}MB`,
                    suggestion: '建议清理缓存或重启浏览器'
                });
            }
            
            // 检查缓存命中率
            const cacheHitRate = this.performanceStats.cacheStats.hitRate;
            if (cacheHitRate < this.performanceConfig.thresholds.lowCacheHitRate) {
                healthScore -= 15;
                issues.push({
                    type: 'cache',
                    severity: 'warning',
                    message: `缓存命中率过低: ${cacheHitRate}%`,
                    suggestion: '建议优化缓存策略'
                });
            }
            
            // 检查API响应时间
            const totalLoadTime = this.performanceStats.apiLoadTimes.get('total') || 0;
            if (totalLoadTime > this.performanceConfig.thresholds.slowApiTime) {
                healthScore -= 25;
                issues.push({
                    type: 'api',
                    severity: 'error',
                    message: `API响应时间过慢: ${totalLoadTime}ms`,
                    suggestion: '建议检查网络连接或联系技术支持'
                });
            }
            
            // 更新健康度评分
            this.performanceStats.systemHealth = {
                overallScore: Math.max(0, healthScore),
                lastCheck: Date.now(),
                issues: issues
            };
            
            // 生成优化建议
            this.generateOptimizationSuggestions(issues);
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '性能健康度分析失败', error);
        }
    }

    /**
     * @function generateOptimizationSuggestions - 生成优化建议
     * @description 基于性能分析结果生成优化建议
     * @param {Array} issues - 性能问题列表
     */
    generateOptimizationSuggestions(issues) {
        this.optimizationSuggestions = [];
        
        issues.forEach(issue => {
            switch (issue.type) {
                case 'memory':
                    this.optimizationSuggestions.push({
                        priority: 'high',
                        action: 'memory_optimization',
                        description: '执行内存优化',
                        details: '清理未使用的缓存，释放内存空间'
                    });
                    break;
                    
                case 'cache':
                    this.optimizationSuggestions.push({
                        priority: 'medium',
                        action: 'cache_optimization',
                        description: '优化缓存策略',
                        details: '调整缓存过期时间，提高命中率'
                    });
                    break;
                    
                case 'api':
                    this.optimizationSuggestions.push({
                        priority: 'high',
                        action: 'api_optimization',
                        description: '优化API加载',
                        details: '启用增量更新，减少不必要的API调用'
                    });
                    break;
            }
        });
    }

    /**
     * @function runComprehensivePerformanceTest - 运行全面性能测试
     * @description 执行全面的系统性能测试
     * @returns {Promise<object>} 测试结果
     */
    async runComprehensivePerformanceTest() {
        logger.info('PerformanceOptimizer', '开始全面性能测试');
        const testStartTime = Date.now();
        
        try {
            const testResults = {
                testId: `perf_test_${Date.now()}`,
                startTime: testStartTime,
                tests: {},
                overallScore: 0,
                recommendations: []
            };
            
            // 1. API加载性能测试
            testResults.tests.apiLoadingTest = await this.testApiLoadingPerformance();
            
            // 2. 缓存性能测试
            testResults.tests.cachePerformanceTest = await this.testCachePerformance();
            
            // 3. 内存使用测试
            testResults.tests.memoryUsageTest = await this.testMemoryUsage();
            
            // 4. 并发加载测试
            testResults.tests.concurrentLoadTest = await this.testConcurrentLoading();
            
            // 5. 增量更新测试
            testResults.tests.incrementalUpdateTest = await this.testIncrementalUpdate();
            
            // 计算总体评分
            testResults.overallScore = this.calculateOverallScore(testResults.tests);
            testResults.endTime = Date.now();
            testResults.duration = testResults.endTime - testStartTime;
            
            // 生成测试建议
            testResults.recommendations = this.generateTestRecommendations(testResults.tests);
            
            // 保存测试结果
            this.testHistory.push(testResults);
            
            logger.success('PerformanceOptimizer', '全面性能测试完成', {
                score: testResults.overallScore,
                duration: testResults.duration + 'ms',
                tests: Object.keys(testResults.tests).length
            });
            
            return testResults;
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '全面性能测试失败', error);
            throw error;
        }
    }

    /**
     * @function testApiLoadingPerformance - 测试API加载性能
     * @description 测试系统API加载性能
     * @returns {Promise<object>} API性能测试结果
     */
    async testApiLoadingPerformance() {
        logger.info('PerformanceOptimizer', '执行API加载性能测试');
        
        try {
            const testResults = {
                testName: 'API Loading Performance',
                startTime: Date.now(),
                results: {},
                score: 0
            };
            
            // 检查是否已登录，未登录则跳过API测试
            if (!this.appState.token) {
                logger.info('PerformanceOptimizer', '用户未登录，跳过API性能测试');
                testResults.results = {
                    message: '用户未登录，API测试已跳过',
                    status: 'skipped'
                };
                testResults.score = 50; // 给一个中性分数
                testResults.endTime = Date.now();
                return testResults;
            }
            
            // 测试各个API的响应时间
            const apiTests = [
                { name: 'BackendUsers', method: () => this.apiService.getBackendUsers() },
                { name: 'SubCategories', method: () => this.apiService.getSubCategories() },
                { name: 'CarTypes', method: () => this.apiService.getCarTypes() },
                { name: 'DrivingRegions', method: () => this.apiService.getDrivingRegions() },
                { name: 'Languages', method: () => this.apiService.getLanguages() }
            ];
            
            for (const apiTest of apiTests) {
                const apiStartTime = Date.now();
                
                try {
                    await apiTest.method();
                    const responseTime = Date.now() - apiStartTime;
                    
                    testResults.results[apiTest.name] = {
                        responseTime,
                        status: 'success',
                        score: this.calculateApiScore(responseTime)
                    };
                    
                } catch (error) {
                    testResults.results[apiTest.name] = {
                        responseTime: Date.now() - apiStartTime,
                        status: 'failed',
                        error: error.message,
                        score: 0
                    };
                }
            }
            
            // 计算API测试总分
            const apiScores = Object.values(testResults.results).map(r => r.score);
            testResults.score = apiScores.reduce((sum, score) => sum + score, 0) / apiScores.length;
            testResults.endTime = Date.now();
            
            return testResults;
            
        } catch (error) {
            logger.error('PerformanceOptimizer', 'API性能测试失败', error);
            return { testName: 'API Loading Performance', score: 0, error: error.message };
        }
    }

    /**
     * @function testCachePerformance - 测试缓存性能
     * @description 测试系统缓存命中率和效率
     * @returns {Promise<object>} 缓存性能测试结果
     */
    async testCachePerformance() {
        logger.info('PerformanceOptimizer', '执行缓存性能测试');
        
        try {
            const testResults = {
                testName: 'Cache Performance',
                startTime: Date.now(),
                results: {},
                score: 0
            };
            
            // 重置缓存统计
            const initialHits = this.performanceStats.cacheStats.hits;
            const initialMisses = this.performanceStats.cacheStats.misses;
            
            // 执行多次数据访问测试缓存命中率
            for (let i = 0; i < 5; i++) {
                const accessStartTime = Date.now();
                
                // 访问已缓存的数据
                const validation = this.appState.validateDataIntegrity();
                if (validation.isValid) {
                    this.performanceStats.cacheStats.hits++;
                } else {
                    this.performanceStats.cacheStats.misses++;
                }
                
                const accessTime = Date.now() - accessStartTime;
                testResults.results[`access_${i + 1}`] = {
                    accessTime,
                    cacheHit: validation.isValid
                };
            }
            
            // 计算缓存性能评分
            const totalRequests = this.performanceStats.cacheStats.hits + this.performanceStats.cacheStats.misses;
            const hitRate = totalRequests > 0 ? 
                (this.performanceStats.cacheStats.hits / totalRequests) * 100 : 0;
            
            testResults.score = Math.min(100, hitRate);
            testResults.hitRate = hitRate;
            testResults.endTime = Date.now();
            
            return testResults;
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '缓存性能测试失败', error);
            return { testName: 'Cache Performance', score: 0, error: error.message };
        }
    }

    /**
     * @function testMemoryUsage - 测试内存使用
     * @description 测试系统内存使用效率
     * @returns {Promise<object>} 内存使用测试结果
     */
    async testMemoryUsage() {
        logger.info('PerformanceOptimizer', '执行内存使用测试');
        
        try {
            const testResults = {
                testName: 'Memory Usage',
                startTime: Date.now(),
                results: {},
                score: 0
            };
            
            // 记录初始内存使用
            this.collectMemoryStats();
            const initialMemory = this.performanceStats.memoryStats.currentUsage;
            
            // 执行一些内存密集型操作来测试内存管理
            const testData = [];
            for (let i = 0; i < 1000; i++) {
                testData.push({
                    id: i,
                    data: new Array(100).fill(Math.random())
                });
            }
            
            // 等待垃圾回收
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 清理测试数据
            testData.length = 0;
            
            // 强制垃圾回收（如果可能）
            if (window.gc) {
                window.gc();
            }
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 记录最终内存使用
            this.collectMemoryStats();
            const finalMemory = this.performanceStats.memoryStats.currentUsage;
            
            // 计算内存使用评分
            const memoryGrowth = finalMemory - initialMemory;
            testResults.results = {
                initialMemory,
                finalMemory,
                memoryGrowth,
                peakMemory: this.performanceStats.memoryStats.peakUsage
            };
            
            // 评分标准：内存增长越少分数越高
            testResults.score = Math.max(0, 100 - (memoryGrowth * 2));
            testResults.endTime = Date.now();
            
            return testResults;
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '内存使用测试失败', error);
            return { testName: 'Memory Usage', score: 0, error: error.message };
        }
    }

    /**
     * @function testConcurrentLoading - 测试并发加载性能
     * @description 测试系统并发数据加载性能
     * @returns {Promise<object>} 并发加载测试结果
     */
    async testConcurrentLoading() {
        logger.info('PerformanceOptimizer', '执行并发加载性能测试');
        
        try {
            const testResults = {
                testName: 'Concurrent Loading',
                startTime: Date.now(),
                results: {},
                score: 0
            };
            
            // 检查是否已登录，未登录则跳过API测试
            if (!this.appState.token) {
                logger.info('PerformanceOptimizer', '用户未登录，跳过并发加载测试');
                testResults.results = {
                    message: '用户未登录，并发加载测试已跳过',
                    status: 'skipped'
                };
                testResults.score = 50; // 给一个中性分数
                testResults.endTime = Date.now();
                return testResults;
            }
            
            const concurrentStartTime = Date.now();
            
            // 同时请求多个API测试并发性能
            const concurrentPromises = [
                this.apiService.getBackendUsers(),
                this.apiService.getSubCategories(),
                this.apiService.getCarTypes(),
                this.apiService.getDrivingRegions(),
                this.apiService.getLanguages()
            ];
            
            const results = await Promise.allSettled(concurrentPromises);
            const concurrentTime = Date.now() - concurrentStartTime;
            
            // 分析并发结果
            let successCount = 0;
            results.forEach((result, index) => {
                const apiName = ['BackendUsers', 'SubCategories', 'CarTypes', 'DrivingRegions', 'Languages'][index];
                
                if (result.status === 'fulfilled') {
                    successCount++;
                    testResults.results[apiName] = { status: 'success' };
                } else {
                    testResults.results[apiName] = { 
                        status: 'failed', 
                        error: result.reason?.message || '未知错误' 
                    };
                }
            });
            
            // 计算并发性能评分
            const successRate = (successCount / results.length) * 100;
            const timeScore = Math.max(0, 100 - (concurrentTime / 100)); // 时间越短分数越高
            testResults.score = (successRate + timeScore) / 2;
            
            testResults.concurrentTime = concurrentTime;
            testResults.successRate = successRate;
            testResults.endTime = Date.now();
            
            return testResults;
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '并发加载测试失败', error);
            return { testName: 'Concurrent Loading', score: 0, error: error.message };
        }
    }

    /**
     * @function testIncrementalUpdate - 测试增量更新性能
     * @description 测试系统增量数据更新性能
     * @returns {Promise<object>} 增量更新测试结果
     */
    async testIncrementalUpdate() {
        logger.info('PerformanceOptimizer', '执行增量更新性能测试');
        
        try {
            const testResults = {
                testName: 'Incremental Update',
                startTime: Date.now(),
                results: {},
                score: 0
            };
            
            // 模拟数据变更检测
            const originalTimestamp = this.appState.getSystemDataTimestamp();
            
            // 检查缓存是否需要更新
            const updateStartTime = Date.now();
            const needsUpdate = await this.checkDataCacheStatus();
            const checkTime = Date.now() - updateStartTime;
            
            testResults.results.cacheCheck = {
                checkTime,
                needsUpdate
            };
            
            // 如果需要更新，测试增量更新性能
            if (needsUpdate) {
                const updateStartTime = Date.now();
                await this.performIncrementalUpdate();
                const updateTime = Date.now() - updateStartTime;
                
                testResults.results.incrementalUpdate = {
                    updateTime,
                    status: 'completed'
                };
                
                // 评分：更新时间越短分数越高
                testResults.score = Math.max(0, 100 - (updateTime / 100));
            } else {
                testResults.results.incrementalUpdate = {
                    status: 'not_needed'
                };
                testResults.score = 100; // 不需要更新是最优情况
            }
            
            testResults.endTime = Date.now();
            return testResults;
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '增量更新测试失败', error);
            return { testName: 'Incremental Update', score: 0, error: error.message };
        }
    }

    /**
     * @function checkDataCacheStatus - 检查数据缓存状态
     * @description 检查是否需要更新缓存数据
     * @returns {Promise<boolean>} 是否需要更新
     */
    async checkDataCacheStatus() {
        try {
            // 检查数据完整性
            const validation = this.appState.validateDataIntegrity();
            if (!validation.isValid) {
                return true;
            }
            
            // 检查缓存时间
            const lastUpdate = this.appState.getSystemDataTimestamp();
            const cacheAge = Date.now() - lastUpdate;
            const CACHE_TTL = 60 * 60 * 1000; // 1小时
            
            if (cacheAge > CACHE_TTL) {
                return true;
            }
            
            // 检查强制刷新标志
            if (this.appState.getForceRefreshFlag()) {
                return true;
            }
            
            return false;
            
        } catch (error) {
            logger.warn('PerformanceOptimizer', '缓存状态检查失败', error);
            return true;
        }
    }

    /**
     * @function performIncrementalUpdate - 执行增量更新
     * @description 执行智能增量数据更新
     * @returns {Promise<boolean>} 更新结果
     */
    async performIncrementalUpdate() {
        logger.info('PerformanceOptimizer', '执行增量数据更新');
        
        try {
            // 检查是否已登录
            if (!this.appState.token) {
                logger.info('PerformanceOptimizer', '用户未登录，跳过增量数据更新');
                return false;
            }
            
            // 检查哪些数据需要更新
            const updateNeeds = await this.analyzeUpdateNeeds();
            
            // 只更新需要的数据
            const updatePromises = [];
            
            if (updateNeeds.coreData) {
                updatePromises.push(
                    this.apiService.getBackendUsers(),
                    this.apiService.getSubCategories(),
                    this.apiService.getCarTypes()
                );
            }
            
            if (updateNeeds.enhancedData) {
                updatePromises.push(
                    this.apiService.getDrivingRegions(),
                    this.apiService.getLanguages()
                );
            }
            
            // 执行增量更新
            if (updatePromises.length > 0) {
                await Promise.allSettled(updatePromises);
                logger.success('PerformanceOptimizer', '增量更新完成', {
                    updatedApis: updatePromises.length
                });
                return true;
            } else {
                logger.info('PerformanceOptimizer', '无需更新数据');
                return false;
            }
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '增量更新失败', error);
            throw error;
        }
    }

    /**
     * @function analyzeUpdateNeeds - 分析更新需求
     * @description 分析哪些数据需要更新
     * @returns {Promise<object>} 更新需求分析结果
     */
    async analyzeUpdateNeeds() {
        try {
            const updateNeeds = {
                coreData: false,
                enhancedData: false,
                reasons: []
            };
            
            // 检查核心数据
            const coreDataValid = this.appState.backendUsers.length > 0 &&
                                 this.appState.subCategories.length > 0 &&
                                 this.appState.carTypes.length > 0;
            
            if (!coreDataValid) {
                updateNeeds.coreData = true;
                updateNeeds.reasons.push('核心数据缺失或无效');
            }
            
            // 检查增强数据
            const enhancedDataValid = this.appState.drivingRegions.length > 0 &&
                                     this.appState.languages.length > 0;
            
            if (!enhancedDataValid) {
                updateNeeds.enhancedData = true;
                updateNeeds.reasons.push('增强数据缺失或无效');
            }
            
            // 检查数据时效性
            const dataAge = Date.now() - this.appState.getSystemDataTimestamp();
            const DATA_EXPIRY = 2 * 60 * 60 * 1000; // 2小时
            
            if (dataAge > DATA_EXPIRY) {
                updateNeeds.coreData = true;
                updateNeeds.enhancedData = true;
                updateNeeds.reasons.push('数据已过期');
            }
            
            return updateNeeds;
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '更新需求分析失败', error);
            return { coreData: true, enhancedData: true, reasons: ['分析失败，执行完整更新'] };
        }
    }

    /**
     * @function calculateApiScore - 计算API性能评分
     * @description 根据API响应时间计算性能评分
     * @param {number} responseTime - API响应时间（毫秒）
     * @returns {number} 性能评分 (0-100)
     */
    calculateApiScore(responseTime) {
        // 评分标准：2秒内100分，每增加1秒减10分
        const baseScore = 100;
        const penalty = Math.max(0, (responseTime - 2000) / 1000) * 10;
        return Math.max(0, baseScore - penalty);
    }

    /**
     * @function calculateOverallScore - 计算总体性能评分
     * @description 根据各项测试结果计算总体评分
     * @param {object} testResults - 所有测试结果
     * @returns {number} 总体评分 (0-100)
     */
    calculateOverallScore(testResults) {
        const scores = Object.values(testResults).map(test => test.score || 0);
        const validScores = scores.filter(score => score > 0);
        
        if (validScores.length === 0) return 0;
        
        return Math.round(validScores.reduce((sum, score) => sum + score, 0) / validScores.length);
    }

    /**
     * @function generateTestRecommendations - 生成测试建议
     * @description 根据测试结果生成优化建议
     * @param {object} testResults - 测试结果
     * @returns {Array} 优化建议列表
     */
    generateTestRecommendations(testResults) {
        const recommendations = [];
        
        // 分析API性能
        if (testResults.apiLoadingTest && testResults.apiLoadingTest.score < 70) {
            recommendations.push({
                type: 'api_optimization',
                priority: 'high',
                message: 'API响应时间过慢，建议优化网络连接或联系技术支持',
                action: '检查网络状况，考虑启用数据预加载'
            });
        }
        
        // 分析缓存性能
        if (testResults.cachePerformanceTest && testResults.cachePerformanceTest.score < 80) {
            recommendations.push({
                type: 'cache_optimization',
                priority: 'medium',
                message: '缓存命中率较低，建议优化缓存策略',
                action: '调整缓存过期时间，增加预缓存机制'
            });
        }
        
        // 分析内存使用
        if (testResults.memoryUsageTest && testResults.memoryUsageTest.score < 70) {
            recommendations.push({
                type: 'memory_optimization',
                priority: 'high',
                message: '内存使用效率较低，建议执行内存优化',
                action: '清理未使用的缓存，定期执行垃圾回收'
            });
        }
        
        // 分析并发性能
        if (testResults.concurrentLoadTest && testResults.concurrentLoadTest.score < 80) {
            recommendations.push({
                type: 'concurrent_optimization',
                priority: 'medium',
                message: '并发加载性能不佳，建议优化并发策略',
                action: '调整API并发数量，实施请求队列管理'
            });
        }
        
        return recommendations;
    }

    /**
     * @function runAutomatedPerformanceTest - 运行自动化性能测试
     * @description 定期执行的轻量级性能测试
     * @returns {Promise<object>} 自动测试结果
     */
    async runAutomatedPerformanceTest() {
        logger.info('PerformanceOptimizer', '执行自动化性能测试');
        
        try {
            const testResults = {
                testId: `auto_test_${Date.now()}`,
                startTime: Date.now(),
                type: 'automated',
                results: {}
            };
            
            // 轻量级API测试
            testResults.results.apiQuickTest = await this.quickApiTest();
            
            // 内存检查
            this.collectMemoryStats();
            testResults.results.memoryCheck = {
                currentUsage: this.performanceStats.memoryStats.currentUsage,
                score: this.performanceStats.memoryStats.currentUsage < 100 ? 100 : 50
            };
            
            // 缓存检查
            this.collectCacheStats();
            testResults.results.cacheCheck = {
                hitRate: this.performanceStats.cacheStats.hitRate,
                score: this.performanceStats.cacheStats.hitRate > 70 ? 100 : 50
            };
            
            testResults.endTime = Date.now();
            testResults.overallScore = this.calculateOverallScore(testResults.results);
            
            // 如果发现性能问题，记录警告
            if (testResults.overallScore < 60) {
                logger.warn('PerformanceOptimizer', '自动测试发现性能问题', {
                    score: testResults.overallScore,
                    issues: Object.keys(testResults.results).filter(
                        key => testResults.results[key].score < 70
                    )
                });
            }
            
            return testResults;
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '自动化测试失败', error);
            return { testId: `auto_test_${Date.now()}`, error: error.message, score: 0 };
        }
    }

    /**
     * @function quickApiTest - 快速API测试
     * @description 执行快速的API响应测试
     * @returns {Promise<object>} 快速测试结果
     */
    async quickApiTest() {
        try {
            // 检查是否已登录
            if (!this.appState.token) {
                return {
                    responseTime: 0,
                    score: 50,
                    status: 'skipped',
                    message: '用户未登录，API测试已跳过'
                };
            }
            
            const startTime = Date.now();
            
            // 测试一个轻量级API调用
            await this.apiService.getBackendUsers();
            
            const responseTime = Date.now() - startTime;
            const score = this.calculateApiScore(responseTime);
            
            return {
                responseTime,
                score,
                status: 'success'
            };
            
        } catch (error) {
            return {
                responseTime: 0,
                score: 0,
                status: 'failed',
                error: error.message
            };
        }
    }

    /**
     * @function initializeMemoryMonitoring - 初始化内存监控
     * @description 设置内存使用监控和优化
     */
    initializeMemoryMonitoring() {
        logger.info('PerformanceOptimizer', '初始化内存监控');
        
        // 定期检查内存使用
        setInterval(() => {
            this.collectMemoryStats();
            
            // 如果内存使用过高，执行优化
            if (this.performanceStats.memoryStats.currentUsage > 
                this.performanceConfig.thresholds.highMemoryUsage) {
                this.executeMemoryOptimization();
            }
        }, 60000); // 每分钟检查一次
    }

    /**
     * @function executeMemoryOptimization - 执行内存优化
     * @description 执行内存清理和优化操作
     */
    executeMemoryOptimization() {
        logger.info('PerformanceOptimizer', '执行内存优化');
        
        try {
            // 清理应用状态中的过期数据
            this.appState.optimizeMemoryUsage();
            
            // 清理过期的测试历史
            this.cleanupTestHistory();
            
            // 清理性能统计中的旧数据
            this.cleanupPerformanceStats();
            
            // 尝试触发垃圾回收
            if (window.gc) {
                window.gc();
            }
            
            logger.success('PerformanceOptimizer', '内存优化完成');
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '内存优化失败', error);
        }
    }

    /**
     * @function cleanupTestHistory - 清理测试历史
     * @description 清理旧的测试历史记录
     */
    cleanupTestHistory() {
        const maxHistorySize = 50;
        
        if (this.testHistory.length > maxHistorySize) {
            this.testHistory = this.testHistory.slice(-maxHistorySize);
            logger.debug('PerformanceOptimizer', '清理测试历史记录', {
                remaining: this.testHistory.length
            });
        }
    }

    /**
     * @function cleanupPerformanceStats - 清理性能统计
     * @description 清理旧的性能统计数据
     */
    cleanupPerformanceStats() {
        // 清理API加载时间中的旧记录
        const maxApiStats = 100;
        if (this.performanceStats.apiLoadTimes.size > maxApiStats) {
            const entries = Array.from(this.performanceStats.apiLoadTimes.entries());
            this.performanceStats.apiLoadTimes.clear();
            
            // 保留最新的记录
            entries.slice(-maxApiStats).forEach(([key, value]) => {
                this.performanceStats.apiLoadTimes.set(key, value);
            });
        }
        
        // 重置累积的缓存统计
        if (this.performanceStats.cacheStats.hits > 10000) {
            this.performanceStats.cacheStats.hits = Math.floor(this.performanceStats.cacheStats.hits / 2);
            this.performanceStats.cacheStats.misses = Math.floor(this.performanceStats.cacheStats.misses / 2);
        }
    }

    /**
     * @function updatePerformanceUI - 更新性能监控UI
     * @description 更新页面中的性能监控显示
     */
    updatePerformanceUI() {
        try {
            const monitor = document.getElementById('performanceMonitor');
            if (!monitor) return;
            
            // 更新统计数据
            const avgLoadTime = monitor.querySelector('.avg-load-time');
            const successRate = monitor.querySelector('.success-rate');
            const cacheHitRate = monitor.querySelector('.cache-hit-rate');
            
            if (avgLoadTime) {
                const avgTime = this.performanceStats.apiLoadTimes.get('average') || 0;
                avgLoadTime.textContent = `${Math.round(avgTime)}ms`;
            }
            
            if (successRate) {
                const success = this.performanceStats.apiLoadTimes.get('successRate') || 0;
                successRate.textContent = `${Math.round(success)}%`;
            }
            
            if (cacheHitRate) {
                cacheHitRate.textContent = `${this.performanceStats.cacheStats.hitRate}%`;
            }
            
        } catch (error) {
            logger.error('PerformanceOptimizer', '性能UI更新失败', error);
        }
    }

    /**
     * @function getPerformanceReport - 获取性能报告
     * @description 生成详细的性能分析报告
     * @returns {object} 性能报告
     */
    getPerformanceReport() {
        return {
            timestamp: Date.now(),
            systemHealth: this.performanceStats.systemHealth,
            performanceStats: this.performanceStats,
            testHistory: this.testHistory.slice(-10), // 最近10次测试
            optimizationSuggestions: this.optimizationSuggestions,
            configuration: this.performanceConfig
        };
    }

    /**
     * @function destroy - 销毁性能优化器
     * @description 清理资源和定时器
     */
    destroy() {
        logger.info('PerformanceOptimizer', '销毁性能优化器');
        
        // 清理定时器
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
            this.monitoringTimer = null;
        }
        
        if (this.testingTimer) {
            clearInterval(this.testingTimer);
            this.testingTimer = null;
        }
        
        // 清理数据
        this.performanceStats = null;
        this.testHistory = [];
        this.optimizationSuggestions = [];
        
        logger.success('PerformanceOptimizer', '性能优化器销毁完成');
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
} else {
    window.PerformanceOptimizer = PerformanceOptimizer;
}

logger.info('模块', 'PerformanceOptimizer模块加载完成', { version: 'v4.2.0' }); 