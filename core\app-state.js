/**
 * @file app-state.js - 应用状态管理模块（Observer模式增强版）
 * @description 用户数据管理、缓存机制、数据验证、用户切换检测、Observer模式支持
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.1.0
 */

/**
 * @class AppState - 应用状态管理类（Observer模式增强版）
 * @description 管理应用的全局状态，包括用户认证、数据缓存、用户切换检测，支持Observer模式自动UI同步
 */
class AppState {
    /**
     * @function constructor - 构造函数
     * @description 初始化应用状态，设置用户数据缓存机制和Observer模式
     */
    constructor() {
        this.token = localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN);
        this.userInfo = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO) || 'null');
        this.currentUserHash = null; // 当前用户标识
        this.userDataCache = new Map(); // 用户数据缓存
        this.backendUsers = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.BACKEND_USERS) || '[]');
        this.subCategories = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.SUB_CATEGORIES) || '[]');
        this.carTypes = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.CAR_TYPES) || '[]');
        this.drivingRegions = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.DRIVING_REGIONS) || '[]'); // 新增：行驶区域数据
        this.languages = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.LANGUAGES) || '[]'); // 新增：语言数据
        this.currentProfile = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.CURRENT_PROFILE) || 'null'); // 新增：当前Profile
        this.processedOrders = [];
        
        // Observer模式支持
        this.observers = new Map(); // 事件 -> 回调函数数组
        this.observerIdCounter = 0; // 观察者ID计数器
        
        // 初始化用户数据
        this.initializeUserData();
        
        logger.info('AppState', 'Observer模式已启用', {
            supportedEvents: this.getSupportedEvents()
        });
    }

    // === Observer模式核心方法 ===

    /**
     * @function subscribe - 订阅事件
     * @description 注册观察者回调函数，支持自动UI同步
     * @param {string} event - 事件名称
     * @param {function} callback - 回调函数
     * @param {object} options - 选项 {once: boolean, priority: number}
     * @returns {number} 观察者ID，用于取消订阅
     */
    subscribe(event, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('回调函数必须是function类型');
        }

        if (!this.observers.has(event)) {
            this.observers.set(event, []);
        }

        const observerId = ++this.observerIdCounter;
        const observer = {
            id: observerId,
            callback,
            once: options.once || false,
            priority: options.priority || 0,
            subscribedAt: Date.now()
        };

        const eventObservers = this.observers.get(event);
        eventObservers.push(observer);
        
        // 按优先级排序（高优先级先执行）
        eventObservers.sort((a, b) => b.priority - a.priority);

        logger.debug('AppState', '观察者已注册', {
            event,
            observerId,
            totalObservers: eventObservers.length,
            options
        });

        return observerId;
    }

    /**
     * @function unsubscribe - 取消订阅
     * @description 移除指定的观察者
     * @param {string} event - 事件名称
     * @param {number} observerId - 观察者ID
     * @returns {boolean} 是否成功移除
     */
    unsubscribe(event, observerId) {
        if (!this.observers.has(event)) {
            return false;
        }

        const eventObservers = this.observers.get(event);
        const index = eventObservers.findIndex(observer => observer.id === observerId);
        
        if (index !== -1) {
            eventObservers.splice(index, 1);
            logger.debug('AppState', '观察者已移除', {
                event,
                observerId,
                remainingObservers: eventObservers.length
            });
            return true;
        }

        return false;
    }

    /**
     * @function notify - 通知观察者
     * @description 触发事件，通知所有注册的观察者
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     * @param {object} context - 事件上下文
     */
    notify(event, data = null, context = {}) {
        if (!this.observers.has(event)) {
            logger.debug('AppState', '无观察者订阅此事件', { event });
            return;
        }

        const eventObservers = this.observers.get(event);
        const eventData = {
            event,
            data,
            context,
            timestamp: Date.now(),
            source: 'AppState'
        };

        logger.debug('AppState', '通知观察者', {
            event,
            observerCount: eventObservers.length,
            dataType: typeof data
        });

        // 执行观察者回调（倒序遍历，支持在回调中移除观察者）
        for (let i = eventObservers.length - 1; i >= 0; i--) {
            const observer = eventObservers[i];
            
            try {
                observer.callback(eventData);
                
                // 如果是一次性观察者，执行后移除
                if (observer.once) {
                    eventObservers.splice(i, 1);
                    logger.debug('AppState', '一次性观察者已移除', {
                        event,
                        observerId: observer.id
                    });
                }
            } catch (error) {
                logger.error('AppState', '观察者回调执行失败', {
                    event,
                    observerId: observer.id,
                    error: error.message
                });
            }
        }
    }

    /**
     * @function getSupportedEvents - 获取支持的事件列表
     * @description 返回所有支持的事件类型
     * @returns {Array<string>} 支持的事件列表
     */
    getSupportedEvents() {
        return [
            'userLogin',      // 用户登录
            'userLogout',     // 用户登出
            'userSwitch',     // 用户切换
            'tokenUpdate',    // Token更新
            'dataUpdate',     // 系统数据更新
            'cacheUpdate',    // 缓存更新
            'authStateChange' // 认证状态变更
        ];
    }

    /**
     * @function clearObservers - 清除所有观察者
     * @description 清除指定事件或所有事件的观察者
     * @param {string} event - 事件名称，不传则清除所有
     */
    clearObservers(event = null) {
        if (event) {
            this.observers.delete(event);
            logger.debug('AppState', '事件观察者已清除', { event });
        } else {
            this.observers.clear();
            logger.debug('AppState', '所有观察者已清除');
        }
    }

    // === 原有方法（增强Observer支持） ===

    /**
     * @function initializeUserData - 初始化用户数据
     * @description 设置用户标识和加载用户特定的系统数据
     */
    initializeUserData() {
        if (this.userInfo) {
            this.currentUserHash = this.getUserHash();
            this.loadUserSystemData();
            logger.info('AppState', '用户数据初始化完成', {
                userHash: this.currentUserHash,
                userEmail: this.userInfo.email
            });
            
            // 通知观察者：用户已登录
            this.notify('userLogin', {
                userInfo: this.userInfo,
                userHash: this.currentUserHash
            });
        } else {
            logger.debug('AppState', '无用户信息，使用兼容模式');
        }
    }

    /**
     * @function getUserHash - 生成用户标识哈希
     * @description 基于用户邮箱和ID生成唯一标识
     * @returns {string|null} 用户唯一标识
     */
    getUserHash() {
        if (!this.userInfo) return null;
        
        // 基于用户邮箱和ID生成哈希
        const userKey = `${this.userInfo.email || 'unknown'}_${this.userInfo.id || 'anonymous'}`;
        return btoa(userKey).replace(/[^a-zA-Z0-9]/g, '');
    }

    /**
     * @function setToken - 设置认证令牌
     * @description 更新认证令牌并持久化存储，通知观察者
     * @param {string} token - 认证令牌
     */
    setToken(token) {
        const oldToken = this.token;
        this.token = token;
        localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN, token);
        
        // 通知观察者：Token已更新
        this.notify('tokenUpdate', {
            oldToken,
            newToken: token,
            hasToken: !!token
        });
        
        // 通知认证状态变更
        this.notify('authStateChange', {
            type: 'tokenUpdate',
            authenticated: !!token
        });
    }

    /**
     * @function setUserInfo - 设置用户信息并更新缓存策略
     * @description 设置用户信息，检测用户切换，管理数据缓存，通知观察者
     * @param {object} userInfo - 用户信息对象
     */
    setUserInfo(userInfo) {
        const oldUserHash = this.currentUserHash;
        const oldUserEmail = this.userInfo?.email;
        const isUserSwitch = oldUserHash && this.userInfo;
        
        this.userInfo = userInfo;
        this.currentUserHash = this.getUserHash();
        
        // 检测用户切换
        if (oldUserHash && oldUserHash !== this.currentUserHash) {
            logger.info('AppState', '检测到用户切换', {
                oldUser: oldUserEmail,
                newUser: userInfo?.email,
                oldHash: oldUserHash,
                newHash: this.currentUserHash
            });
            
            // 清理旧用户数据
            this.clearUserSpecificData();
            
            // 通知观察者：用户切换
            this.notify('userSwitch', {
                oldUser: oldUserEmail,
                newUser: userInfo?.email,
                oldUserHash,
                newUserHash: this.currentUserHash
            });
            
            // 触发用户切换事件（向后兼容）
            this.triggerUserSwitchEvent(oldUserEmail, userInfo?.email);
        } else if (!isUserSwitch) {
            // 首次登录
            this.notify('userLogin', {
                userInfo,
                userHash: this.currentUserHash,
                isFirstLogin: true
            });
        }
        
        localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
        
        // 加载新用户的系统数据
        if (this.currentUserHash) {
            this.loadUserSystemData();
        }
        
        // 通知认证状态变更
        this.notify('authStateChange', {
            type: 'userInfoUpdate',
            authenticated: !!userInfo,
            userInfo
        });
    }

    /**
     * @function triggerUserSwitchEvent - 触发用户切换事件
     * @description 发送自定义事件通知应用用户已切换（向后兼容）
     * @param {string} oldUser - 旧用户邮箱
     * @param {string} newUser - 新用户邮箱
     */
    triggerUserSwitchEvent(oldUser, newUser) {
        const event = new CustomEvent('userSwitch', {
            detail: { oldUser, newUser }
        });
        window.dispatchEvent(event);
    }

    /**
     * @function clearAuth - 清除认证信息
     * @description 清除所有认证相关的数据和状态，通知观察者
     */
    clearAuth() {
        const oldUserInfo = this.userInfo;
        const oldUserHash = this.currentUserHash;
        
        logger.info('AppState', '清除认证信息', { 
            userHash: this.currentUserHash 
        });
        
        this.token = null;
        this.userInfo = null;
        this.currentUserHash = null;
        localStorage.removeItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN);
        localStorage.removeItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO);
        
        // 通知观察者：用户登出
        this.notify('userLogout', {
            oldUserInfo,
            oldUserHash,
            clearedAt: Date.now()
        });
        
        // 通知认证状态变更
        this.notify('authStateChange', {
            type: 'logout',
            authenticated: false
        });
    }

    /**
     * @function cacheSystemData - 用户关联的系统数据缓存
     * @description 将系统数据与用户关联并缓存，支持用户数据隔离，通知观察者
     * @param {string} key - 缓存键
     * @param {any} data - 要缓存的数据
     */
    cacheSystemData(key, data) {
        const oldData = this[key];
        
        if (!this.currentUserHash) {
            logger.warn('AppState', '无用户标识，跳过数据缓存', { key });
            // 向后兼容：仍然更新实例属性和旧版存储
            this[key] = data;
            const storageKey = SYSTEM_CONFIG.STORAGE_KEYS[key.toUpperCase()];
            if (storageKey) {
                localStorage.setItem(storageKey, JSON.stringify(data));
            }
            
            // 通知数据更新
            this.notify('dataUpdate', {
                key,
                oldData,
                newData: data,
                userHash: null,
                isLegacyMode: true
            });
            return;
        }
        
        // 获取或创建用户缓存空间
        if (!this.userDataCache.has(this.currentUserHash)) {
            this.userDataCache.set(this.currentUserHash, {
                cacheTime: Date.now(),
                data: {}
            });
        }
        
        const userCache = this.userDataCache.get(this.currentUserHash);
        userCache.data[key] = data;
        userCache.lastUpdate = Date.now();
        
        // 持久化存储（添加用户前缀）
        const storageKey = `${this.currentUserHash}_${key}`;
        this.saveToStorage(storageKey, data);
        
        // 更新实例属性（向后兼容）
        this[key] = data;
        
        logger.debug('AppState', '用户数据缓存已更新', {
            userHash: this.currentUserHash,
            key,
            dataSize: data ? JSON.stringify(data).length : 0
        });
        
        // 通知观察者：数据更新
        this.notify('dataUpdate', {
            key,
            oldData,
            newData: data,
            userHash: this.currentUserHash,
            updateTime: userCache.lastUpdate
        });
        
        // 通知缓存更新
        this.notify('cacheUpdate', {
            key,
            userHash: this.currentUserHash,
            cacheSize: this.userDataCache.size,
            operation: 'update'
        });
    }

    /**
     * @function getUserSystemData - 获取用户系统数据
     * @description 从用户缓存中获取特定的系统数据
     * @param {string} key - 数据键
     * @returns {any} 缓存数据
     */
    getUserSystemData(key) {
        if (!this.currentUserHash) {
            // 向后兼容：返回实例属性
            return this[key] || null;
        }
        
        const userCache = this.userDataCache.get(this.currentUserHash);
        return userCache?.data?.[key] || null;
    }

    /**
     * @function clearUserSpecificData - 清理用户特定数据
     * @description 清理当前用户的所有缓存数据，用于用户切换时
     */
    clearUserSpecificData() {
        logger.info('AppState', '开始清理用户特定数据', { 
            userHash: this.currentUserHash 
        });
        
        // 清理内存缓存
        this.backendUsers = [];
        this.subCategories = [];
        this.carTypes = [];
        this.drivingRegions = []; // 新增：清理行驶区域数据
        this.languages = []; // 新增：清理语言数据
        this.currentProfile = null; // 新增：清理当前Profile
        this.processedOrders = [];
        
        // 清理当前用户的持久化缓存
        if (this.currentUserHash) {
            const keys = ['backendUsers', 'subCategories', 'carTypes', 'drivingRegions', 'languages'];
            keys.forEach(key => {
                const storageKey = `${this.currentUserHash}_${key}`;
                localStorage.removeItem(storageKey);
            });
            
            this.userDataCache.delete(this.currentUserHash);
        }
        
        logger.success('AppState', '用户特定数据清理完成');
    }

    /**
     * @function loadUserSystemData - 加载用户系统数据
     * @description 从持久化存储中加载用户特定的系统数据
     */
    loadUserSystemData() {
        if (!this.currentUserHash) {
            logger.warn('AppState', '无用户标识，跳过数据加载');
            return;
        }
        
        const keys = ['backendUsers', 'subCategories', 'carTypes', 'drivingRegions', 'languages'];
        let loadedCount = 0;
        
        keys.forEach(key => {
            const storageKey = `${this.currentUserHash}_${key}`;
            const data = this.loadFromStorage(storageKey);
            if (data && Array.isArray(data) && data.length > 0) {
                this[key] = data;
                
                // 更新用户缓存
                if (!this.userDataCache.has(this.currentUserHash)) {
                    this.userDataCache.set(this.currentUserHash, { 
                        cacheTime: Date.now(),
                        data: {} 
                    });
                }
                this.userDataCache.get(this.currentUserHash).data[key] = data;
                loadedCount++;
            }
        });
        
        // 加载当前Profile
        const profileData = this.loadFromStorage(SYSTEM_CONFIG.STORAGE_KEYS.CURRENT_PROFILE);
        if (profileData) {
            this.currentProfile = profileData;
        }
        
        logger.info('AppState', '用户系统数据加载完成', {
            userHash: this.currentUserHash,
            loadedCount,
            totalKeys: keys.length,
            hasProfile: !!this.currentProfile
        });
    }

    /**
     * @function saveToStorage - 保存数据到本地存储
     * @description 安全地将数据保存到localStorage
     * @param {string} key - 存储键
     * @param {any} data - 数据
     */
    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            logger.error('AppState', '数据存储失败', { key, error: error.message });
        }
    }

    /**
     * @function loadFromStorage - 从本地存储加载数据
     * @description 安全地从localStorage加载数据
     * @param {string} key - 存储键
     * @returns {any} 存储的数据
     */
    loadFromStorage(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            logger.error('AppState', '数据加载失败', { key, error: error.message });
            return null;
        }
    }

    /**
     * @function getCacheInfo - 获取缓存信息
     * @description 获取当前缓存状态的详细信息，用于调试和监控
     * @returns {object} 缓存状态信息
     */
    getCacheInfo() {
        return {
            currentUserHash: this.currentUserHash,
            userCount: this.userDataCache.size,
            currentUserData: this.currentUserHash ?
                this.userDataCache.get(this.currentUserHash) : null,
            hasLegacyData: !this.currentUserHash && (
                this.backendUsers.length > 0 ||
                this.subCategories.length > 0 ||
                this.carTypes.length > 0
            )
        };
    }

    /**
     * @function validateDataIntegrity - 验证数据完整性
     * @description 检查系统数据的完整性和有效性
     * @returns {object} 验证结果
     */
    validateDataIntegrity() {
        const requiredKeys = ['backendUsers', 'subCategories', 'carTypes'];
        const optionalKeys = ['drivingRegions', 'languages']; // 新增：可选数据类型
        const validation = {
            isValid: true,
            missingData: [],
            emptyData: [],
            optionalMissing: [],
            userHash: this.currentUserHash,
            hasProfile: !!this.currentProfile
        };

        // 验证必需数据
        requiredKeys.forEach(key => {
            const data = this.getUserSystemData(key) || this[key];
            if (!data) {
                validation.missingData.push(key);
                validation.isValid = false;
            } else if (Array.isArray(data) && data.length === 0) {
                validation.emptyData.push(key);
                validation.isValid = false;
            }
        });

        // 验证可选数据（不影响整体有效性）
        optionalKeys.forEach(key => {
            const data = this.getUserSystemData(key) || this[key];
            if (!data || (Array.isArray(data) && data.length === 0)) {
                validation.optionalMissing.push(key);
            }
        });

        return validation;
    }

    /**
     * @function getSystemDataTimestamp - 获取系统数据时间戳
     * @description 获取系统数据最后更新的时间戳
     * @returns {number} 时间戳
     */
    getSystemDataTimestamp() {
        if (!this.currentUserHash) {
            // 向后兼容：返回固定时间戳
            return this.lastDataUpdate || 0;
        }
        
        const userCache = this.userDataCache.get(this.currentUserHash);
        return userCache?.cacheTime || 0;
    }

    /**
     * @function getForceRefreshFlag - 获取强制刷新标志
     * @description 检查是否需要强制刷新数据
     * @returns {boolean} 是否需要强制刷新
     */
    getForceRefreshFlag() {
        try {
            const flag = localStorage.getItem('forceRefreshSystemData');
            return flag === 'true';
        } catch (error) {
            logger.warn('AppState', '读取强制刷新标志失败', error);
            return false;
        }
    }

    /**
     * @function clearForceRefreshFlag - 清除强制刷新标志
     * @description 清除强制刷新标志
     */
    clearForceRefreshFlag() {
        try {
            localStorage.removeItem('forceRefreshSystemData');
        } catch (error) {
            logger.warn('AppState', '清除强制刷新标志失败', error);
        }
    }

    /**
     * @function setForceRefreshFlag - 设置强制刷新标志
     * @description 设置强制刷新标志，下次加载时将强制更新数据
     */
    setForceRefreshFlag() {
        try {
            localStorage.setItem('forceRefreshSystemData', 'true');
            logger.info('AppState', '已设置强制刷新标志');
        } catch (error) {
            logger.warn('AppState', '设置强制刷新标志失败', error);
        }
    }

    /**
     * @function updateLoadingStats - 更新加载统计
     * @description 保存数据加载性能统计信息
     * @param {object} stats - 统计数据
     */
    updateLoadingStats(stats) {
        try {
            // 保存到内存
            this.loadingStats = stats;
            
            // 保存最近5次的统计记录到localStorage
            const historyKey = 'loadingStatsHistory';
            let history = this.loadFromStorage(historyKey) || [];
            
            // 添加新记录
            history.unshift(stats);
            
            // 保持最多5条记录
            if (history.length > 5) {
                history = history.slice(0, 5);
            }
            
            this.saveToStorage(historyKey, history);
            
            logger.debug('AppState', '加载统计已更新', stats);
            
        } catch (error) {
            logger.warn('AppState', '更新加载统计失败', error);
        }
    }

    /**
     * @function getLoadingStatsHistory - 获取加载统计历史
     * @description 获取历史加载性能数据
     * @returns {Array} 统计历史记录
     */
    getLoadingStatsHistory() {
        try {
            return this.loadFromStorage('loadingStatsHistory') || [];
        } catch (error) {
            logger.warn('AppState', '获取加载统计历史失败', error);
            return [];
        }
    }

    /**
     * @function getLoadingPerformanceInsights - 获取加载性能洞察
     * @description 分析加载性能数据并提供优化建议
     * @returns {object} 性能洞察
     */
    getLoadingPerformanceInsights() {
        const history = this.getLoadingStatsHistory();
        if (history.length === 0) {
            return { message: '暂无性能数据', recommendations: [] };
        }

        const avgLoadTime = history.reduce((sum, stat) => sum + stat.totalTime, 0) / history.length;
        const avgSuccessRate = history.reduce((sum, stat) => sum + stat.successRate, 0) / history.length;
        const recentFailures = history.filter(stat => stat.failureCount > 0).length;

        const insights = {
            averageLoadTime: Math.round(avgLoadTime),
            averageSuccessRate: Math.round(avgSuccessRate * 100) / 100,
            recentFailureRate: Math.round((recentFailures / history.length) * 100),
            recommendations: []
        };

        // 生成建议
        if (avgLoadTime > 8000) {
            insights.recommendations.push('加载时间较长，建议检查网络连接或考虑增加缓存策略');
        }
        if (avgSuccessRate < 95) {
            insights.recommendations.push('数据加载成功率较低，建议检查API服务稳定性');
        }
        if (recentFailures > 2) {
            insights.recommendations.push('近期多次加载失败，建议检查服务器状态');
        }
        if (insights.recommendations.length === 0) {
            insights.recommendations.push('系统性能良好，无需特别优化');
        }

        return insights;
    }

    /**
     * @function optimizeMemoryUsage - 优化内存使用
     * @description 清理过期的缓存数据以优化内存使用
     */
    optimizeMemoryUsage() {
        const now = Date.now();
        const CACHE_RETENTION = 24 * 60 * 60 * 1000; // 24小时
        let cleanedCount = 0;

        // 清理过期的用户缓存
        for (const [userHash, cacheData] of this.userDataCache) {
            if (now - cacheData.cacheTime > CACHE_RETENTION) {
                this.userDataCache.delete(userHash);
                cleanedCount++;
            }
        }

        // 清理localStorage中的过期数据
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.includes('_backendUsers') || key.includes('_subCategories') || 
                    key.includes('_carTypes') || key.includes('_drivingRegions') || 
                    key.includes('_languages')) {
                    
                    const data = this.loadFromStorage(key);
                    if (data && data.timestamp && (now - data.timestamp > CACHE_RETENTION)) {
                        localStorage.removeItem(key);
                        cleanedCount++;
                    }
                }
            });
        } catch (error) {
            logger.warn('AppState', '清理localStorage失败', error);
        }

        if (cleanedCount > 0) {
            logger.info('AppState', '内存优化完成', { 
                cleanedCount,
                remainingCacheCount: this.userDataCache.size 
            });
        }

        // 手动触发垃圾回收（如果可用）
        if (window.gc && typeof window.gc === 'function') {
            try {
                window.gc();
                logger.debug('AppState', '手动垃圾回收已触发');
            } catch (error) {
                // 忽略垃圾回收错误
            }
        }
    }
}

// 创建全局实例
window.appState = new AppState();

logger.info('模块', 'AppState模块加载完成', {
    version: 'v4.2.0',
    userHash: window.appState.currentUserHash
});
