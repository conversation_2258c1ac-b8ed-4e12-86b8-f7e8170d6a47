/**
 * @file ota-profile-manager.js - OTA Profile管理器
 * @description 管理OTA Profile配置、自动选择逻辑和邮箱映射
 * <AUTHOR> IDE
 * @created_at 2025-01-08
 * @version v4.2.0
 */

/**
 * @class OTAProfileManager - OTA Profile管理器
 * @description 管理用户配置文件和自动选择逻辑
 */
class OTAProfileManager {
    /**
     * @function constructor - 构造函数
     * @param {object} appState - 应用状态管理器
     */
    constructor(appState) {
        this.appState = appState;
        this.profiles = new Map();
        this.emailProfileMap = new Map();
        this.autoSelectionRules = new Map();
        
        // 初始化默认配置模板
        this.initializeDefaultTemplates();
        
        // 加载保存的配置
        this.loadSavedProfiles();
        
        // 初始化邮箱映射
        this.initializeEmailMappings();
        
        logger.info('OTAProfileManager', 'Profile管理器初始化完成');
    }

    /**
     * @function initializeDefaultTemplates - 初始化默认配置模板
     * @description 创建系统预设的配置模板
     */
    initializeDefaultTemplates() {
        // General Template - 通用模板
        const generalTemplate = {
            id: 'general',
            name: 'General Template',
            description: '通用多语言支持模板',
            type: 'system',
            config: {
                autoSelection: {
                    enabled: true,
                    backendUser: 'auto', // 自动选择第一个用户
                    serviceMapping: {
                        pickup: 'auto', // 自动匹配接机服务
                        dropoff: 'auto', // 自动匹配送机服务
                        charter: 'auto' // 自动匹配包车服务
                    },
                    carTypeLogic: 'smart', // 智能车型选择
                    languagePreference: ['english', 'chinese'], // 语言偏好
                    regionPreference: 'auto' // 自动区域选择
                },
                ui: {
                    theme: 'default',
                    language: 'auto',
                    showAdvanced: false
                },
                business: {
                    defaultCurrency: 'MYR',
                    timeZone: 'Asia/Kuala_Lumpur',
                    workingHours: '24/7'
                }
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // Chong Dealer Template - 中文经销商模板
        const chongDealerTemplate = {
            id: 'chong-dealer',
            name: 'Chong Dealer Template',
            description: '中文经销商专用模板',
            type: 'system',
            config: {
                autoSelection: {
                    enabled: true,
                    backendUser: 'auto',
                    serviceMapping: {
                        pickup: 'auto',
                        dropoff: 'auto',
                        charter: 'auto'
                    },
                    carTypeLogic: 'conservative', // 保守车型选择
                    languagePreference: ['chinese'], // 优先中文
                    regionPreference: 'klang-valley' // 优先雪隆地区
                },
                ui: {
                    theme: 'business',
                    language: 'chinese',
                    showAdvanced: true
                },
                business: {
                    defaultCurrency: 'MYR',
                    timeZone: 'Asia/Kuala_Lumpur',
                    workingHours: '09:00-18:00',
                    specialRules: {
                        preferLuxury: true,
                        minimumNotice: '2hours'
                    }
                }
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // 注册默认模板
        this.profiles.set('general', generalTemplate);
        this.profiles.set('chong-dealer', chongDealerTemplate);
        
        logger.info('OTAProfileManager', '默认配置模板初始化完成', {
            templates: ['general', 'chong-dealer']
        });
    }

    /**
     * @function loadSavedProfiles - 加载保存的配置
     * @description 从本地存储加载用户自定义配置
     */
    loadSavedProfiles() {
        try {
            // 加载用户配置
            const savedProfiles = localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.OTA_PROFILES);
            if (savedProfiles) {
                const profilesData = JSON.parse(savedProfiles);
                Object.entries(profilesData).forEach(([id, profile]) => {
                    this.profiles.set(id, profile);
                });
            }

            // 加载邮箱映射
            const emailMappings = localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.EMAIL_PROFILE_MAP);
            if (emailMappings) {
                const mappingData = JSON.parse(emailMappings);
                Object.entries(mappingData).forEach(([email, profileId]) => {
                    this.emailProfileMap.set(email, profileId);
                });
            }

            logger.info('OTAProfileManager', '配置加载完成', {
                totalProfiles: this.profiles.size,
                emailMappings: this.emailProfileMap.size
            });

        } catch (error) {
            logger.error('OTAProfileManager', '配置加载失败', error);
        }
    }

    /**
     * @function saveProfiles - 保存配置到本地存储
     * @description 将当前配置保存到localStorage
     */
    saveProfiles() {
        try {
            // 保存用户配置（排除系统模板）
            const userProfiles = {};
            this.profiles.forEach((profile, id) => {
                if (profile.type !== 'system') {
                    userProfiles[id] = profile;
                }
            });
            localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.OTA_PROFILES, JSON.stringify(userProfiles));

            // 保存邮箱映射
            const emailMappings = {};
            this.emailProfileMap.forEach((profileId, email) => {
                emailMappings[email] = profileId;
            });
            localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.EMAIL_PROFILE_MAP, JSON.stringify(emailMappings));

            logger.success('OTAProfileManager', '配置保存完成');

        } catch (error) {
            logger.error('OTAProfileManager', '配置保存失败', error);
        }
    }

    /**
     * @function createProfile - 创建新配置
     * @description 创建用户自定义配置
     * @param {object} profileData - 配置数据
     * @returns {string} 配置ID
     */
    createProfile(profileData) {
        const profileId = `profile_${Date.now()}`;
        const profile = {
            id: profileId,
            type: 'user',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            ...profileData
        };

        this.profiles.set(profileId, profile);
        this.saveProfiles();

        logger.success('OTAProfileManager', '新配置创建完成', { profileId, name: profile.name });
        return profileId;
    }

    /**
     * @function updateProfile - 更新配置
     * @description 更新现有配置
     * @param {string} profileId - 配置ID
     * @param {object} updates - 更新数据
     * @returns {boolean} 更新是否成功
     */
    updateProfile(profileId, updates) {
        if (!this.profiles.has(profileId)) {
            logger.error('OTAProfileManager', '配置不存在', { profileId });
            return false;
        }

        const profile = this.profiles.get(profileId);
        if (profile.type === 'system') {
            logger.error('OTAProfileManager', '系统模板不可修改', { profileId });
            return false;
        }

        const updatedProfile = {
            ...profile,
            ...updates,
            updatedAt: new Date().toISOString()
        };

        this.profiles.set(profileId, updatedProfile);
        this.saveProfiles();

        logger.success('OTAProfileManager', '配置更新完成', { profileId });
        return true;
    }

    /**
     * @function deleteProfile - 删除配置
     * @description 删除用户自定义配置
     * @param {string} profileId - 配置ID
     * @returns {boolean} 删除是否成功
     */
    deleteProfile(profileId) {
        const profile = this.profiles.get(profileId);
        if (!profile) {
            logger.error('OTAProfileManager', '配置不存在', { profileId });
            return false;
        }

        if (profile.type === 'system') {
            logger.error('OTAProfileManager', '系统模板不可删除', { profileId });
            return false;
        }

        this.profiles.delete(profileId);
        
        // 清理邮箱映射
        const emailsToRemove = [];
        this.emailProfileMap.forEach((mappedProfileId, email) => {
            if (mappedProfileId === profileId) {
                emailsToRemove.push(email);
            }
        });
        emailsToRemove.forEach(email => this.emailProfileMap.delete(email));

        this.saveProfiles();

        logger.success('OTAProfileManager', '配置删除完成', { profileId });
        return true;
    }

    /**
     * @function getProfile - 获取配置
     * @description 根据ID获取配置
     * @param {string} profileId - 配置ID
     * @returns {object|null} 配置对象
     */
    getProfile(profileId) {
        return this.profiles.get(profileId) || null;
    }

    /**
     * @function getAllProfiles - 获取所有配置
     * @description 获取所有可用配置列表
     * @returns {Array} 配置列表
     */
    getAllProfiles() {
        return Array.from(this.profiles.values());
    }

    /**
     * @function getProfileByEmail - 根据邮箱获取配置
     * @description 根据登录邮箱自动获取对应配置
     * @param {string} email - 邮箱地址
     * @returns {object|null} 配置对象
     */
    getProfileByEmail(email) {
        const profileId = this.emailProfileMap.get(email);
        if (profileId) {
            return this.getProfile(profileId);
        }
        
        // 如果没有映射，返回通用模板
        return this.getProfile('general');
    }

    /**
     * @function setEmailProfileMapping - 设置邮箱配置映射
     * @description 将邮箱与配置进行关联
     * @param {string} email - 邮箱地址
     * @param {string} profileId - 配置ID
     * @returns {boolean} 设置是否成功
     */
    setEmailProfileMapping(email, profileId) {
        if (!this.profiles.has(profileId)) {
            logger.error('OTAProfileManager', '配置不存在', { profileId });
            return false;
        }

        this.emailProfileMap.set(email, profileId);
        this.saveProfiles();

        logger.success('OTAProfileManager', '邮箱映射设置完成', { email, profileId });
        return true;
    }

    /**
     * @function removeEmailProfileMapping - 移除邮箱配置映射
     * @description 移除邮箱与配置的关联
     * @param {string} email - 邮箱地址
     * @returns {boolean} 移除是否成功
     */
    removeEmailProfileMapping(email) {
        const removed = this.emailProfileMap.delete(email);
        if (removed) {
            this.saveProfiles();
            logger.success('OTAProfileManager', '邮箱映射移除完成', { email });
        }
        return removed;
    }

    /**
     * @function applyProfile - 应用配置
     * @description 将配置应用到当前系统状态
     * @param {string} profileId - 配置ID
     * @returns {boolean} 应用是否成功
     */
    applyProfile(profileId) {
        const profile = this.getProfile(profileId);
        if (!profile) {
            logger.error('OTAProfileManager', '配置不存在', { profileId });
            return false;
        }

        // 更新应用状态
        this.appState.currentProfile = profile;
        localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.CURRENT_PROFILE, JSON.stringify(profile));

        // 应用Profile配置到智能选择服务
        this.applyProfileToSmartSelection();

        // 通知界面控制器更新Profile显示
        if (window.interfaceController) {
            window.interfaceController.updateProfileDisplay();
        }

        logger.success('OTAProfileManager', '配置应用完成', { 
            profileId, 
            name: profile.name 
        });
        
        return true;
    }

    /**
     * @function autoApplyProfileByEmail - 根据邮箱自动应用配置
     * @description 用户登录时自动应用对应配置
     * @param {string} email - 邮箱地址
     * @returns {object|null} 应用的配置
     */
    autoApplyProfileByEmail(email) {
        const profile = this.getProfileByEmail(email);
        if (profile) {
            this.applyProfile(profile.id);
            logger.info('OTAProfileManager', '自动应用配置完成', { 
                email, 
                profileId: profile.id,
                profileName: profile.name 
            });
            return profile;
        }
        
        logger.warn('OTAProfileManager', '未找到匹配配置', { email });
        return null;
    }

    /**
     * @function getAutoSelectionConfig - 获取自动选择配置
     * @description 获取当前配置的自动选择规则
     * @returns {object|null} 自动选择配置
     */
    getAutoSelectionConfig() {
        if (!this.appState.currentProfile) {
            return null;
        }
        
        return this.appState.currentProfile.config?.autoSelection || null;
    }

    /**
     * @function validateProfile - 验证配置
     * @description 验证配置数据的完整性和有效性
     * @param {object} profile - 配置对象
     * @returns {object} 验证结果
     */
    validateProfile(profile) {
        const validation = {
            isValid: true,
            errors: [],
            warnings: []
        };

        // 必需字段检查
        if (!profile.name) {
            validation.errors.push('配置名称不能为空');
            validation.isValid = false;
        }

        if (!profile.config) {
            validation.errors.push('配置内容不能为空');
            validation.isValid = false;
        }

        // 配置结构检查
        if (profile.config && !profile.config.autoSelection) {
            validation.warnings.push('缺少自动选择配置');
        }

        return validation;
    }

    /**
     * @function getCurrentProfile - 获取当前应用的Profile配置
     * @description 获取当前正在使用的Profile配置
     * @returns {object|null} 当前Profile配置
     */
    getCurrentProfile() {
        // 优先从应用状态获取
        if (this.appState.currentProfile) {
            return this.appState.currentProfile;
        }
        
        // 从本地存储获取
        try {
            const savedProfile = localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.CURRENT_PROFILE);
            if (savedProfile) {
                const profile = JSON.parse(savedProfile);
                this.appState.currentProfile = profile;
                return profile;
            }
        } catch (error) {
            logger.error('OTAProfileManager', '获取当前Profile失败', error);
        }
        
        // 返回默认通用模板
        const defaultProfile = this.getProfile('general');
        if (defaultProfile) {
            this.applyProfile('general');
            return defaultProfile;
        }
        
        return null;
    }

    /**
     * @function getProfileWeights - 获取Profile权重配置
     * @description 获取当前Profile的智能选择权重配置
     * @returns {object} 权重配置对象
     */
    getProfileWeights() {
        const currentProfile = this.getCurrentProfile();
        if (!currentProfile || !currentProfile.config) {
            return {};
        }
        
        // 根据Profile类型生成权重配置
        const weights = {};
        
        if (currentProfile.id === 'chong-dealer') {
            // Chong Dealer模板权重配置
            weights.languages = {
                4: 1.5,  // 中文权重提升
                2: 0.8,  // 英文权重降低
                3: 0.5   // 马来文权重降低
            };
            weights.drivingRegion = {
                1: 1.2,  // KL/Selangor权重提升
                2: 0.8,  // 其他区域权重降低
                3: 0.8
            };
            weights.carType = {
                15: 1.3, // 7座MPV权重提升
                20: 1.2, // 10座车权重提升
                5: 0.9   // 5座车权重略降
            };
        } else {
            // 通用模板权重配置
            weights.languages = {
                2: 1.0,  // 英文标准权重
                4: 1.0,  // 中文标准权重
                3: 0.8   // 马来文权重略低
            };
            weights.drivingRegion = {
                1: 1.0,  // 所有区域标准权重
                2: 1.0,
                3: 1.0
            };
            weights.carType = {
                5: 1.0,  // 所有车型标准权重
                15: 1.0,
                20: 1.0
            };
        }
        
        return weights;
    }

    /**
     * @function getProfileDefaults - 获取Profile默认值配置
     * @description 获取当前Profile的默认选择配置
     * @returns {object} 默认值配置对象
     */
    getProfileDefaults() {
        const currentProfile = this.getCurrentProfile();
        if (!currentProfile || !currentProfile.config) {
            return {};
        }
        
        const defaults = {};
        const autoSelection = currentProfile.config.autoSelection;
        
        if (autoSelection) {
            // 语言默认值
            if (autoSelection.languagePreference) {
                const languageMap = {
                    'english': 2,
                    'chinese': 4,
                    'malay': 3
                };
                defaults.languages = autoSelection.languagePreference.map(lang => 
                    languageMap[lang] || lang
                ).filter(id => typeof id === 'number');
            }
            
            // 区域默认值
            if (autoSelection.regionPreference && autoSelection.regionPreference !== 'auto') {
                const regionMap = {
                    'klang-valley': 1,
                    'penang': 2,
                    'johor': 3,
                    'sabah': 4,
                    'singapore': 5,
                    'malacca': 12
                };
                defaults.drivingRegion = regionMap[autoSelection.regionPreference] || 1;
            }
            
            // 车型逻辑
            if (autoSelection.carTypeLogic) {
                defaults.carTypeLogic = autoSelection.carTypeLogic;
            }
            
            // 后台用户
            if (autoSelection.backendUser && autoSelection.backendUser !== 'auto') {
                defaults.backendUser = autoSelection.backendUser;
            }
        }
        
        return defaults;
    }

    /**
     * @function applyProfileToSmartSelection - 将Profile配置应用到智能选择服务
     * @description 将当前Profile的配置同步到智能选择服务
     * @returns {boolean} 应用是否成功
     */
    applyProfileToSmartSelection() {
        try {
            if (!window.smartSelection) {
                logger.warn('OTAProfileManager', '智能选择服务不可用');
                return false;
            }
            
            const currentProfile = this.getCurrentProfile();
            if (!currentProfile) {
                logger.warn('OTAProfileManager', '无当前Profile配置');
                return false;
            }
            
            // 构建Profile配置对象
            const profileConfig = {
                templateType: currentProfile.id,
                name: currentProfile.name,
                weights: this.getProfileWeights(),
                defaults: this.getProfileDefaults(),
                rules: currentProfile.config?.business?.specialRules || {}
            };
            
            // 应用到智能选择服务
            window.smartSelection.currentProfileConfig = profileConfig;
            
            logger.success('OTAProfileManager', 'Profile配置已应用到智能选择服务', {
                profileId: currentProfile.id,
                profileName: currentProfile.name
            });
            
            return true;
            
        } catch (error) {
            logger.error('OTAProfileManager', 'Profile配置应用失败', error);
            return false;
        }
    }

    /**
     * @function initializeEmailMappings - 初始化邮箱映射
     * @description 设置默认的邮箱-Profile映射关系
     */
    initializeEmailMappings() {
        // 默认邮箱映射配置
        const defaultMappings = {
            '<EMAIL>': 'chong-dealer',
            '<EMAIL>': 'chong-dealer',
            '<EMAIL>': 'general',
            '<EMAIL>': 'general'
        };
        
        // 只设置尚未配置的映射
        Object.entries(defaultMappings).forEach(([email, profileId]) => {
            if (!this.emailProfileMap.has(email)) {
                this.emailProfileMap.set(email, profileId);
            }
        });
        
        this.saveProfiles();
        
        logger.info('OTAProfileManager', '默认邮箱映射初始化完成', {
            mappings: Object.keys(defaultMappings).length
        });
    }

    /**
     * @function createEmailOtaPresetModule - 创建登录邮箱-OTA类型预设模块
     * @description 基于用户邮箱自动设定OTA类型和相关配置
     * @param {string} userEmail - 用户邮箱
     * @returns {Object} OTA类型预设配置
     */
    createEmailOtaPresetModule(userEmail) {
        try {
            if (!userEmail) {
                logger.warn('OTAProfileManager', '用户邮箱为空，无法创建OTA预设');
                return this.getDefaultOtaPreset();
            }

            const email = userEmail.toLowerCase().trim();
            
            // 邮箱域名分析
            const domain = email.split('@')[1];
            
            // 邮箱-OTA类型映射规则
            const emailOtaMapping = {
                // 特定经销商邮箱
                '<EMAIL>': {
                    otaType: 'chong_dealer',
                    profileId: 'chong_dealer',
                    reasoning: 'CHONG经销商专用邮箱',
                    confidence: 1.0
                },
                
                // 公司内部邮箱
                'gomyhire.com.my': {
                    otaType: 'internal',
                    profileId: 'general',
                    reasoning: '公司内部邮箱域名',
                    confidence: 0.9
                },
                
                // 常见OTA平台邮箱模式
                'grab': {
                    otaType: 'grab',
                    profileId: 'general',
                    reasoning: 'Grab平台相关邮箱',
                    confidence: 0.8
                },
                
                'booking': {
                    otaType: 'booking',
                    profileId: 'general',
                    reasoning: 'Booking平台相关邮箱',
                    confidence: 0.8
                },
                
                'agoda': {
                    otaType: 'agoda',
                    profileId: 'general',
                    reasoning: 'Agoda平台相关邮箱',
                    confidence: 0.8
                }
            };
            
            // 1. 精确邮箱匹配
            if (emailOtaMapping[email]) {
                const preset = emailOtaMapping[email];
                logger.info('OTAProfileManager', '邮箱精确匹配OTA预设', {
                    email,
                    otaType: preset.otaType,
                    profileId: preset.profileId
                });
                return this.buildOtaPresetResult(preset, email);
            }
            
            // 2. 域名匹配
            if (emailOtaMapping[domain]) {
                const preset = emailOtaMapping[domain];
                logger.info('OTAProfileManager', '邮箱域名匹配OTA预设', {
                    email,
                    domain,
                    otaType: preset.otaType,
                    profileId: preset.profileId
                });
                return this.buildOtaPresetResult(preset, email);
            }
            
            // 3. 邮箱用户名关键词匹配
            const username = email.split('@')[0];
            for (const [keyword, preset] of Object.entries(emailOtaMapping)) {
                if (typeof keyword === 'string' && username.includes(keyword)) {
                    logger.info('OTAProfileManager', '邮箱用户名关键词匹配OTA预设', {
                        email,
                        keyword,
                        otaType: preset.otaType,
                        profileId: preset.profileId
                    });
                    return this.buildOtaPresetResult(preset, email);
                }
            }
            
            // 4. 默认预设
            logger.info('OTAProfileManager', '使用默认OTA预设', { email });
            return this.getDefaultOtaPreset();
            
        } catch (error) {
            logger.error('OTAProfileManager', '创建邮箱OTA预设失败', error);
            return this.getDefaultOtaPreset();
        }
    }

    /**
     * @function buildOtaPresetResult - 构建OTA预设结果
     * @description 构建完整的OTA预设配置结果
     * @param {Object} preset - 预设配置
     * @param {string} email - 用户邮箱
     * @returns {Object} 完整的预设结果
     */
    buildOtaPresetResult(preset, email) {
        const profile = this.getProfile(preset.profileId);
        
        return {
            email: email,
            otaType: preset.otaType,
            profileId: preset.profileId,
            profile: profile,
            reasoning: preset.reasoning,
            confidence: preset.confidence,
            
            // OTA类型相关配置
            otaConfig: {
                type: preset.otaType,
                defaultLanguages: profile?.defaultLanguages || [2, 4], // 英文+中文
                defaultRegion: profile?.defaultRegion || 1,             // KL/Selangor
                defaultBackendUser: profile?.defaultBackendUser || 1,   // Super Admin
                
                // 基于OTA类型的特殊配置
                specialConfig: this.getOtaSpecialConfig(preset.otaType)
            },
            
            // 应用时间戳
            appliedAt: new Date().toISOString()
        };
    }

    /**
     * @function getOtaSpecialConfig - 获取OTA类型特殊配置
     * @description 获取特定OTA类型的特殊配置
     * @param {string} otaType - OTA类型
     * @returns {Object} 特殊配置
     */
    getOtaSpecialConfig(otaType) {
        const specialConfigs = {
            'chong_dealer': {
                preferredLanguages: [4], // 仅中文
                restrictedRegions: [1],  // 仅KL/Selangor
                fixedBackendUser: 420,   // 专用后台用户
                serviceTypePreference: 7, // 机场接送优先
                carTypePreference: 'flexible' // 灵活车型选择
            },
            
            'internal': {
                preferredLanguages: [2, 4], // 英文+中文
                allRegionsAllowed: true,     // 所有区域可用
                adminAccess: true,          // 管理员权限
                serviceTypePreference: null, // 无特定偏好
                carTypePreference: 'flexible'
            },
            
            'grab': {
                preferredLanguages: [2, 3], // 英文+马来文
                preferredRegions: [1, 2],   // KL和Penang
                serviceTypePreference: 7,   // 机场接送优先
                carTypePreference: 'economy'
            },
            
            'booking': {
                preferredLanguages: [2, 4], // 英文+中文
                preferredRegions: [1, 2, 3], // 主要城市
                serviceTypePreference: 7,     // 机场接送优先
                carTypePreference: 'standard'
            },
            
            'agoda': {
                preferredLanguages: [2],    // 仅英文
                preferredRegions: [1, 2],   // KL和Penang
                serviceTypePreference: 7,   // 机场接送优先
                carTypePreference: 'standard'
            }
        };
        
        return specialConfigs[otaType] || {};
    }

    /**
     * @function getDefaultOtaPreset - 获取默认OTA预设
     * @description 获取默认的OTA类型预设配置
     * @returns {Object} 默认预设
     */
    getDefaultOtaPreset() {
        const defaultProfile = this.getProfile('general');
        
        return {
            email: 'unknown',
            otaType: 'general',
            profileId: 'general',
            profile: defaultProfile,
            reasoning: '无法匹配特定邮箱模式，使用默认通用配置',
            confidence: 0.5,
            
            otaConfig: {
                type: 'general',
                defaultLanguages: [2, 4], // 英文+中文
                defaultRegion: 1,          // KL/Selangor
                defaultBackendUser: 1,     // Super Admin
                specialConfig: {}
            },
            
            appliedAt: new Date().toISOString()
        };
    }

    /**
     * @function applyEmailOtaPreset - 应用邮箱OTA预设
     * @description 基于用户邮箱自动应用OTA预设配置
     * @param {string} userEmail - 用户邮箱
     * @returns {Object} 应用结果
     */
    applyEmailOtaPreset(userEmail) {
        try {
            // 创建邮箱OTA预设
            const otaPreset = this.createEmailOtaPresetModule(userEmail);
            
            // 应用对应的Profile
            const applyResult = this.applyProfile(otaPreset.profileId);
            
            // 保存OTA预设信息
            localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.CURRENT_OTA_PRESET, JSON.stringify(otaPreset));
            
            // 触发OTA预设应用事件
            if (window.smartSelection) {
                window.smartSelection.applyOtaPreset(otaPreset);
            }
            
            logger.success('OTAProfileManager', '邮箱OTA预设应用成功', {
                email: userEmail,
                otaType: otaPreset.otaType,
                profileId: otaPreset.profileId,
                confidence: otaPreset.confidence
            });
            
            return {
                success: true,
                otaPreset: otaPreset,
                profileApplied: applyResult,
                message: `成功应用邮箱 ${userEmail} 的OTA预设: ${otaPreset.otaType}`
            };
            
        } catch (error) {
            logger.error('OTAProfileManager', '应用邮箱OTA预设失败', error);
            return {
                success: false,
                error: error.message,
                message: '邮箱OTA预设应用失败，使用默认配置'
            };
        }
    }
}

// 创建全局实例（在appState初始化后）
if (typeof window !== 'undefined' && window.appState) {
    window.otaProfileManager = new OTAProfileManager(window.appState);
    
    logger.info('模块', 'OTAProfileManager模块加载完成', {
        version: 'v4.2.0'
    });
} 