# 技术实现细节 - techContext.md

*最后更新: 2024-12-19*

## 🏗️ 架构技术决策

### 核心技术选型

#### 前端架构
- **框架**: 原生JavaScript ES6+ (无第三方框架依赖)
- **模块系统**: ES6 Modules + IIFE封装
- **构建方式**: 无构建步骤，直接运行
- **部署方式**: 静态文件，支持file://协议

**选择理由**:
- 降低复杂度，便于维护
- 避免构建工具依赖
- 提高系统兼容性
- 减少加载时间

#### API集成策略
```javascript
// 三重API架构设计
const API_HIERARCHY = {
    primary: 'DeepSeek',      // 主要LLM服务
    backup: 'Gemini',        // 备用LLM服务  
    specialized: 'GoogleVision' // 专业图像处理
};
```

### 模块化设计原则

#### 目录结构逻辑
```
核心原则: 功能分层 + 职责分离
├── core/        - 核心业务逻辑，应用状态管理
├── services/    - 外部API集成，数据处理服务
├── components/  - 可复用UI组件
├── assets/      - 静态资源，样式文件
└── memory-bank/ - 项目文档，知识管理
```

#### 依赖关系设计
```
app.js (主控制器)
├── 依赖 config.js (配置管理)
├── 依赖 logger.js (日志服务)
├── 依赖 services/* (业务服务)
└── 调用 components/* (UI组件)

services层相互独立，避免循环依赖
```

## 🤖 AI服务集成技术

### DeepSeek AI集成
```javascript
class DeepSeekService {
    constructor() {
        this.apiUrl = 'https://api.deepseek.com/chat/completions';
        this.timeout = 30000; // 30秒超时
        this.maxRetries = 3;
    }
    
    async callAPI(prompt, options = {}) {
        const requestBody = {
            model: 'deepseek-chat',
            messages: [{ role: 'user', content: prompt }],
            temperature: 0.1,
            max_tokens: 2048
        };
        
        return await this.makeRequest(requestBody, options);
    }
}
```

### Gemini AI集成
```javascript
class GeminiService {
    constructor() {
        this.apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent';
        this.timeout = 30000;
    }
    
    async callAPI(prompt, options = {}) {
        const requestBody = {
            contents: [{
                parts: [{ text: prompt }]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 2048
            }
        };
        
        return await this.makeRequest(requestBody, options);
    }
}
```

### 智能故障切换机制
```javascript
class LLMFailoverManager {
    async processWithFailover(prompt, otaType) {
        const providers = ['deepseek', 'gemini'];
        
        for (const provider of providers) {
            try {
                const result = await this.callProvider(provider, prompt, otaType);
                if (this.isValidResponse(result)) {
                    return { provider, result, fallback: provider !== 'deepseek' };
                }
            } catch (error) {
                logger.warn('LLM', `${provider}调用失败，尝试下一个服务`, { error });
                continue;
            }
        }
        
        throw new Error('所有LLM服务均不可用');
    }
}
```

## 📊 数据处理技术

### 订单解析引擎
```javascript
class OrderParsingEngine {
    constructor() {
        this.parsers = {
            'chong-dealer': new ChongDealerParser(),
            'universal': new UniversalParser(),
            'llm-only': new LLMParser()
        };
    }
    
    async parseOrder(text, otaType = 'auto') {
        // 1. OTA类型检测
        const detectedType = this.detectOTAType(text);
        const finalType = otaType === 'auto' ? detectedType : otaType;
        
        // 2. 本地关键词匹配
        const localResult = this.parseLocalKeywords(text, finalType);
        
        // 3. LLM增强处理
        const llmResult = await this.enhanceWithLLM(text, localResult, finalType);
        
        // 4. 结果合并验证
        return this.mergeAndValidate(localResult, llmResult);
    }
}
```

### 关键词匹配技术
```javascript
// Chong Dealer关键词配置
const CHONG_DEALER_PATTERNS = {
    keywords: [
        'CHONG 车头',
        '收单&进单',
        '\\*京鱼\\*', '\\*野马\\*', '\\*小野马\\*',
        '用车地点[:：]', '用车时间[:：]',
        '举牌', '机场转乘'
    ],
    patterns: {
        flight: '[A-Z]{2}\\d{3,4}',
        time: '\\d{1,2}[:：]\\d{2}',
        date: '\\d{1,2}[月\\.]\\d{1,2}日?',
        passenger: '\\d+人|\\d+大人|\\d+位'
    },
    confidence: {
        minKeywords: 1,
        baseConfidence: 0.9
    }
};
```

### 智能选择算法
```javascript
class SmartSelectionAlgorithm {
    selectVehicleType(orderData) {
        const passengerCount = parseInt(orderData.passenger_count) || 1;
        
        // 基于人数的车型匹配算法
        const vehicleRules = [
            { maxPassengers: 4, typeId: 1, confidence: 0.9 },
            { maxPassengers: 7, typeId: 2, confidence: 0.9 },
            { maxPassengers: 10, typeId: 3, confidence: 0.8 }
        ];
        
        for (const rule of vehicleRules) {
            if (passengerCount <= rule.maxPassengers) {
                return {
                    id: rule.typeId,
                    confidence: rule.confidence,
                    reason: `基于乘客数量 ${passengerCount} 人自动选择`
                };
            }
        }
        
        // 默认大车型
        return { id: 3, confidence: 0.6, reason: '超出标准人数，选择大车型' };
    }
}
```

## 🖼️ 图像处理技术

### Google Vision OCR集成
```javascript
class VisionOCRProcessor {
    constructor() {
        this.apiUrl = 'https://vision.googleapis.com/v1/images:annotate';
        this.features = [
            { type: 'TEXT_DETECTION', maxResults: 1 },
            { type: 'DOCUMENT_TEXT_DETECTION', maxResults: 1 }
        ];
    }
    
    async extractTextFromImage(base64Image) {
        const requestBody = {
            requests: [{
                image: { content: base64Image },
                features: this.features
            }]
        };
        
        const response = await this.callVisionAPI(requestBody);
        return this.parseOCRResponse(response);
    }
    
    parseOCRResponse(response) {
        const annotations = response.responses[0];
        
        if (annotations.error) {
            throw new Error(`OCR处理失败: ${annotations.error.message}`);
        }
        
        // 提取文本内容
        const textAnnotation = annotations.fullTextAnnotation;
        return textAnnotation ? textAnnotation.text : '';
    }
}
```

### 图片预处理优化
```javascript
class ImagePreprocessor {
    async optimizeForOCR(file) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // 计算最优尺寸
                const maxDimension = 2048;
                const scale = Math.min(
                    maxDimension / img.width,
                    maxDimension / img.height,
                    1
                );
                
                canvas.width = img.width * scale;
                canvas.height = img.height * scale;
                
                // 绘制并优化
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                // 转换为JPEG格式，压缩文件大小
                canvas.toBlob(resolve, 'image/jpeg', 0.9);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }
}
```

## 💾 状态管理技术

### 应用状态设计
```javascript
class ApplicationState {
    constructor() {
        this.state = {
            // 用户认证状态
            auth: {
                token: null,
                userInfo: null,
                isLoggedIn: false
            },
            
            // 系统数据缓存
            systemData: {
                backendUsers: [],
                subCategories: [],
                carTypes: [],
                lastUpdate: null
            },
            
            // 处理中的订单数据
            currentSession: {
                inputText: '',
                processedOrders: [],
                selectedOTA: 'auto',
                processingStatus: 'idle'
            },
            
            // AI服务状态
            aiServices: {
                deepseek: { status: 'unknown', lastCheck: null },
                gemini: { status: 'unknown', lastCheck: null },
                googleVision: { status: 'unknown', lastCheck: null }
            }
        };
    }
    
    // 状态更新方法
    updateState(path, value) {
        const keys = path.split('.');
        let current = this.state;
        
        for (let i = 0; i < keys.length - 1; i++) {
            current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = value;
        this.notifyStateChange(path, value);
    }
}
```

### 本地存储策略
```javascript
class StorageManager {
    constructor() {
        this.storageKeys = {
            TOKEN: 'ota_auth_token',
            USER_INFO: 'ota_user_info',
            SYSTEM_DATA: 'ota_system_data',
            PREFERENCES: 'ota_user_preferences'
        };
    }
    
    saveToStorage(key, data) {
        try {
            const serialized = JSON.stringify(data);
            localStorage.setItem(key, serialized);
        } catch (error) {
            logger.error('Storage', '数据保存失败', { key, error });
        }
    }
    
    loadFromStorage(key, defaultValue = null) {
        try {
            const stored = localStorage.getItem(key);
            return stored ? JSON.parse(stored) : defaultValue;
        } catch (error) {
            logger.error('Storage', '数据读取失败', { key, error });
            return defaultValue;
        }
    }
}
```

## 🔍 日志监控技术

### 分层日志设计
```javascript
class AdvancedLogger {
    constructor() {
        this.levels = {
            DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3, SUCCESS: 4
        };
        this.logBuffer = [];
        this.maxBufferSize = 1000;
    }
    
    log(level, module, message, data = null) {
        const entry = {
            timestamp: new Date(),
            level,
            module,
            message,
            data: this.sanitizeData(data),
            id: this.generateLogId()
        };
        
        this.addToBuffer(entry);
        this.displayLog(entry);
        this.outputToConsole(entry);
    }
    
    // API调用专用日志
    logApiCall(method, url, requestData, responseData, timing) {
        this.log('INFO', 'API', `${method} ${url}`, {
            type: 'api_call',
            request: requestData,
            response: responseData,
            timing: timing,
            status: responseData?.status || 'unknown'
        });
    }
}
```

### 性能监控技术
```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.thresholds = {
            apiCall: 30000,    // 30秒
            imageProcess: 15000, // 15秒
            uiResponse: 1000    // 1秒
        };
    }
    
    startTiming(operation) {
        const startTime = performance.now();
        this.metrics.set(operation, { startTime });
        
        return () => {
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            this.recordMetric(operation, duration);
            this.checkThreshold(operation, duration);
            
            return duration;
        };
    }
    
    recordMetric(operation, duration) {
        logger.debug('Performance', `${operation}操作耗时`, {
            operation,
            duration: `${duration.toFixed(2)}ms`,
            threshold: this.thresholds[operation] || 'N/A'
        });
    }
}
```

## 🔒 安全技术实现

### 输入验证技术
```javascript
class SecurityValidator {
    static validateOrderInput(orderData) {
        const rules = {
            customer_name: /^[\u4e00-\u9fa5\w\s]{1,50}$/,
            customer_contact: /^[+\d\-\s()]{1,20}$/,
            customer_email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            service_date: /^\d{4}-\d{2}-\d{2}$/,
            service_time: /^\d{2}:\d{2}$/
        };
        
        const errors = [];
        
        for (const [field, pattern] of Object.entries(rules)) {
            const value = orderData[field];
            if (value && !pattern.test(value)) {
                errors.push(`${field}格式不正确`);
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    
    static sanitizeInput(input) {
        if (typeof input !== 'string') return input;
        
        return input
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+=/gi, '');
    }
}
```

### API请求安全
```javascript
class SecureApiClient {
    constructor() {
        this.rateLimiter = new Map();
        this.maxRequestsPerMinute = 60;
    }
    
    async makeSecureRequest(url, options) {
        // 频率限制检查
        if (!this.checkRateLimit(url)) {
            throw new Error('请求频率过高，请稍后再试');
        }
        
        // 请求签名（如果需要）
        const secureOptions = this.addSecurityHeaders(options);
        
        // 执行请求
        const response = await fetch(url, secureOptions);
        
        // 响应验证
        return this.validateResponse(response);
    }
    
    checkRateLimit(url) {
        const now = Date.now();
        const minute = Math.floor(now / 60000);
        const key = `${url}-${minute}`;
        
        const count = this.rateLimiter.get(key) || 0;
        if (count >= this.maxRequestsPerMinute) {
            return false;
        }
        
        this.rateLimiter.set(key, count + 1);
        return true;
    }
}
```

## 🔧 配置管理技术

### 环境配置策略
```javascript
class ConfigurationManager {
    constructor() {
        this.environment = this.detectEnvironment();
        this.config = this.loadConfiguration();
    }
    
    detectEnvironment() {
        const hostname = window.location.hostname;
        
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'development';
        } else if (hostname.includes('staging')) {
            return 'staging';
        } else {
            return 'production';
        }
    }
    
    loadConfiguration() {
        const baseConfig = {
            development: {
                apiTimeout: 60000,
                logLevel: 'DEBUG',
                enableDetailedLogging: true
            },
            staging: {
                apiTimeout: 30000,
                logLevel: 'INFO',
                enableDetailedLogging: true
            },
            production: {
                apiTimeout: 30000,
                logLevel: 'WARN',
                enableDetailedLogging: false
            }
        };
        
        return {
            ...baseConfig.production,
            ...baseConfig[this.environment]
        };
    }
}
```

---

## 📋 技术债务记录

### 当前技术债务
1. **app.js文件过大**: 需要进一步模块化拆分
2. **缓存策略不够智能**: 需要实现LRU缓存
3. **错误处理不够统一**: 需要统一的错误处理抽象
4. **性能监控不够完善**: 需要更详细的性能指标

### 技术改进计划
1. **引入TypeScript**: 提高类型安全性
2. **实现Service Worker**: 支持离线功能
3. **优化打包策略**: 减少文件大小
4. **增加自动化测试**: 提高代码质量

---

*技术栈版本记录: ES6+, Chrome 90+, Firefox 88+, Safari 14+* 