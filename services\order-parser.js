/**
 * @file order-parser.js - 订单解析服务
 * @description 整合本地关键词检测和LLM处理的订单解析服务
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

/**
 * @class OrderParser - 订单解析服务类
 * @description 提供统一的订单解析接口，支持本地检测和LLM处理
 */
class OrderParser {
    /**
     * @function constructor - 构造函数
     * @param {LLMService} llmService - LLM服务实例（可选）
     */
    constructor(llmService = null) {
        this.llmService = llmService || new LLMService();
        this.otaDetectionRules = SYSTEM_CONFIG.OTA_TYPES;
    }

    /**
     * @function parseOrders - 解析订单文本
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型（可选，默认自动检测）
     * @returns {Promise<object>} 解析结果
     */
    async parseOrders(text, otaType = 'auto') {
        const startTime = Date.now();
        
        logger.info('订单解析', '开始解析订单', {
            textLength: text.length,
            specifiedOtaType: otaType
        });

        try {
            // 1. OTA类型检测（如果未指定）
            let detectedOtaType = otaType;
            if (otaType === 'auto') {
                detectedOtaType = this.detectOtaType(text);
                logger.info('订单解析', 'OTA类型检测完成', {
                    detectedType: detectedOtaType,
                    confidence: this.getDetectionConfidence(text, detectedOtaType)
                });
            }

            // 2. 统一使用LLM处理所有订单类型
            let parseResult;
            if (detectedOtaType === 'other') {
                // 完全没有识别到任何OTA关键词时，使用通用回退模板
                parseResult = await this.parseLLMOrders(text, 'fallback');
            } else {
                // 所有已知OTA类型统一使用LLM处理
                parseResult = await this.parseLLMOrders(text, detectedOtaType);
            }

            const processingTime = Date.now() - startTime;

            logger.success('订单解析', '订单解析完成', {
                otaType: detectedOtaType,
                orderCount: parseResult.orders?.length || 0,
                processingTime: `${processingTime}ms`,
                success: parseResult.success
            });

            return {
                success: parseResult.success,
                orders: parseResult.orders || [],
                otaType: detectedOtaType,
                processingTime: processingTime,
                metadata: {
                    originalText: text,
                    detectionMethod: 'llm',
                    ...parseResult.metadata
                }
            };

        } catch (error) {
            const processingTime = Date.now() - startTime;
            
            logger.error('订单解析', '订单解析失败', {
                error: error.message,
                processingTime: `${processingTime}ms`
            });

            return {
                success: false,
                error: error.message,
                orders: [],
                processingTime: processingTime
            };
        }
    }

    /**
     * @function detectOtaType - 检测OTA类型
     * @param {string} text - 订单文本
     * @returns {string} 检测到的OTA类型
     */
    detectOtaType(text) {
        const textLower = text.toLowerCase();
        
        // 检查Chong Dealer关键词
        const chongKeywords = this.otaDetectionRules['chong-dealer'].keywordPatterns;
        let chongMatches = 0;
        
        for (const pattern of chongKeywords) {
            const regex = new RegExp(pattern, 'i');
            if (regex.test(text)) {
                chongMatches++;
            }
        }

        // 如果匹配到足够的Chong Dealer关键词
        if (chongMatches >= this.otaDetectionRules['chong-dealer'].minimumMatches) {
            return 'chong-dealer';
        }

        // 检查通用回退模板关键词
        if (this.otaDetectionRules['fallback']) {
            const fallbackKeywords = this.otaDetectionRules['fallback'].keywordPatterns;
            let fallbackMatches = 0;

            for (const pattern of fallbackKeywords) {
                const regex = new RegExp(pattern, 'i');
                if (regex.test(text)) {
                    fallbackMatches++;
                }
            }

            // 如果匹配到足够的通用关键词，使用通用回退模板
            if (fallbackMatches >= this.otaDetectionRules['fallback'].minimumMatches) {
                return 'fallback';
            }
        }

        // 检查其他OTA类型的关键词
        for (const [otaType, config] of Object.entries(this.otaDetectionRules)) {
            if (otaType === 'chong-dealer' || otaType === 'fallback' || !config.keywordPatterns) continue;

            let matches = 0;
            for (const pattern of config.keywordPatterns) {
                const regex = new RegExp(pattern, 'i');
                if (regex.test(text)) {
                    matches++;
                }
            }

            if (matches >= (config.minimumMatches || 1)) {
                return otaType;
            }
        }

        // 如果没有匹配到任何特定类型，返回'other'表示无法识别
        // 只有在完全没有识别到任何OTA关键词时才使用通用模板
        return 'other';
    }

    /**
     * @function getDetectionConfidence - 获取检测置信度
     * @param {string} text - 订单文本
     * @param {string} otaType - 检测到的OTA类型
     * @returns {number} 置信度（0-1）
     */
    getDetectionConfidence(text, otaType) {
        const config = this.otaDetectionRules[otaType];
        if (!config || !config.keywordPatterns) return 0.5;

        let matches = 0;
        for (const pattern of config.keywordPatterns) {
            const regex = new RegExp(pattern, 'i');
            if (regex.test(text)) {
                matches++;
            }
        }

        return Math.min(matches / config.keywordPatterns.length, 1.0);
    }



    /**
     * @function parseLLMOrders - 使用LLM解析订单
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Promise<object>} 解析结果
     */
    async parseLLMOrders(text, otaType) {
        logger.info('订单解析', '使用LLM解析订单', { otaType });

        try {
            const llmResult = await this.llmService.processOrderText(text, otaType);
            
            if (llmResult.success) {
                let orders = llmResult.data.orders || [];
                
                // 应用智能选择（如果服务可用）
                if (window.smartSelection && window.smartSelection.initialized) {
                    try {
                        orders = orders.map(order => {
                            const enhancedOrder = window.smartSelection.applySmartSelection(order, 'llm');
                            logger.debug('订单解析', '智能选择已应用到LLM解析结果', {
                                originalOrder: {
                                    car_type_id: order.car_type_id,
                                    sub_category_id: order.sub_category_id,
                                    incharge_by_backend_user_id: order.incharge_by_backend_user_id
                                },
                                enhancedOrder: {
                                    car_type_id: enhancedOrder.car_type_id,
                                    sub_category_id: enhancedOrder.sub_category_id,
                                    incharge_by_backend_user_id: enhancedOrder.incharge_by_backend_user_id
                                }
                            });
                            return { ...order, ...enhancedOrder };
                        });
                    } catch (error) {
                        logger.error('订单解析', '智能选择应用失败', error);
                    }
                }
                
                return {
                    success: true,
                    orders: orders,
                    metadata: {
                        llmProvider: llmResult.provider,
                        processingTime: llmResult.processingTime,
                        rawResponse: llmResult.data.rawContent,
                        smartSelectionApplied: window.smartSelection && window.smartSelection.initialized
                    }
                };
            } else {
                return {
                    success: false,
                    error: llmResult.error,
                    orders: []
                };
            }

        } catch (error) {
            logger.error('订单解析', 'LLM解析失败', { error: error.message });
            return {
                success: false,
                error: error.message,
                orders: []
            };
        }
    }

    /**
     * @function optimizedBatchProcessing - LLM优化：异步批处理订单
     * @param {Array} orderTexts - 订单文本数组
     * @param {string} otaType - OTA类型
     * @param {number} batchSize - 批处理大小，默认5
     * @returns {Promise<Array>} 批处理结果数组
     */
    async optimizedBatchProcessing(orderTexts, otaType = 'auto', batchSize = 5) {
        const startTime = Date.now();
        
        logger.info('订单解析', '开始批处理订单', {
            totalOrders: orderTexts.length,
            batchSize: batchSize,
            otaType: otaType
        });

        const results = [];
        const batches = [];
        
        // 将订单分组为批次
        for (let i = 0; i < orderTexts.length; i += batchSize) {
            batches.push(orderTexts.slice(i, i + batchSize));
        }

        try {
            // 并行处理每个批次
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                
                logger.info('订单解析', `处理批次 ${batchIndex + 1}/${batches.length}`, {
                    batchSize: batch.length
                });

                // 并行处理当前批次中的所有订单
                const batchPromises = batch.map(async (text, index) => {
                    try {
                        const result = await this.parseOrders(text, otaType);
                        return {
                            index: batchIndex * batchSize + index,
                            success: true,
                            result: result
                        };
                    } catch (error) {
                        logger.error('订单解析', `批次订单处理失败`, {
                            batchIndex: batchIndex,
                            orderIndex: index,
                            error: error.message
                        });
                        return {
                            index: batchIndex * batchSize + index,
                            success: false,
                            error: error.message
                        };
                    }
                });

                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);

                // 批次间添加短暂延迟，避免API限流
                if (batchIndex < batches.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            const processingTime = Date.now() - startTime;
            const successCount = results.filter(r => r.success).length;
            
            logger.success('订单解析', '批处理完成', {
                totalOrders: orderTexts.length,
                successCount: successCount,
                failureCount: results.length - successCount,
                processingTime: `${processingTime}ms`,
                avgTimePerOrder: `${Math.round(processingTime / orderTexts.length)}ms`
            });

            return results;

        } catch (error) {
            logger.error('订单解析', '批处理失败', {
                error: error.message,
                processedBatches: results.length
            });
            throw error;
        }
    }





    /**
     * @function validateOrder - 验证订单信息
     * @param {object} order - 订单对象
     * @returns {object} 验证结果
     */
    validateOrder(order) {
        const errors = [];
        const warnings = [];

        // 必填字段检查
        if (!order.customerName && !order.flightNumber) {
            errors.push('缺少客人姓名或航班号');
        }

        if (!order.serviceType) {
            warnings.push('未指定服务类型');
        }

        if (!order.serviceDate) {
            warnings.push('未指定服务日期');
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    }








}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OrderParser;
} else if (typeof window !== 'undefined') {
    window.OrderParser = OrderParser;
}
