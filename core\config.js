/**
 * @file config.js - 系统统一配置文件
 * @description 整合所有配置信息，提供统一的配置管理
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2025-01-08
 * @version v4.2.0
 */

// 系统配置
const SYSTEM_CONFIG = {
    // 系统信息
    SYSTEM_INFO: {
        name: 'OTA订单处理系统',
        version: '4.2.0',
        description: '基于五维智能选择的订单解析系统',
        lastUpdated: '2025-01-08'
    },

    // API配置 - 统一到LIVE环境
    API: {
        // GoMyHire API基础URL - 统一使用LIVE环境
        BASE_URL: 'https://gomyhire.com.my/api',
        
        // API端点配置
        ENDPOINTS: {
            login: '/login',
            backend_users: '/backend_users',
            sub_category: '/sub_category',
            car_types: '/car_types',
            driving_regions: '/driving_regions',  // 新增：行驶区域端点
            languages: '/languages',              // 新增：语言端点
            create_order: '/create_order'
        },
        
        // Gemini AI API配置（主要LLM）
        GEMINI: {
            // 请替换为您的实际Gemini API Key
            API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
            API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent',
            MODEL_CONFIG: {
                temperature: 0.1,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 2048
            },
            TIMEOUT: 30000,
            MAX_RETRIES: 1,
            PRIORITY: 2
        },

        // Google Vision API配置（图片分析专用）
        GOOGLE_VISION: {
            API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
            API_URL: 'https://vision.googleapis.com/v1/images:annotate',
            TIMEOUT: 15000
        },

        // Google Maps Places API配置（地址搜索专用）
        GOOGLE_MAPS: {
            API_KEY: 'AIzaSyBsBoEOqYR2_Y3UZ5e-5k7h8MZTvZ7XbUE',
            PLACES_API_URL: 'https://maps.googleapis.com/maps/api/place',
            AUTOCOMPLETE_URL: 'https://maps.googleapis.com/maps/api/place/autocomplete/json',
            PLACE_DETAILS_URL: 'https://maps.googleapis.com/maps/api/place/details/json',
            GEOCODING_API_URL: 'https://maps.googleapis.com/maps/api/geocode/json',
            SEARCH_CONFIG: {
                components: 'country:my',
                language: 'zh-CN',
                types: 'establishment'
            },
            PLACE_FIELDS: ['place_id', 'formatted_address', 'name', 'geometry'],
            MAX_RESULTS: 5,
            TIMEOUT: 10000,
            DEBOUNCE_DELAY: 300
        }
    },
    
    // 本地存储键名 - 扩展支持新数据类型
    STORAGE_KEYS: {
        TOKEN: 'ota_system_token',
        USER_INFO: 'ota_system_user',
        BACKEND_USERS: 'ota_backend_users',
        SUB_CATEGORIES: 'ota_sub_categories',
        CAR_TYPES: 'ota_car_types',
        DRIVING_REGIONS: 'ota_driving_regions',    // 新增：行驶区域缓存键
        LANGUAGES: 'ota_languages',                // 新增：语言缓存键
        CURRENT_PROFILE: 'ota_current_profile',    // 新增：当前Profile缓存键
        LAST_LOGIN: 'ota_last_login'
    },
    
    // 默认登录信息
    DEFAULT_LOGIN: {
        email: '<EMAIL>',
        password: '1234'
    },
    
    // OTA类型配置
    OTA_TYPES: {
        'chong-dealer': {
            name: 'Chong Dealer',
            keywordPatterns: [
                'CHONG 车头', '收单&进单', '\\*京鱼\\*', '\\*野马\\*',
                '用车地点[:：]', '用车时间[:：]', '客人姓名[:：]',
                '接机[:：]?\\s*[A-Z]{1,3}\\d{2,4}', '\\d{1,2}[:：]\\d{2}抵达'
            ],
            minimumMatches: 2,
            confidence: 0.8
        },
        'fallback': {
            name: '通用模板',
            keywordPatterns: [
                '[A-Z]{1,3}\\d{2,4}', '\\d{1,2}[:：]\\d{2}',
                '接机|送机|包车', '\\d+人|\\d+大人', '航班[:：]'
            ],
            minimumMatches: 2,
            confidence: 0.6
        },
        'auto': { name: '自动识别' },
        'other': { name: '其他' }
    },

    // OTA Profile 配置 - 新增
    OTA_PROFILES: {
        GENERAL: {
            id: 'general',
            name: '通用模板',
            defaultLanguages: [2, 4], // English, Chinese
            defaultRegion: 1, // KL/Selangor
            defaultCarTypeRules: {
                '1-2': 5,  // 1-2人默认5座车
                '3-4': 15, // 3-4人默认7座MPV
                '5+': 20   // 5人以上默认10座车
            },
            defaultBackendUser: 1,
            serviceTypePreferences: {
                'pickup': 2,
                'dropoff': 3,
                'charter': 4
            }
        },
        CHONG_DEALER: {
            id: 'chong-dealer',
            name: 'Chong Dealer 模板',
            defaultLanguages: [4], // Chinese only
            defaultRegion: 1, // KL/Selangor
            defaultCarTypeRules: {
                '1-3': 5,  // 1-3人默认5座车
                '4-6': 15, // 4-6人默认7座MPV
                '7+': 20   // 7人以上默认10座车
            },
            defaultBackendUser: 420, // chongyoonlim
            serviceTypePreferences: {
                'pickup': 2,
                'charter': 4
            },
            specialRules: {
                autoGenerateReference: true,
                preferredTimeFormat: '24h',
                defaultMeetAndGreet: true
            }
        }
    },

    // 账号-Profile 映射 - 新增
    PROFILE_MAPPING: {
        '<EMAIL>': 'chong-dealer',
        '<EMAIL>': 'chong-dealer',
        'default': 'general'
    },

    // 智能选择规则 - 扩展支持五维选择
    SMART_SELECTION: {
        carTypeByPassengerCount: {
            '1': 1, '2': 1, '3': 1, '4': 1, '5': 2, '6': 2, '7': 2, '8': 3, '9': 3, '10': 3
        },
        subCategoryByServiceType: {
            '接机': 1, '送机': 2, '包车': 3, '点对点': 4, '机场转乘': 5
        },
        backendUserByOta: { 'default': 1 },
        // 新增：区域映射规则
        regionByLocation: {
            'kuala lumpur': 1, 'kl': 1, 'selangor': 1, 'petaling jaya': 1,
            'penang': 2, 'georgetown': 2, 'butterworth': 2,
            'johor': 3, 'johor bahru': 3, 'jb': 3,
            'sabah': 4, 'kota kinabalu': 4,
            'singapore': 5, 'sg': 5
        },
        // 新增：语言识别规则
        languageByPattern: {
            chinese: { pattern: /[\u4e00-\u9fff]/, id: 4 },
            malay: { pattern: /(bin |binti |ahmad|muhammad)/i, id: 3 },
            english: { pattern: /^[a-zA-Z\s]+$/, id: 2 }
        }
    },

    // 文件上传配置
    UPLOAD: {
        MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
        ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    },
    


    // 错误处理配置
    ERROR_HANDLING: {
        // 统一错误消息模板
        ERROR_MESSAGES: {
            // 网络相关错误
            NETWORK_ERROR: '网络连接失败，请检查网络连接',
            TIMEOUT_ERROR: '请求超时，请稍后重试',
            CONNECTION_ERROR: '连接服务器失败，请检查网络',
            
            // 认证相关错误
            AUTH_FAILED: '登录失败，请检查用户名和密码',
            TOKEN_EXPIRED: '登录已过期，请重新登录',
            UNAUTHORIZED: '认证失败，请重新登录',
            
            // 权限相关错误
            PERMISSION_DENIED: '权限不足，请联系管理员',
            FORBIDDEN: '访问被拒绝，请联系管理员',
            
            // 数据相关错误
            VALIDATION_ERROR: '数据验证失败，请检查输入信息',
            DATA_NOT_FOUND: '请求的数据不存在，请刷新后重试',
            INVALID_DATA: '数据格式错误，请检查输入',
            
            // 服务器相关错误
            SERVER_ERROR: '服务器错误，请稍后重试',
            SERVICE_UNAVAILABLE: '服务暂时不可用，请稍后重试',
            
            // 业务相关错误
            ORDER_CREATE_FAILED: '订单创建失败',
            ORDER_PARSE_FAILED: '订单解析失败，请检查订单格式',
            LLM_SERVICE_ERROR: 'AI服务暂时不可用，请稍后重试',
            
            // 通用错误
            UNKNOWN_ERROR: '操作失败，请稍后重试',
            OPERATION_FAILED: '操作失败'
        },
        
        // 错误码映射
        STATUS_CODE_MAPPING: {
            400: 'INVALID_DATA',
            401: 'UNAUTHORIZED', 
            403: 'FORBIDDEN',
            404: 'DATA_NOT_FOUND',
            422: 'VALIDATION_ERROR',
            500: 'SERVER_ERROR',
            502: 'SERVICE_UNAVAILABLE',
            503: 'SERVICE_UNAVAILABLE',
            504: 'TIMEOUT_ERROR'
        },
        
        // 重试配置
        RETRY_CONFIG: {
            maxAttempts: 3,
            baseDelay: 1000,
            maxDelay: 5000
        }
    },

    // 缓存配置
    CACHE: {
        enabled: true,
        maxSize: 100,
        ttl: 5 * 60 * 1000 // 5分钟TTL
    },

    // 系统设置
    SYSTEM: {
        AUTO_SAVE_INTERVAL: 30000, // 30秒自动保存
        SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24小时
        MAX_LOG_ENTRIES: 1000,
        PRODUCTION_MODE: false // 生产环境设置为true
    }
};

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SYSTEM_CONFIG;
}

// 导出配置（Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SYSTEM_CONFIG;
}

// 全局配置（浏览器环境）
if (typeof window !== 'undefined') {
    window.SYSTEM_CONFIG = SYSTEM_CONFIG;
}
