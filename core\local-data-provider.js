/**
 * @file local-data-provider.js - 本地数据提供器
 * @description 提供本地API数据，跳过登录环节直接使用预设数据
 * <AUTHOR> IDE
 * @created_at 2025-01-08
 * @version v1.0.0
 */

/**
 * @class LocalDataProvider - 本地数据提供器
 * @description 提供本地API数据，支持跳过登录直接使用系统
 */
class LocalDataProvider {
    /**
     * @function constructor - 构造函数
     * @description 初始化本地数据提供器
     */
    constructor() {
        this.isEnabled = false;
        this.data = this.initializeLocalData();
        
        logger.info('本地数据', '本地数据提供器初始化完成');
    }

    /**
     * @function initializeLocalData - 初始化本地数据
     * @description 从API返回ID列表中提取数据并格式化
     * @returns {object} 格式化的本地数据
     */
    initializeLocalData() {
        return {
            // 后台用户数据
            backendUsers: [
                { id: 1, name: 'Super Admin', email: '', phone: '', role_id: 'super admin' },
                { id: 37, name: 'smw', email: '', phone: '', role_id: 'operator' },
                { id: 89, name: 'GMH Sabah', email: '<EMAIL>', phone: '', role_id: 'operator' },
                { id: 310, name: 'Jcy', email: '<EMAIL>', phone: '', role_id: 'operator' },
                { id: 311, name: 'opAnnie', email: '<EMAIL>', phone: '', role_id: 'operator' },
                { id: 312, name: 'opVenus', email: '<EMAIL>', phone: '', role_id: 'operator' },
                { id: 313, name: 'opEric', email: '', phone: '', role_id: 'operator' },
                { id: 342, name: 'SMW Wendy', email: 'SMW <EMAIL>', phone: '', role_id: 'operator' },
                { id: 343, name: 'SMW XiaoYu', email: 'SMW <EMAIL>', phone: '', role_id: 'operator' },
                { id: 420, name: 'chongyoonlim', email: '', phone: '', role_id: 'operator' },
                { id: 421, name: 'josua', email: '<EMAIL>', phone: '', role_id: 'operator' },
                { id: 428, name: 'Gomyhire Yong', email: '', phone: '', role_id: 'operator' },
                { id: 533, name: 'xhs', email: '', phone: '', role_id: 'operator' },
                { id: 622, name: 'CsBob', email: '', phone: '', role_id: 'operator' },
                { id: 777, name: '空空', email: '空空@gomyhire.com', phone: '', role_id: 'operator' },
                { id: 812, name: '淼淼', email: '', phone: '', role_id: 'operator' },
                { id: 856, name: 'GMH Ashley', email: '', phone: '', role_id: 'operator' },
                { id: 907, name: 'OP XINYIN', email: '', phone: '', role_id: 'operator' },
                { id: 1043, name: 'Billy Yong close', email: '', phone: '', role_id: 'operator' },
                { id: 1047, name: 'OP QiJun', email: 'OP <EMAIL>', phone: '', role_id: 'operator' },
                { id: 1181, name: 'Op Karen', email: 'Op <EMAIL>', phone: '', role_id: 'operator' },
                { id: 1201, name: 'KK Lucas', email: '', phone: '', role_id: 'operator' },
                { id: 1223, name: 'Chong admin', email: '', phone: '', role_id: 'admin' },
                { id: 1652, name: 'CSteam Swee Qing', email: '<NAME_EMAIL>', phone: '', role_id: 'operator' },
                { id: 1832, name: 'GMH SG William', email: '', phone: '', role_id: 'operator' },
                { id: 2050, name: 'agent victor', email: '', phone: '', role_id: 'operator' },
                { id: 2085, name: 'CSteam Tze Ying', email: '', phone: '', role_id: 'operator' },
                { id: 2141, name: 'SMW Nas', email: 'SMW <EMAIL>', phone: '', role_id: 'operator' },
                { id: 2142, name: 'SMW Wen', email: 'SMW <EMAIL>', phone: '', role_id: 'operator' },
                { id: 2248, name: 'GMH Shi Wei', email: '', phone: '', role_id: 'operator' },
                { id: 2249, name: 'Skymirror jetty', email: 'Skymirror <EMAIL>', phone: '', role_id: 'operator' },
                { id: 2340, name: 'GMH JingSoon', email: '', phone: '', role_id: 'operator' },
                { id: 2358, name: 'GMH Zilok', email: 'GMH <EMAIL>', phone: '', role_id: 'operator' },
                { id: 2446, name: 'UCSI - Cheras', email: '', phone: '', role_id: 'operator' },
                { id: 2503, name: 'GMH Veron', email: 'GMH <EMAIL>', phone: '', role_id: 'operator' }
            ],

            // 子分类数据
            subCategories: [
                { id: 2, name: 'Pickup', category: { name: '接机服务' } },
                { id: 3, name: 'Dropoff', category: { name: '送机服务' } },
                { id: 4, name: 'Charter', category: { name: '包车服务' } },
                { id: 5, name: 'Paging', category: { name: '寻呼服务' } },
                { id: 6, name: 'SIM Card Only', category: { name: 'SIM卡服务' } },
                { id: 7, name: 'Paging + Sim', category: { name: '寻呼+SIM' } },
                { id: 8, name: '73494 (KLK) Putrajaya + Melaka', category: { name: '包车游览' } },
                { id: 9, name: '73496 (KLK) Skymirror 6 H', category: { name: '天空之镜' } },
                { id: 10, name: '73496 (KLK) Skymirror + 3act 12H', category: { name: '天空之镜+三景点' } },
                { id: 11, name: '73496 (KLK) Sky + Sekinchan + 3act', category: { name: '天空之镜+适耕庄' } },
                { id: 12, name: '73496 (KLK) 3act 6 H', category: { name: '三景点游览' } },
                { id: 13, name: '73496 (KLK) Skymirror + KL 10 hour', category: { name: '天空之镜+吉隆坡' } },
                { id: 14, name: '73496 (KLK) Skymirror + Sekinchan 10 H', category: { name: '天空之镜+适耕庄' } },
                { id: 15, name: '115623 (KLK) Ipoh 12 H', category: { name: '怡保一日游' } },
                { id: 16, name: '133629(KLK) Bongawan 6H', category: { name: '邦咯威游览' } },
                { id: 17, name: '119183(KLK) Desa Farm 10H', category: { name: '德萨农场' } },
                { id: 18, name: '116204(KLK) Kota Kinabalu City 5H', category: { name: '亚庇市区游' } },
                { id: 20, name: '110544(KLK) Hourly Tour', category: { name: '按时包车' } },
                { id: 21, name: '99412(KLK) Genting Shuttle (Shared)', category: { name: '云顶拼车' } },
                { id: 22, name: '99412(KLK) Genting Shuttle Private', category: { name: '云顶专车' } },
                { id: 23, name: '81288(KLK) Hatyai transfer (Penang)', category: { name: '合艾接送' } },
                { id: 24, name: '155609 (KK) Kuala Lumpur City 21 Attractions Sightseeing Tour | Malaysia', category: { name: '吉隆坡21景点' } },
                { id: 25, name: '包车（商铺）', category: { name: '商铺包车' } }
            ],

            // 车型数据
            carTypes: [
                { id: 38, name: '4 Seater Hatchback (3 passenger, 2 x L size luggage)', capacity: 3, luggage: 2 },
                { id: 5, name: '5 Seater (3 passenger, 3 x L size luggage)', capacity: 3, luggage: 3 },
                { id: 33, name: 'Premium 5 Seater (Mercedes/BMW Only) (3 passenger, 3 x L size luggage)', capacity: 3, luggage: 3 },
                { id: 37, name: 'Extended 5 (4 passenger, 4 x L size luggage)', capacity: 4, luggage: 4 },
                { id: 35, name: '7 Seater SUV (4 passenger, 4 x L size luggage)', capacity: 4, luggage: 4 },
                { id: 15, name: '7 Seater MPV (5 passenger, 4 x L size luggage)', capacity: 5, luggage: 4 },
                { id: 16, name: 'Standard Size MPV (5 passenger, 4 x L size luggage)', capacity: 5, luggage: 4 },
                { id: 31, name: 'Luxury Mpv (Serena) (5 passenger, 4 x L size luggage)', capacity: 5, luggage: 4 },
                { id: 32, name: 'Velfire/ Alphard (6 passenger, 4 x L size luggage)', capacity: 6, luggage: 4 },
                { id: 36, name: 'Alphard (6 passenger, 4 x L size luggage)', capacity: 6, luggage: 4 },
                { id: 20, name: '10 Seater MPV / Van (7 passenger, 7 x L size luggage)', capacity: 7, luggage: 7 },
                { id: 30, name: '12 seat Starex (7 passenger, 7 x L size luggage)', capacity: 7, luggage: 7 },
                { id: 23, name: '14 Seater Van (10 passenger, 10 x L size luggage)', capacity: 10, luggage: 10 },
                { id: 24, name: '18 Seater Van (12 passenger, 12 x L size luggage)', capacity: 12, luggage: 12 },
                { id: 25, name: '30 Seat Mni Bus (29 passenger, 29 x L size luggage)', capacity: 29, luggage: 29 },
                { id: 26, name: '44 Seater Bus (43 passenger, 43 x L size luggage)', capacity: 43, luggage: 43 },
                { id: 34, name: 'Ticket (N/A passenger, N/A luggage)', capacity: 0, luggage: 0 },
                { id: 39, name: 'Ticket (Non-Malaysian) (N/A passenger, N/A luggage)', capacity: 0, luggage: 0 }
            ],

            // 行驶区域数据
            drivingRegions: [
                { id: 1, name: 'Kl/selangor (KL)', code: 'KL' },
                { id: 2, name: 'Penang (PNG)', code: 'PNG' },
                { id: 3, name: 'Johor (JB)', code: 'JB' },
                { id: 4, name: 'Sabah (SBH)', code: 'SBH' },
                { id: 5, name: 'Singapore (SG)', code: 'SG' },
                { id: 6, name: '携程专车 (CTRIP)', code: 'CTRIP' },
                { id: 8, name: 'Complete (COMPLETE)', code: 'COMPLETE' },
                { id: 9, name: 'Paging (PG)', code: 'PG' },
                { id: 10, name: 'Charter (CHRT)', code: 'CHRT' },
                { id: 12, name: 'Malacca (MLK)', code: 'MLK' },
                { id: 13, name: 'SMW (SMW)', code: 'SMW' }
            ],

            // 语言数据
            languages: [
                { id: 2, name: 'English (EN)', code: 'EN' },
                { id: 3, name: 'Malay (MY)', code: 'MY' },
                { id: 4, name: 'Chinese (CN)', code: 'CN' },
                { id: 5, name: 'Paging (PG)', code: 'PG' },
                { id: 6, name: 'Charter (CHARTER)', code: 'CHARTER' },
                { id: 8, name: '携程司导 (IM)', code: 'IM' },
                { id: 9, name: 'PSV (PSV)', code: 'PSV' },
                { id: 10, name: 'EVP (EVP)', code: 'EVP' },
                { id: 11, name: 'Car Type Reverify (CAR)', code: 'CAR' },
                { id: 12, name: 'Jetty (JETTY)', code: 'JETTY' },
                { id: 13, name: 'PhotoSkill Proof (PHOTO)', code: 'PHOTO' }
            ]
        };
    }

    /**
     * @function enableLocalMode - 启用本地模式
     * @description 启用本地数据模式，跳过登录和API调用
     */
    enableLocalMode() {
        this.isEnabled = true;
        logger.info('本地数据', '本地数据模式已启用');
    }

    /**
     * @function disableLocalMode - 禁用本地模式
     * @description 禁用本地数据模式，恢复正常API调用
     */
    disableLocalMode() {
        this.isEnabled = false;
        logger.info('本地数据', '本地数据模式已禁用');
    }

    /**
     * @function isLocalModeEnabled - 检查本地模式状态
     * @description 检查是否启用了本地数据模式
     * @returns {boolean} 本地模式状态
     */
    isLocalModeEnabled() {
        return this.isEnabled;
    }

    /**
     * @function populateAppState - 填充应用状态
     * @description 将本地数据填充到应用状态中
     * @param {AppState} appState - 应用状态实例
     */
    populateAppState(appState) {
        if (!this.isEnabled) {
            logger.warn('本地数据', '本地模式未启用，跳过数据填充');
            return;
        }

        try {
            // 填充各类数据到应用状态
            appState.backendUsers = this.data.backendUsers;
            appState.subCategories = this.data.subCategories;
            appState.carTypes = this.data.carTypes;
            appState.drivingRegions = this.data.drivingRegions;
            appState.languages = this.data.languages;

            // 缓存数据
            appState.cacheSystemData('backendUsers', this.data.backendUsers);
            appState.cacheSystemData('subCategories', this.data.subCategories);
            appState.cacheSystemData('carTypes', this.data.carTypes);
            appState.cacheSystemData('drivingRegions', this.data.drivingRegions);
            appState.cacheSystemData('languages', this.data.languages);

            // 设置模拟用户信息
            const mockUser = {
                id: 1,
                name: 'Local User',
                email: '<EMAIL>',
                role: 'admin'
            };

            appState.setUserInfo(mockUser);
            appState.setToken('local-mode-token');

            logger.success('本地数据', '应用状态数据填充完成', {
                backendUsers: this.data.backendUsers.length,
                subCategories: this.data.subCategories.length,
                carTypes: this.data.carTypes.length,
                drivingRegions: this.data.drivingRegions.length,
                languages: this.data.languages.length
            });

        } catch (error) {
            logger.error('本地数据', '应用状态数据填充失败', error);
            throw error;
        }
    }

    /**
     * @function getDataSummary - 获取数据摘要
     * @description 获取本地数据的统计摘要
     * @returns {object} 数据摘要
     */
    getDataSummary() {
        return {
            backendUsers: this.data.backendUsers.length,
            subCategories: this.data.subCategories.length,
            carTypes: this.data.carTypes.length,
            drivingRegions: this.data.drivingRegions.length,
            languages: this.data.languages.length,
            totalRecords: this.data.backendUsers.length + 
                         this.data.subCategories.length + 
                         this.data.carTypes.length + 
                         this.data.drivingRegions.length + 
                         this.data.languages.length
        };
    }
}

// 创建全局实例
window.localDataProvider = new LocalDataProvider();

logger.info('模块', '本地数据提供器模块加载完成', { version: 'v1.0.0' }); 