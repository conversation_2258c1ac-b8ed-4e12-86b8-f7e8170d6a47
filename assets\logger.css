/**
 * @file logger.css - 日志调试控制台样式
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

/* 控制台切换按钮 */
.console-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 9998;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.console-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* 调试控制台主容器 */
.debug-console {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 80%;
    height: 60%;
    background: #1e1e1e;
    border: 1px solid #333;
    border-radius: 8px 0 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    color: #d4d4d4;
    transition: all 0.3s ease;
}

.debug-console.hidden {
    display: none;
}

.debug-console.minimized {
    height: 40px;
    overflow: hidden;
}

.debug-console.minimized .console-body,
.debug-console.minimized .console-footer {
    display: none;
}

/* 控制台头部 */
.console-header {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    padding: 8px 12px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
}

.console-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #e2e8f0;
}

.console-icon {
    font-size: 16px;
}

.log-count {
    background: #4a5568;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    color: #a0aec0;
}

.console-controls {
    display: flex;
    align-items: center;
    gap: 6px;
}

.level-filter,
.module-filter {
    background: #2d3748;
    border: 1px solid #4a5568;
    color: #e2e8f0;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    outline: none;
}

.level-filter {
    width: 80px;
}

.module-filter {
    width: 120px;
}

.module-filter::placeholder {
    color: #a0aec0;
}

.console-controls button {
    background: #4a5568;
    border: 1px solid #718096;
    color: #e2e8f0;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.console-controls button:hover {
    background: #718096;
    transform: translateY(-1px);
}

.console-controls button.active {
    background: #3182ce;
    border-color: #2c5aa0;
}

.btn-close:hover {
    background: #e53e3e !important;
    border-color: #c53030 !important;
}

/* 控制台主体 */
.console-body {
    flex: 1;
    overflow: hidden;
    background: #1e1e1e;
}

.log-container {
    height: 100%;
    overflow-y: auto;
    padding: 8px;
    background: #1e1e1e;
}

/* 日志条目 */
.log-entry {
    margin-bottom: 4px;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 3px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.02);
}

.log-entry:hover {
    background: rgba(255, 255, 255, 0.05);
}

.log-entry.expanded {
    background: rgba(255, 255, 255, 0.08);
}

/* 不同级别的日志样式 */
.log-debug {
    border-left-color: #6c757d;
    color: #a0aec0;
}

.log-info {
    border-left-color: #17a2b8;
    color: #63b3ed;
}

.log-warn {
    border-left-color: #ffc107;
    color: #f6e05e;
}

.log-error {
    border-left-color: #dc3545;
    color: #fc8181;
}

.log-success {
    border-left-color: #28a745;
    color: #68d391;
}

/* 日志头部 */
.log-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.log-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.log-time {
    color: #a0aec0;
    font-size: 10px;
    font-family: monospace;
    min-width: 80px;
}

.log-level {
    background: rgba(255, 255, 255, 0.1);
    padding: 1px 6px;
    border-radius: 10px;
    font-size: 9px;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.log-debug .log-level {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
}

.log-info .log-level {
    background: rgba(23, 162, 184, 0.2);
    color: #17a2b8;
}

.log-warn .log-level {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.log-error .log-level {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.log-success .log-level {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.log-module {
    color: #9ca3af;
    font-weight: bold;
    font-size: 10px;
}

/* 日志消息 */
.log-message {
    margin-left: 24px;
    line-height: 1.4;
    word-break: break-word;
}

/* 日志数据 */
.log-data {
    margin-left: 24px;
    margin-top: 4px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    border: 1px solid #333;
    font-size: 11px;
    color: #cbd5e0;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.log-entry.expanded .log-data {
    display: block;
}

.log-data pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
}

/* API日志专用样式 */
.log-expand-hint {
    color: #9ca3af;
    font-size: 9px;
    font-style: italic;
    margin-left: auto;
    padding: 1px 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.log-expand-hint:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #e2e8f0;
}

.api-data {
    border-left: 3px solid #3182ce;
    background: rgba(49, 130, 206, 0.1);
    max-height: 300px;
    overflow-y: auto;
}

.api-data pre {
    color: #e2e8f0;
    font-size: 10px;
    line-height: 1.3;
}

/* API请求和响应的特殊标识 */
.log-entry[data-api-type="request"] {
    border-left-color: #3182ce;
}

.log-entry[data-api-type="response"] {
    border-left-color: #38a169;
}

.log-entry[data-api-type="response"].log-error {
    border-left-color: #e53e3e;
}

/* 控制台底部 */
.console-footer {
    background: #2d3748;
    padding: 8px 12px;
    border-top: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
}

.console-stats {
    display: flex;
    gap: 12px;
}

.stat-item {
    font-size: 10px;
    color: #a0aec0;
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.console-actions {
    display: flex;
    gap: 6px;
}

.btn-pause,
.btn-resume {
    background: #4a5568;
    border: 1px solid #718096;
    color: #e2e8f0;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
}

.btn-pause:hover,
.btn-resume:hover {
    background: #718096;
}

/* 滚动条样式 */
.log-container::-webkit-scrollbar {
    width: 8px;
}

.log-container::-webkit-scrollbar-track {
    background: #2d3748;
}

.log-container::-webkit-scrollbar-thumb {
    background: #4a5568;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: #718096;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .debug-console {
        width: 90%;
        height: 50%;
    }
    
    .console-controls {
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .module-filter {
        width: 100px;
    }
}

@media (max-width: 768px) {
    .debug-console {
        width: 100%;
        height: 40%;
        border-radius: 0;
    }
    
    .console-header {
        padding: 6px 8px;
    }
    
    .console-controls button {
        padding: 3px 6px;
        font-size: 11px;
        min-width: 24px;
        height: 24px;
    }
    
    .level-filter,
    .module-filter {
        padding: 3px 6px;
        font-size: 10px;
    }
    
    .log-entry {
        padding: 4px 6px;
    }
    
    .log-header {
        gap: 6px;
    }
    
    .log-time {
        min-width: 70px;
    }
}

/* 动画效果 */
@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.debug-console:not(.hidden) {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.log-entry {
    animation: fadeIn 0.2s ease-out;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .debug-console {
        border: 2px solid #fff;
    }
    
    .log-entry {
        border: 1px solid #666;
    }
    
    .console-controls button {
        border: 2px solid #fff;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .debug-console,
    .log-entry,
    .console-toggle {
        animation: none;
        transition: none;
    }
}

/* 深色主题优化 */
@media (prefers-color-scheme: dark) {
    .debug-console {
        background: #0d1117;
        border-color: #21262d;
    }
    
    .console-header {
        background: linear-gradient(135deg, #161b22 0%, #21262d 100%);
    }
    
    .log-container {
        background: #0d1117;
    }
    
    .console-footer {
        background: #161b22;
    }
}