/**
 * @file smart-selection-engine.js - 智能选择引擎（合并版）
 * @description 合并了matching-engine.js和learning-engine.js的功能，提供增强匹配和智能学习能力
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.0.3
 */

/**
 * @class EnhancedMatchingEngine - 增强匹配引擎
 * @description 提供模糊匹配、同义词匹配、语义相似度计算等高级匹配功能
 */
class EnhancedMatchingEngine {
    constructor() {
        // 同义词词典 - 支持中英文
        this.synonymDict = {
            // 车型同义词
            'sedan': ['轿车', '小车', '私家车', 'car', 'saloon'],
            'suv': ['越野车', '运动型多用途车', 'sport utility vehicle'],
            'mpv': ['商务车', '多用途车', 'multi-purpose vehicle', '面包车'],
            'van': ['面包车', '厢式车', '货车', 'minivan'],
            'bus': ['大巴', '巴士', '客车', 'coach'],
            'luxury': ['豪华', '高端', '奢华', 'premium', 'deluxe', 'vip'],
            
            // 服务类型同义词
            'pickup': ['接机', '接送', '迎接', 'pick up', 'collect'],
            'dropoff': ['送机', '送达', '护送', 'drop off', 'deliver'],
            'charter': ['包车', '租车', '专车', 'private hire', 'exclusive'],
            'transfer': ['转移', '接驳', '中转', 'transport'],
            
            // 地点同义词
            'airport': ['机场', '航站楼', '候机楼', 'terminal'],
            'hotel': ['酒店', '宾馆', '旅馆', 'resort', 'accommodation'],
            'mall': ['商场', '购物中心', 'shopping center', 'plaza'],
            
            // 时间同义词
            'urgent': ['紧急', '急需', '马上', 'asap', 'immediately'],
            'flexible': ['灵活', '可调整', '弹性', 'adjustable']
        };
        
        // 拼音匹配表（扩展版）
        this.pinyinMap = {
            // 基础拼音
            'jie': '接', 'song': '送', 'che': '车', 'ji': '机',
            'bao': '包', 'hao': '豪', 'hua': '华', 'shang': '商',
            'wu': '务', 'jing': '经', 'ji': '济', 'shu': '舒',
            'shi': '适', 'da': '大', 'xiao': '小', 'zhong': '中',
            // 扩展拼音
            'jie机': '接机', 'song机': '送机', 'bao车': '包车', 
            'lv游': '旅游', 'jing点': '景点', 'hao华': '豪华',
            'shang务': '商务', 'an全': '安全', 'kuai捷': '快捷',
            'wen度': '温度', 'su度': '速度', 'gao铁': '高铁',
            'fei机': '飞机', 'huo车': '火车', 'lu线': '路线'
        };
        
        // 缩写匹配表
        this.abbreviationMap = {
            // 英文缩写
            'suv': 'sport utility vehicle',
            'mpv': 'multi-purpose vehicle', 
            'vip': 'very important person',
            'api': 'application programming interface',
            'ota': 'online travel agency',
            'gps': 'global positioning system',
            'ac': 'air conditioning',
            'atm': 'automated teller machine',
            'kl': 'kuala lumpur',
            'pj': 'petaling jaya',
            'sg': 'singapore',
            
            // 中文缩写
            '北京': 'bj', '上海': 'sh', '广州': 'gz', '深圳': 'sz',
            '成都': 'cd', '杭州': 'hz', '南京': 'nj', '武汉': 'wh',
            '西安': 'xa', '重庆': 'cq', '天津': 'tj', '青岛': 'qd'
        };
        
        // 语义相似度词汇表（扩展版）
        this.semanticGroups = {
            transportation: {
                vehicles: ['车', 'car', 'vehicle', 'automobile', '汽车', '车辆'],
                types: ['sedan', 'suv', 'mpv', 'van', 'bus', '轿车', '越野车', '商务车', '面包车', '巴士'],
                actions: ['drive', 'ride', 'travel', '驾驶', '乘坐', '出行', '运输']
            },
            service: {
                types: ['pickup', 'dropoff', 'charter', 'transfer', '接送', '包车', '转乘', '服务'],
                quality: ['luxury', 'premium', 'standard', 'economy', '豪华', '高级', '标准', '经济'],
                time: ['urgent', 'immediate', 'scheduled', '紧急', '立即', '预约', '定时']
            },
            location: {
                airport: ['airport', 'terminal', 'runway', '机场', '航站楼', '候机楼'],
                hotel: ['hotel', 'resort', 'accommodation', '酒店', '度假村', '住宿'],
                attraction: ['attraction', 'sightseeing', 'tour', '景点', '观光', '旅游']
            }
        };
        
        // 匹配权重配置（增强版）
        this.matchWeights = {
            exactMatch: 1.0,        // 精确匹配
            synonymMatch: 0.9,      // 同义词匹配
            pinyinMatch: 0.85,      // 拼音匹配
            abbreviationMatch: 0.8, // 缩写匹配
            fuzzyMatch: 0.75,       // 模糊匹配
            semanticMatch: 0.7,     // 语义匹配
            partialMatch: 0.6,      // 部分匹配
            contextMatch: 0.5,      // 上下文匹配
            soundexMatch: 0.4       // 音似匹配
        };
    }
    
    /**
     * @function calculateStringDistance - 计算字符串编辑距离
     * @param {string} str1 - 字符串1
     * @param {string} str2 - 字符串2
     * @returns {number} 编辑距离
     */
    calculateStringDistance(str1, str2) {
        if (!str1 || !str2) return Infinity;
        
        const len1 = str1.length;
        const len2 = str2.length;
        
        // 创建距离矩阵
        const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
        
        // 初始化第一行和第一列
        for (let i = 0; i <= len1; i++) matrix[i][0] = i;
        for (let j = 0; j <= len2; j++) matrix[0][j] = j;
        
        // 计算编辑距离
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,      // 删除
                    matrix[i][j - 1] + 1,      // 插入
                    matrix[i - 1][j - 1] + cost // 替换
                );
            }
        }
        
        return matrix[len1][len2];
    }
    
    /**
     * @function calculateSimilarity - 计算相似度评分
     * @param {string} source - 源字符串
     * @param {string} target - 目标字符串
     * @returns {number} 相似度评分 (0.0-1.0)
     */
    calculateSimilarity(source, target) {
        if (!source || !target) return 0;
        
        const src = source.toLowerCase().trim();
        const tgt = target.toLowerCase().trim();
        
        // 精确匹配
        if (src === tgt) return this.matchWeights.exactMatch;
        
        // 包含匹配
        if (src.includes(tgt) || tgt.includes(src)) {
            const longerLength = Math.max(src.length, tgt.length);
            const shorterLength = Math.min(src.length, tgt.length);
            return this.matchWeights.partialMatch * (shorterLength / longerLength);
        }
        
        // 编辑距离匹配
        const distance = this.calculateStringDistance(src, tgt);
        const maxLength = Math.max(src.length, tgt.length);
        if (maxLength === 0) return 0;
        
        const similarity = 1 - (distance / maxLength);
        return similarity * this.matchWeights.fuzzyMatch;
    }
    
    /**
     * @function findSynonyms - 查找同义词
     * @param {string} word - 输入词汇
     * @returns {string[]} 同义词列表
     */
    findSynonyms(word) {
        const normalizedWord = word.toLowerCase().trim();
        const synonyms = [];
        
        // 直接查找
        if (this.synonymDict[normalizedWord]) {
            synonyms.push(...this.synonymDict[normalizedWord]);
        }
        
        // 反向查找
        for (const [key, values] of Object.entries(this.synonymDict)) {
            if (values.some(synonym => synonym.toLowerCase() === normalizedWord)) {
                synonyms.push(key, ...values);
            }
        }
        
        // 去重并返回
        return [...new Set(synonyms)].filter(syn => syn.toLowerCase() !== normalizedWord);
    }
    
    /**
     * @function enhancedKeywordMatch - 增强关键词匹配
     * @param {string} text - 待匹配文本
     * @param {string} keyword - 关键词
     * @returns {object} 匹配结果
     */
    enhancedKeywordMatch(text, keyword) {
        if (!text || !keyword) {
            return { found: false, score: 0, method: 'invalid_input' };
        }
        
        const normalizedText = text.toLowerCase().trim();
        const normalizedKeyword = keyword.toLowerCase().trim();
        
        // 1. 精确匹配
        if (normalizedText.includes(normalizedKeyword)) {
            return {
                found: true,
                score: this.matchWeights.exactMatch,
                method: 'exact_match',
                matchedText: normalizedKeyword
            };
        }
        
        // 2. 同义词匹配
        const synonymResult = this.performSynonymMatch(normalizedText, normalizedKeyword);
        if (synonymResult.found) return synonymResult;
        
        // 3. 拼音匹配
        const pinyinResult = this.performPinyinMatch(normalizedText, normalizedKeyword);
        if (pinyinResult.found) return pinyinResult;
        
        // 4. 缩写匹配
        const abbreviationResult = this.performAbbreviationMatch(normalizedText, normalizedKeyword);
        if (abbreviationResult.found) return abbreviationResult;
        
        // 5. 语义匹配
        const semanticResult = this.performSemanticMatch(normalizedText, normalizedKeyword);
        if (semanticResult.found) return semanticResult;
        
        // 6. 模糊匹配
        const similarity = this.calculateSimilarity(normalizedText, normalizedKeyword);
        if (similarity > 0.6) {
            return {
                found: true,
                score: similarity,
                method: 'fuzzy_match',
                similarity: similarity
            };
        }
        
        return { found: false, score: 0, method: 'no_match' };
    }
    
    /**
     * @function performSynonymMatch - 执行同义词匹配
     * @param {string} text - 文本
     * @param {string} keyword - 关键词
     * @returns {object} 匹配结果
     */
    performSynonymMatch(text, keyword) {
        const synonyms = this.findSynonyms(keyword);
        
        for (const synonym of synonyms) {
            if (text.includes(synonym.toLowerCase())) {
                return {
                    found: true,
                    score: this.matchWeights.synonymMatch,
                    method: 'synonym_match',
                    matchedSynonym: synonym
                };
            }
        }
        
        return { found: false, score: 0 };
    }
    
    /**
     * @function performPinyinMatch - 执行拼音匹配
     * @param {string} text - 文本
     * @param {string} keyword - 关键词
     * @returns {object} 匹配结果
     */
    performPinyinMatch(text, keyword) {
        // 检查拼音映射
        for (const [pinyin, chinese] of Object.entries(this.pinyinMap)) {
            if (keyword.includes(pinyin) && text.includes(chinese)) {
                return {
                    found: true,
                    score: this.matchWeights.pinyinMatch,
                    method: 'pinyin_match',
                    pinyinMatch: { pinyin, chinese }
                };
            }
            if (keyword.includes(chinese) && text.includes(pinyin)) {
                return {
                    found: true,
                    score: this.matchWeights.pinyinMatch,
                    method: 'pinyin_match',
                    pinyinMatch: { pinyin, chinese }
                };
            }
        }
        
        return { found: false, score: 0 };
    }
    
    /**
     * @function performAbbreviationMatch - 执行缩写匹配
     * @param {string} text - 文本
     * @param {string} keyword - 关键词
     * @returns {object} 匹配结果
     */
    performAbbreviationMatch(text, keyword) {
        // 检查缩写映射
        for (const [abbr, full] of Object.entries(this.abbreviationMap)) {
            if (keyword.includes(abbr) && text.includes(full)) {
                return {
                    found: true,
                    score: this.matchWeights.abbreviationMatch,
                    method: 'abbreviation_match',
                    abbreviationMatch: { abbreviation: abbr, fullForm: full }
                };
            }
            if (keyword.includes(full) && text.includes(abbr)) {
                return {
                    found: true,
                    score: this.matchWeights.abbreviationMatch,
                    method: 'abbreviation_match',
                    abbreviationMatch: { abbreviation: abbr, fullForm: full }
                };
            }
        }
        
        return { found: false, score: 0 };
    }
    
    /**
     * @function performSemanticMatch - 执行语义匹配
     * @param {string} text - 文本
     * @param {string} keyword - 关键词
     * @returns {object} 匹配结果
     */
    performSemanticMatch(text, keyword) {
        let bestMatch = { found: false, score: 0 };
        
        // 遍历语义组
        for (const [groupName, group] of Object.entries(this.semanticGroups)) {
            for (const [categoryName, words] of Object.entries(group)) {
                const keywordInCategory = words.some(word => keyword.includes(word.toLowerCase()));
                const textInCategory = words.some(word => text.includes(word.toLowerCase()));
                
                if (keywordInCategory && textInCategory) {
                    const score = this.matchWeights.semanticMatch;
                    if (score > bestMatch.score) {
                        bestMatch = {
                            found: true,
                            score: score,
                            method: 'semantic_match',
                            semanticGroup: groupName,
                            semanticCategory: categoryName
                        };
                    }
                }
            }
        }
        
        return bestMatch;
    }
}

/**
 * @class IntelligentLearningEngine - 智能学习引擎
 * @description 从历史数据中学习，自动优化匹配算法和权重
 */
class IntelligentLearningEngine {
    constructor() {
        this.learningData = this.loadLearningData();
        this.performanceMetrics = {
            totalSelections: 0,
            successfulSelections: 0,
            failedSelections: 0,
            accuracyRate: 0,
            lastUpdate: Date.now()
        };
        
        // 动态权重调整因子
        this.dynamicWeights = {
            passengerCount: 0.4,
            keywordMatch: 0.3,
            serviceType: 0.2,
            contextual: 0.1
        };
        
        // 学习阈值
        this.learningThreshold = {
            minSamples: 10,          // 最少样本数
            confidenceThreshold: 0.8, // 置信度阈值
            adaptionRate: 0.1        // 自适应学习率
        };
    }
    
    /**
     * @function loadLearningData - 加载学习数据
     * @returns {object} 学习数据
     */
    loadLearningData() {
        try {
            const savedData = localStorage.getItem('smartSelection_learningData');
            return savedData ? JSON.parse(savedData) : {
                successfulMatches: [],
                failedMatches: [],
                userPreferences: {},
                patternRecognition: {}
            };
        } catch (error) {
            console.warn('智能学习引擎: 加载学习数据失败', error);
            return {
                successfulMatches: [],
                failedMatches: [],
                userPreferences: {},
                patternRecognition: {}
            };
        }
    }
    
    /**
     * @function saveLearningData - 保存学习数据
     */
    saveLearningData() {
        try {
            localStorage.setItem('smartSelection_learningData', JSON.stringify(this.learningData));
        } catch (error) {
            console.warn('智能学习引擎: 保存学习数据失败', error);
        }
    }
    
    /**
     * @function recordSelection - 记录选择结果
     * @param {object} selectionRecord - 选择记录
     */
    recordSelection(selectionRecord) {
        const record = {
            ...selectionRecord,
            timestamp: Date.now(),
            sessionId: this.getSessionId()
        };
        
        if (selectionRecord.success) {
            this.learningData.successfulMatches.push(record);
            this.performanceMetrics.successfulSelections++;
        } else {
            this.learningData.failedMatches.push(record);
            this.performanceMetrics.failedSelections++;
        }
        
        this.performanceMetrics.totalSelections++;
        this.updateAccuracyRate();
        
        // 限制记录数量，避免内存过载
        if (this.learningData.successfulMatches.length > 1000) {
            this.learningData.successfulMatches = this.learningData.successfulMatches.slice(-500);
        }
        if (this.learningData.failedMatches.length > 500) {
            this.learningData.failedMatches = this.learningData.failedMatches.slice(-250);
        }
        
        this.saveLearningData();
        this.adaptWeights();
    }
    
    /**
     * @function getSessionId - 获取会话ID
     * @returns {string} 会话ID
     */
    getSessionId() {
        if (!window.sessionStorage.getItem('smartSelection_sessionId')) {
            window.sessionStorage.setItem('smartSelection_sessionId', 
                'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9));
        }
        return window.sessionStorage.getItem('smartSelection_sessionId');
    }
    
    /**
     * @function updateAccuracyRate - 更新准确率
     */
    updateAccuracyRate() {
        if (this.performanceMetrics.totalSelections > 0) {
            this.performanceMetrics.accuracyRate = 
                this.performanceMetrics.successfulSelections / this.performanceMetrics.totalSelections;
        }
        this.performanceMetrics.lastUpdate = Date.now();
    }
    
    /**
     * @function adaptWeights - 自适应权重调整
     */
    adaptWeights() {
        if (this.learningData.successfulMatches.length < this.learningThreshold.minSamples) {
            return; // 样本数不足，不进行调整
        }
        
        // 分析成功匹配的模式
        const recentSuccess = this.learningData.successfulMatches.slice(-50);
        const methodStats = {};
        
        recentSuccess.forEach(record => {
            const method = record.method || 'unknown';
            if (!methodStats[method]) {
                methodStats[method] = { count: 0, totalConfidence: 0 };
            }
            methodStats[method].count++;
            methodStats[method].totalConfidence += (record.confidence || 0.5);
        });
        
        // 根据成功率调整权重
        const totalSamples = recentSuccess.length;
        for (const [method, stats] of Object.entries(methodStats)) {
            const successRate = stats.count / totalSamples;
            const avgConfidence = stats.totalConfidence / stats.count;
            
            // 自适应调整对应的权重
            if (method.includes('passenger') && successRate > 0.8) {
                this.dynamicWeights.passengerCount = Math.min(0.6, 
                    this.dynamicWeights.passengerCount + this.learningThreshold.adaptionRate);
            }
            if (method.includes('keyword') && successRate > 0.8) {
                this.dynamicWeights.keywordMatch = Math.min(0.5, 
                    this.dynamicWeights.keywordMatch + this.learningThreshold.adaptionRate);
            }
        }
        
        // 权重归一化
        const totalWeight = Object.values(this.dynamicWeights).reduce((sum, weight) => sum + weight, 0);
        for (const key of Object.keys(this.dynamicWeights)) {
            this.dynamicWeights[key] = this.dynamicWeights[key] / totalWeight;
        }
    }
    
    /**
     * @function getOptimalWeight - 获取优化后的权重
     * @param {string} method - 方法名称
     * @returns {number} 权重值
     */
    getOptimalWeight(method) {
        const baseWeights = {
            'passenger_count_exact': this.dynamicWeights.passengerCount,
            'enhanced_keyword_match': this.dynamicWeights.keywordMatch,
            'service_mapping': this.dynamicWeights.serviceType,
            'contextual_analysis': this.dynamicWeights.contextual
        };
        
        return baseWeights[method] || 0.5;
    }
    
    /**
     * @function analyzePatterns - 分析匹配模式
     * @param {object} orderData - 订单数据
     * @returns {object} 模式分析结果
     */
    analyzePatterns(orderData) {
        const patterns = {
            suggestedMethod: null,
            confidence: 0,
            reasoning: '',
            similarCases: []
        };
        
        // 查找相似的成功案例
        const similarCases = this.findSimilarSuccessfulCases(orderData);
        patterns.similarCases = similarCases.slice(0, 5); // 最多返回5个相似案例
        
        if (similarCases.length > 0) {
            // 分析最常用的成功方法
            const methodCounts = {};
            similarCases.forEach(case_ => {
                const method = case_.method || 'unknown';
                methodCounts[method] = (methodCounts[method] || 0) + 1;
            });
            
            // 找出最常用的方法
            const mostUsedMethod = Object.entries(methodCounts)
                .sort(([,a], [,b]) => b - a)[0];
            
            if (mostUsedMethod) {
                patterns.suggestedMethod = mostUsedMethod[0];
                patterns.confidence = mostUsedMethod[1] / similarCases.length;
                patterns.reasoning = `基于${similarCases.length}个相似案例，${mostUsedMethod[0]}方法成功率最高`;
            }
        }
        
        return patterns;
    }
    
    /**
     * @function findSimilarSuccessfulCases - 查找相似的成功案例
     * @param {object} orderData - 订单数据
     * @returns {Array} 相似案例列表
     */
    findSimilarSuccessfulCases(orderData) {
        const similarCases = [];
        const recentSuccess = this.learningData.successfulMatches.slice(-100); // 最近100个成功案例
        
        recentSuccess.forEach(record => {
            let similarity = 0;
            let matchCount = 0;
            
            // 比较乘客数量
            if (record.input && record.input.passenger_number && orderData.passenger_number) {
                const passengerDiff = Math.abs(
                    parseInt(record.input.passenger_number) - parseInt(orderData.passenger_number)
                );
                if (passengerDiff <= 1) {
                    similarity += 0.3;
                    matchCount++;
                }
            }
            
            // 比较服务类型关键词
            const recordText = [
                record.input?.pickup || '',
                record.input?.destination || '',
                record.input?.extra_requirement || ''
            ].join(' ').toLowerCase();
            
            const orderText = [
                orderData.pickup || '',
                orderData.destination || '',
                orderData.extra_requirement || ''
            ].join(' ').toLowerCase();
            
            // 简单的关键词匹配
            const commonKeywords = ['机场', 'airport', '接机', 'pickup', '送机', 'dropoff', '包车', 'charter'];
            commonKeywords.forEach(keyword => {
                if (recordText.includes(keyword) && orderText.includes(keyword)) {
                    similarity += 0.1;
                    matchCount++;
                }
            });
            
            // 如果相似度足够高，加入结果
            if (similarity > 0.3 && matchCount > 0) {
                similarCases.push({
                    ...record,
                    similarity: similarity,
                    matchCount: matchCount
                });
            }
        });
        
        // 按相似度排序
        return similarCases.sort((a, b) => b.similarity - a.similarity);
    }
    
    /**
     * @function calculateMethodAccuracy - 计算方法准确率
     * @param {string} method - 方法名称
     * @returns {number} 准确率
     */
    calculateMethodAccuracy(method) {
        const methodRecords = this.learningData.successfulMatches.filter(
            record => record.method === method
        );
        
        if (methodRecords.length === 0) return 0.5; // 默认准确率
        
        const totalConfidence = methodRecords.reduce(
            (sum, record) => sum + (record.confidence || 0.5), 0
        );
        
        return totalConfidence / methodRecords.length;
    }
    
    /**
     * @function getPerformanceMetrics - 获取性能指标
     * @returns {object} 性能指标
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            dynamicWeights: { ...this.dynamicWeights },
            learningDataSize: {
                successfulMatches: this.learningData.successfulMatches.length,
                failedMatches: this.learningData.failedMatches.length
            }
        };
    }
    
    /**
     * @function clearLearningData - 清除学习数据
     */
    clearLearningData() {
        this.learningData = {
            successfulMatches: [],
            failedMatches: [],
            userPreferences: {},
            patternRecognition: {}
        };
        
        this.performanceMetrics = {
            totalSelections: 0,
            successfulSelections: 0,
            failedSelections: 0,
            accuracyRate: 0,
            lastUpdate: Date.now()
        };
        
        // 重置权重为默认值
        this.dynamicWeights = {
            passengerCount: 0.4,
            keywordMatch: 0.3,
            serviceType: 0.2,
            contextual: 0.1
        };
        
        this.saveLearningData();
    }
}

/**
 * @class AddressRegionMatcher - 地址-区域匹配算法引擎
 * @description 专门用于地址关键词与区域的智能匹配，支持地理位置识别和置信度评分
 */
class AddressRegionMatcher {
    constructor() {
        this.logger = window.logger || console;
        
        // 区域关键词映射表（扩展版）
        this.regionKeywordMap = {
            // KL/Selangor - ID: 1
            1: {
                name: 'Kl/selangor (KL)',
                keywords: {
                    // 主要城市和地区
                    'kuala lumpur': { confidence: 0.95, type: 'city' },
                    'kl': { confidence: 0.9, type: 'abbreviation' },
                    'selangor': { confidence: 0.9, type: 'state' },
                    'petaling jaya': { confidence: 0.85, type: 'city' },
                    'pj': { confidence: 0.8, type: 'abbreviation' },
                    'subang': { confidence: 0.8, type: 'area' },
                    'shah alam': { confidence: 0.8, type: 'city' },
                    'klang': { confidence: 0.8, type: 'city' },
                    'ampang': { confidence: 0.75, type: 'area' },
                    'cheras': { confidence: 0.75, type: 'area' },
                    'bangsar': { confidence: 0.75, type: 'area' },
                    'mont kiara': { confidence: 0.75, type: 'area' },
                    'damansara': { confidence: 0.75, type: 'area' },
                    'usj': { confidence: 0.7, type: 'area' },
                    'sunway': { confidence: 0.7, type: 'area' },
                    
                    // 机场和交通枢纽
                    'klia': { confidence: 0.95, type: 'airport' },
                    'klia2': { confidence: 0.95, type: 'airport' },
                    'sultan abdul aziz shah airport': { confidence: 0.9, type: 'airport' },
                    'subang airport': { confidence: 0.9, type: 'airport' },
                    'kl sentral': { confidence: 0.85, type: 'transport' },
                    'tbs': { confidence: 0.8, type: 'transport' },
                    'terminal bersepadu selatan': { confidence: 0.8, type: 'transport' },
                    
                    // 中文关键词
                    '吉隆坡': { confidence: 0.95, type: 'city' },
                    '雪兰莪': { confidence: 0.9, type: 'state' },
                    '八打灵再也': { confidence: 0.85, type: 'city' },
                    '梳邦': { confidence: 0.8, type: 'area' },
                    '莎阿南': { confidence: 0.8, type: 'city' },
                    '巴生': { confidence: 0.8, type: 'city' },
                    '安邦': { confidence: 0.75, type: 'area' },
                    '蕉赖': { confidence: 0.75, type: 'area' }
                }
            },
            
            // Penang - ID: 2
            2: {
                name: 'Penang (PNG)',
                keywords: {
                    'penang': { confidence: 0.95, type: 'state' },
                    'georgetown': { confidence: 0.9, type: 'city' },
                    'george town': { confidence: 0.9, type: 'city' },
                    'butterworth': { confidence: 0.85, type: 'city' },
                    'bayan lepas': { confidence: 0.8, type: 'area' },
                    'penang airport': { confidence: 0.95, type: 'airport' },
                    'penang international airport': { confidence: 0.95, type: 'airport' },
                    'gurney': { confidence: 0.75, type: 'area' },
                    'komtar': { confidence: 0.75, type: 'landmark' },
                    'tanjung bungah': { confidence: 0.7, type: 'area' },
                    'batu ferringhi': { confidence: 0.7, type: 'area' },
                    
                    // 中文关键词
                    '槟城': { confidence: 0.95, type: 'state' },
                    '乔治市': { confidence: 0.9, type: 'city' },
                    '北海': { confidence: 0.85, type: 'city' },
                    '峇六拜': { confidence: 0.8, type: 'area' }
                }
            },
            
            // Johor - ID: 3
            3: {
                name: 'Johor (JB)',
                keywords: {
                    'johor': { confidence: 0.95, type: 'state' },
                    'johor bahru': { confidence: 0.95, type: 'city' },
                    'jb': { confidence: 0.9, type: 'abbreviation' },
                    'senai': { confidence: 0.85, type: 'area' },
                    'senai airport': { confidence: 0.95, type: 'airport' },
                    'johor bahru senai international airport': { confidence: 0.95, type: 'airport' },
                    'iskandar puteri': { confidence: 0.8, type: 'city' },
                    'nusajaya': { confidence: 0.8, type: 'area' },
                    'skudai': { confidence: 0.75, type: 'area' },
                    'tampoi': { confidence: 0.75, type: 'area' },
                    'tebrau': { confidence: 0.75, type: 'area' },
                    'larkin': { confidence: 0.75, type: 'area' },
                    'city square': { confidence: 0.7, type: 'landmark' },
                    'jb sentral': { confidence: 0.8, type: 'transport' },
                    
                    // 中文关键词
                    '柔佛': { confidence: 0.95, type: 'state' },
                    '新山': { confidence: 0.95, type: 'city' },
                    '士乃': { confidence: 0.85, type: 'area' },
                    '依斯干达公主城': { confidence: 0.8, type: 'city' },
                    '努沙再也': { confidence: 0.8, type: 'area' }
                }
            },
            
            // Sabah - ID: 4
            4: {
                name: 'Sabah (SBH)',
                keywords: {
                    'sabah': { confidence: 0.95, type: 'state' },
                    'kota kinabalu': { confidence: 0.95, type: 'city' },
                    'kk': { confidence: 0.85, type: 'abbreviation' },
                    'kota kinabalu airport': { confidence: 0.95, type: 'airport' },
                    'kota kinabalu international airport': { confidence: 0.95, type: 'airport' },
                    'sandakan': { confidence: 0.9, type: 'city' },
                    'tawau': { confidence: 0.9, type: 'city' },
                    'lahad datu': { confidence: 0.85, type: 'city' },
                    'semporna': { confidence: 0.8, type: 'area' },
                    'kudat': { confidence: 0.8, type: 'area' },
                    'beaufort': { confidence: 0.75, type: 'area' },
                    'papar': { confidence: 0.75, type: 'area' },
                    'tuaran': { confidence: 0.75, type: 'area' },
                    
                    // 中文关键词
                    '沙巴': { confidence: 0.95, type: 'state' },
                    '亚庇': { confidence: 0.95, type: 'city' },
                    '山打根': { confidence: 0.9, type: 'city' },
                    '斗湖': { confidence: 0.9, type: 'city' },
                    '拿笃': { confidence: 0.85, type: 'city' }
                }
            },
            
            // Singapore - ID: 5
            5: {
                name: 'Singapore (SG)',
                keywords: {
                    'singapore': { confidence: 0.95, type: 'country' },
                    'sg': { confidence: 0.85, type: 'abbreviation' },
                    'changi': { confidence: 0.9, type: 'area' },
                    'changi airport': { confidence: 0.95, type: 'airport' },
                    'singapore changi airport': { confidence: 0.95, type: 'airport' },
                    'orchard': { confidence: 0.8, type: 'area' },
                    'marina bay': { confidence: 0.8, type: 'area' },
                    'sentosa': { confidence: 0.8, type: 'area' },
                    'jurong': { confidence: 0.75, type: 'area' },
                    'tampines': { confidence: 0.75, type: 'area' },
                    'woodlands': { confidence: 0.75, type: 'area' },
                    'toa payoh': { confidence: 0.7, type: 'area' },
                    'ang mo kio': { confidence: 0.7, type: 'area' },
                    
                    // 中文关键词
                    '新加坡': { confidence: 0.95, type: 'country' },
                    '樟宜': { confidence: 0.9, type: 'area' },
                    '乌节路': { confidence: 0.8, type: 'area' },
                    '滨海湾': { confidence: 0.8, type: 'area' },
                    '圣淘沙': { confidence: 0.8, type: 'area' }
                }
            },
            
            // Malacca - ID: 12
            12: {
                name: 'Malacca (MLK)',
                keywords: {
                    'malacca': { confidence: 0.95, type: 'state' },
                    'melaka': { confidence: 0.95, type: 'state' },
                    'malacca city': { confidence: 0.9, type: 'city' },
                    'melaka city': { confidence: 0.9, type: 'city' },
                    'ayer keroh': { confidence: 0.8, type: 'area' },
                    'batu berendam': { confidence: 0.8, type: 'area' },
                    'malacca airport': { confidence: 0.9, type: 'airport' },
                    'melaka airport': { confidence: 0.9, type: 'airport' },
                    'jonker street': { confidence: 0.75, type: 'landmark' },
                    'a famosa': { confidence: 0.7, type: 'landmark' },
                    'dataran pahlawan': { confidence: 0.7, type: 'landmark' },
                    
                    // 中文关键词
                    '马六甲': { confidence: 0.95, type: 'state' },
                    '马六甲市': { confidence: 0.9, type: 'city' },
                    '爱极乐': { confidence: 0.8, type: 'area' },
                    '鸡场街': { confidence: 0.75, type: 'landmark' }
                }
            }
        };
        
        // 地理位置类型权重
        this.locationTypeWeights = {
            'airport': 1.0,      // 机场最高权重
            'city': 0.9,         // 城市
            'state': 0.85,       // 州/省
            'country': 0.8,      // 国家
            'transport': 0.75,   // 交通枢纽
            'area': 0.7,         // 地区
            'landmark': 0.6,     // 地标
            'abbreviation': 0.5  // 缩写
        };
        
        // 地址解析模式
        this.addressPatterns = {
            // 机场模式
            airport: /(?:airport|机场|航站楼|候机楼|terminal)/i,
            // 酒店模式
            hotel: /(?:hotel|resort|inn|lodge|酒店|宾馆|旅馆|度假村)/i,
            // 商场模式
            mall: /(?:mall|shopping|plaza|center|商场|购物中心|广场)/i,
            // 住宅模式
            residential: /(?:house|home|apartment|condo|住宅|公寓|家)/i,
            // 办公模式
            office: /(?:office|building|tower|办公|大厦|写字楼)/i
        };
    }
    
    /**
     * @function matchRegionByAddress - 主要的地址-区域匹配方法
     * @param {string} pickup - 接送地址
     * @param {string} destination - 目的地地址
     * @returns {Object} 匹配结果
     */
    matchRegionByAddress(pickup = '', destination = '') {
        try {
            this.logger.debug('AddressRegionMatcher', '开始地址-区域匹配', { pickup, destination });
            
            const fullAddress = `${pickup} ${destination}`.toLowerCase().trim();
            if (!fullAddress) {
                return this.getDefaultMatch();
            }
            
            // 1. 精确关键词匹配
            const exactMatch = this.performExactKeywordMatch(fullAddress);
            if (exactMatch.confidence > 0.8) {
                this.logger.info('AddressRegionMatcher', '精确匹配成功', exactMatch);
                return exactMatch;
            }
            
            // 2. 模糊关键词匹配
            const fuzzyMatch = this.performFuzzyKeywordMatch(fullAddress);
            if (fuzzyMatch.confidence > 0.6) {
                this.logger.info('AddressRegionMatcher', '模糊匹配成功', fuzzyMatch);
                return fuzzyMatch;
            }
            
            // 3. 地理位置类型推理
            const typeMatch = this.performLocationTypeInference(fullAddress);
            if (typeMatch.confidence > 0.5) {
                this.logger.info('AddressRegionMatcher', '类型推理成功', typeMatch);
                return typeMatch;
            }
            
            // 4. 返回默认匹配
            const defaultMatch = this.getDefaultMatch();
            this.logger.info('AddressRegionMatcher', '使用默认匹配', defaultMatch);
            return defaultMatch;
            
        } catch (error) {
            this.logger.error('AddressRegionMatcher', '地址匹配失败', error);
            return this.getDefaultMatch();
        }
    }
    
    /**
     * @function performExactKeywordMatch - 精确关键词匹配
     * @param {string} address - 地址文本
     * @returns {Object} 匹配结果
     */
    performExactKeywordMatch(address) {
        let bestMatch = { regionId: null, confidence: 0, matchedKeyword: '', type: '', regionName: '' };
        
        // 遍历所有区域的关键词
        for (const [regionId, regionData] of Object.entries(this.regionKeywordMap)) {
            for (const [keyword, keywordData] of Object.entries(regionData.keywords)) {
                if (address.includes(keyword)) {
                    // 计算最终置信度（关键词置信度 × 位置类型权重）
                    const finalConfidence = keywordData.confidence * this.locationTypeWeights[keywordData.type];
                    
                    if (finalConfidence > bestMatch.confidence) {
                        bestMatch = {
                            regionId: parseInt(regionId),
                            confidence: finalConfidence,
                            matchedKeyword: keyword,
                            type: keywordData.type,
                            regionName: regionData.name,
                            method: 'exact_keyword_match'
                        };
                    }
                }
            }
        }
        
        return bestMatch;
    }
    
    /**
     * @function performFuzzyKeywordMatch - 模糊关键词匹配
     * @param {string} address - 地址文本
     * @returns {Object} 匹配结果
     */
    performFuzzyKeywordMatch(address) {
        let bestMatch = { regionId: null, confidence: 0, matchedKeyword: '', type: '', regionName: '' };
        
        // 使用增强匹配引擎进行模糊匹配
        const enhancedMatcher = new EnhancedMatchingEngine();
        
        for (const [regionId, regionData] of Object.entries(this.regionKeywordMap)) {
            for (const [keyword, keywordData] of Object.entries(regionData.keywords)) {
                // 计算模糊相似度
                const similarity = enhancedMatcher.calculateSimilarity(address, keyword);
                
                if (similarity > 0.6) { // 模糊匹配阈值
                    const finalConfidence = similarity * keywordData.confidence * this.locationTypeWeights[keywordData.type] * 0.8; // 模糊匹配降权
                    
                    if (finalConfidence > bestMatch.confidence) {
                        bestMatch = {
                            regionId: parseInt(regionId),
                            confidence: finalConfidence,
                            matchedKeyword: keyword,
                            type: keywordData.type,
                            regionName: regionData.name,
                            method: 'fuzzy_keyword_match',
                            similarity: similarity
                        };
                    }
                }
            }
        }
        
        return bestMatch;
    }
    
    /**
     * @function performLocationTypeInference - 地理位置类型推理
     * @param {string} address - 地址文本
     * @returns {Object} 匹配结果
     */
    performLocationTypeInference(address) {
        // 检测地址类型
        let detectedType = null;
        let typeConfidence = 0;
        
        for (const [type, pattern] of Object.entries(this.addressPatterns)) {
            if (pattern.test(address)) {
                detectedType = type;
                typeConfidence = 0.7;
                break;
            }
        }
        
        // 基于地址类型推理区域
        if (detectedType === 'airport') {
            // 机场服务通常在主要城市（KL）
            return {
                regionId: 1,
                confidence: 0.6,
                matchedKeyword: 'airport_inference',
                type: 'airport',
                regionName: 'Kl/selangor (KL)',
                method: 'location_type_inference',
                reasoning: '机场服务推理为KL区域'
            };
        }
        
        return { regionId: null, confidence: 0, matchedKeyword: '', type: '', regionName: '' };
    }
    
    /**
     * @function calculateGeographicProximity - 计算地理位置邻近度
     * @param {string} address - 地址文本
     * @param {number} regionId - 区域ID
     * @returns {number} 邻近度评分 (0.0-1.0)
     */
    calculateGeographicProximity(address, regionId) {
        // 简化的地理邻近度计算
        // 在实际应用中，这里可以集成地理编码API
        
        const proximityRules = {
            // KL周边区域
            1: ['selangor', 'putrajaya', 'cyberjaya', 'nilai', 'sepang'],
            // Penang周边区域
            2: ['kedah', 'perak', 'butterworth'],
            // Johor周边区域
            3: ['singapore', 'melaka', 'negeri sembilan'],
            // Sabah周边区域
            4: ['sarawak', 'brunei', 'labuan'],
            // Singapore周边区域
            5: ['johor', 'batam', 'bintan'],
            // Malacca周边区域
            12: ['johor', 'negeri sembilan', 'selangor']
        };
        
        const nearbyAreas = proximityRules[regionId] || [];
        for (const area of nearbyAreas) {
            if (address.includes(area)) {
                return 0.3; // 邻近区域匹配
            }
        }
        
        return 0;
    }
    
    /**
     * @function getDefaultMatch - 获取默认匹配结果
     * @returns {Object} 默认匹配结果
     */
    getDefaultMatch() {
        return {
            regionId: 1, // 默认KL/Selangor
            confidence: 0.3,
            matchedKeyword: 'default',
            type: 'default',
            regionName: 'Kl/selangor (KL)',
            method: 'default_region',
            reasoning: '无明确地址匹配，使用默认区域'
        };
    }
    
    /**
     * @function analyzeAddressComponents - 分析地址组成部分
     * @param {string} address - 地址文本
     * @returns {Object} 地址分析结果
     */
    analyzeAddressComponents(address) {
        const components = {
            hasAirport: this.addressPatterns.airport.test(address),
            hasHotel: this.addressPatterns.hotel.test(address),
            hasMall: this.addressPatterns.mall.test(address),
            hasResidential: this.addressPatterns.residential.test(address),
            hasOffice: this.addressPatterns.office.test(address),
            detectedLanguage: this.detectAddressLanguage(address),
            addressLength: address.length,
            wordCount: address.split(/\s+/).length
        };
        
        return components;
    }
    
    /**
     * @function detectAddressLanguage - 检测地址语言
     * @param {string} address - 地址文本
     * @returns {string} 检测到的语言
     */
    detectAddressLanguage(address) {
        // 中文字符检测
        if (/[\u4e00-\u9fff]/.test(address)) {
            return 'chinese';
        }
        
        // 马来语特征检测
        const malayKeywords = ['jalan', 'lorong', 'taman', 'bandar', 'kampung', 'desa'];
        if (malayKeywords.some(keyword => address.includes(keyword))) {
            return 'malay';
        }
        
        // 默认英语
        return 'english';
    }
    
    /**
     * @function getRegionStatistics - 获取区域匹配统计
     * @returns {Object} 统计信息
     */
    getRegionStatistics() {
        const stats = {
            totalRegions: Object.keys(this.regionKeywordMap).length,
            totalKeywords: 0,
            keywordsByType: {},
            regionCoverage: {}
        };
        
        for (const [regionId, regionData] of Object.entries(this.regionKeywordMap)) {
            const keywordCount = Object.keys(regionData.keywords).length;
            stats.totalKeywords += keywordCount;
            stats.regionCoverage[regionId] = {
                name: regionData.name,
                keywordCount: keywordCount
            };
            
            // 统计关键词类型
            for (const keywordData of Object.values(regionData.keywords)) {
                const type = keywordData.type;
                stats.keywordsByType[type] = (stats.keywordsByType[type] || 0) + 1;
            }
        }
        
        return stats;
    }
}

// 导出到全局对象
if (typeof window !== 'undefined') {
    window.EnhancedMatchingEngine = EnhancedMatchingEngine;
    window.IntelligentLearningEngine = IntelligentLearningEngine;
    window.AddressRegionMatcher = AddressRegionMatcher;
}

logger.info('模块', 'SmartSelection引擎模块加载完成', { version: '4.0.3' }); 