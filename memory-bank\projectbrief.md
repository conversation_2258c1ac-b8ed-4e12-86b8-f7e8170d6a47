# OTA订单处理系统 - 项目概览

## 📋 项目基本信息

**项目名称**: OTA订单处理系统  
**当前版本**: v2.0.1  
**最后更新**: 2024-12-19  
**项目状态**: 🚀 生产就绪，功能完整  
**架构类型**: 纯前端模块化应用 + 三重AI集成  

## 🏗️ 技术架构

### 核心架构
```
前端应用 (HTML5 + JavaScript ES6+)
├── 三重AI服务集成
│   ├── DeepSeek AI (主要LLM)
│   ├── Gemini AI (备用LLM)  
│   └── Google Vision API (图像处理)
└── GoMyHire API (订单管理)
```

### 目录结构
```
/
├── index.html              # 主界面
├── core/                   # 核心模块
│   ├── app.js             # 主应用逻辑 (1800行)
│   ├── config.js          # 统一配置管理 (301行)
│   ├── logger.js          # 日志系统 (650行)
│   ├── prompts.js         # AI提示词配置 (450行)
│   └── smart-selection.js # 智能选择服务 (1550行)
├── services/              # 业务服务
│   ├── api-service.js     # API调用服务 (270行)
│   ├── llm-service.js     # LLM处理服务 (780行)
│   ├── order-parser.js    # 订单解析引擎 (360行)
│   └── image-service.js   # 图像处理服务 (450行)
├── components/            # UI组件
│   └── notification.js    # 通知组件 (350行)
├── assets/               # 静态资源
│   ├── styles.css        # 主样式 (2650行)
│   ├── logger.css        # 日志样式 (550行)
│   └── notification.css  # 通知样式 (370行)
└── memory-bank/          # 项目文档
```

## 🚀 核心功能

### 智能处理能力
- **三重AI架构**: DeepSeek + Gemini + Google Vision
- **OTA类型识别**: 4种处理模式 (自动识别、Chong Dealer、通用模板、其他)
- **故障切换机制**: 30秒超时自动切换AI服务
- **图像OCR识别**: 自动提取图片中的订单信息

### 订单处理功能
- **多输入方式**: 文字输入、图片上传、拖拽上传
- **智能解析**: 日期修正、时间计算、地点标准化
- **智能选择**: 车型、用户、服务类型自动匹配
- **手动编辑**: 完整的订单编辑和验证功能
- **批量创建**: GoMyHire API批量订单创建

### 系统特性
- **实时监控**: AI连接状态指示器
- **完整日志**: 详细的操作记录和错误追踪
- **响应式设计**: 适配桌面、平板、手机
- **模块化架构**: 清晰的代码分离和职责划分

## 📊 性能指标

- **订单识别准确率**: > 95%
- **处理速度**: < 30秒/订单
- **系统可用性**: 99.9% (双AI架构)
- **支持语言**: 中文、英文、混合格式
- **并发处理**: 支持多订单批量处理

## 🔧 配置要求

### API服务配置
```javascript
// core/config.js 中的关键配置
DEEPSEEK_API_KEY: 'sk-xxx',      // 主要LLM服务
GEMINI_API_KEY: 'AIzaSyDxxx',    // 备用LLM服务
GOOGLE_VISION_API_KEY: 'AIzaSyDxxx', // 图像处理
GOMYHIRE_API_BASE: 'https://staging.gomyhire.com.my/api'
```

### 运行环境
- **浏览器**: 现代浏览器 (Chrome, Firefox, Safari, Edge)
- **网络**: 需要网络连接进行API调用
- **部署**: 支持file://协议直接运行，无需服务器

## 🎯 当前重点

### 最近完成 (v2.0.1)
- ✅ 服务类型映射简化优化
- ✅ 前端界面一致性修复
- ✅ 代码逻辑清理和优化

### 开发中
- 🔄 内存管理优化
- 🔄 图片处理性能提升
- 🔄 用户体验细节完善

### 规划中
- 📋 批量操作功能
- 📋 数据分析面板
- 📋 移动端优化
- 📋 离线模式支持

## 🔗 相关文档

- [activeContext.md](activeContext.md) - 当前工作重点
- [progress.md](progress.md) - 开发进度
- [techContext.md](techContext.md) - 技术实现细节
- [systemPatterns.md](systemPatterns.md) - 架构模式
- [naming-conventions.md](naming-conventions.md) - 命名规范

---
*项目状态: �� 生产就绪 | 维护状态: 活跃开发* 