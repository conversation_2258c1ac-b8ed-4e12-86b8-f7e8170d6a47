/**
 * @file smart-selection.js - 智能选择服务主模块（五维扩展版）
 * @description 实现五维智能选择功能：用户+服务+车型+区域+语言
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-08
 * @version v4.2.0
 * @dependencies accuracy-calculator.js, api-sync-manager.js, smart-selection-engine.js
 */

/**
 * @class SmartSelectionService - 五维智能选择服务主类
 * @description 实现基于订单内容的五维智能ID选择功能，包括车型、服务类型、用户、区域、语言识别
 */
class SmartSelectionService {
    constructor() {
        this.config = window.SYSTEM_CONFIG?.SMART_SELECTION || {};
        this.logger = window.logger || console;
        this.initialized = false;
        
        // 初始化增强组件 v4.2.0
        this.enhancedMatchingEngine = new EnhancedMatchingEngine();
        this.learningEngine = new IntelligentLearningEngine();
        this.accuracyCalculator = new DynamicAccuracyCalculator(this.learningEngine);
        
        // 初始化动态API同步管理器
        this.apiSyncManager = new DynamicApiSyncManager();
        
        // Profile配置缓存
        this.currentProfileConfig = null;
        
        // OTA预设配置缓存
        this.currentOtaPreset = null;
        this.restrictedRegions = null;
        this.preferredRegions = null;
        this.forceBackendUser = null;
        this.preferredServiceType = null;
        this.carTypePreference = null;
        
        // 默认选择值和权重 (用于OTA预设)
        this.defaultSelections = {
            backendUser: null,
            subCategory: null,
            carType: null,
            region: null,
            languages: []
        };
        
        this.selectionWeights = {
            user: {},
            service: {},
            vehicle: {},
            region: {},
            languages: {}
        };
        
        // 算法版本信息
        this.version = '4.2.0';
        this.algorithmEnhancements = {
            fuzzyMatching: true,
            synonymSupport: true,
            learningEngine: true,
            dynamicScoring: true,
            contextAnalysis: true,
            dynamicApiSync: true,
            pinyinMatching: true,
            abbreviationMatch: true,
            semanticMatching: true,
            soundexMatching: true,
            fiveDimensionalSelection: true,  // 新增：五维选择
            regionMatching: true,            // 新增：区域匹配
            languageRecommendation: true,    // 新增：语言推荐
            profileIntegration: true         // 新增：Profile集成
        };
        
        // 添加API同步状态监听器
        this.apiSyncManager.addStatusListener((status, data) => {
            this.handleApiSyncStatusChange(status, data);
        });
        
        // 初始化映射表
        this.initializeMappingTables();
    }
    
    /**
     * @function initializeMappingTables - 初始化五维映射表
     */
    initializeMappingTables() {
        // 车型识别映射表 - 基于真实API数据
        this.vehicleTypeMapping = {
            // 基于乘客数量的车型映射
            passengerCount: {
                1: { type: 'compact', id: 5, name: '5 Seater', seats: 4 },
                2: { type: 'compact', id: 5, name: '5 Seater', seats: 4 },
                3: { type: 'compact', id: 5, name: '5 Seater', seats: 4 },
                4: { type: 'mpv', id: 15, name: '7 Seater MPV', seats: 6 },
                5: { type: 'mpv', id: 15, name: '7 Seater MPV', seats: 6 },
                6: { type: 'mpv', id: 15, name: '7 Seater MPV', seats: 6 },
                7: { type: 'van', id: 20, name: '10 Seater MPV / Van', seats: 9 },
                8: { type: 'van', id: 20, name: '10 Seater MPV / Van', seats: 9 },
                9: { type: 'van', id: 20, name: '10 Seater MPV / Van', seats: 9 },
                10: { type: 'van', id: 20, name: '10 Seater MPV / Van', seats: 9 }
            },
            // 增强的车型关键词映射
            keywords: {
                // 5座车关键词 - ID: 5
                'sedan': { id: 5, name: '5 Seater', category: 'sedan', seats: 4 },
                'economy': { id: 5, name: '5 Seater', category: 'sedan', seats: 4 },
                'compact': { id: 5, name: '5 Seater', category: 'sedan', seats: 4 },
                '经济': { id: 5, name: '5 Seater', category: 'sedan', seats: 4 },
                '轿车': { id: 5, name: '5 Seater', category: 'sedan', seats: 4 },
                '小车': { id: 5, name: '5 Seater', category: 'sedan', seats: 4 },
                '5座': { id: 5, name: '5 Seater', category: 'sedan', seats: 4 },
                '五座': { id: 5, name: '5 Seater', category: 'sedan', seats: 4 },
                
                // 7座MPV关键词 - ID: 15
                'mpv': { id: 15, name: '7 Seater MPV', category: 'mpv', seats: 6 },
                '商务': { id: 15, name: '7 Seater MPV', category: 'mpv', seats: 6 },
                '7座': { id: 15, name: '7 Seater MPV', category: 'mpv', seats: 6 },
                '七座': { id: 15, name: '7 Seater MPV', category: 'mpv', seats: 6 },
                
                // 10座车关键词 - ID: 20
                'van': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 },
                '面包': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 },
                '10座': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 },
                '十座': { id: 20, name: '10 Seater MPV / Van', category: 'van', seats: 9 }
            }
        };
        
        // 服务类型映射表
        this.serviceTypeMapping = {
            // 接机服务 - ID: 2
            '接机': { id: 2, name: 'Pickup', category: 'Airport' },
            'pickup': { id: 2, name: 'Pickup', category: 'Airport' },
            'airport_pickup': { id: 2, name: 'Pickup', category: 'Airport' },
            '机场接': { id: 2, name: 'Pickup', category: 'Airport' },
            
            // 送机服务 - ID: 3
            '送机': { id: 3, name: 'Dropoff', category: 'Airport' },
            'dropoff': { id: 3, name: 'Dropoff', category: 'Airport' },
            'airport_dropoff': { id: 3, name: 'Dropoff', category: 'Airport' },
            '机场送': { id: 3, name: 'Dropoff', category: 'Airport' },
            
            // 包车服务 - ID: 4
            '包车': { id: 4, name: 'Charter', category: 'Chartered' },
            'charter': { id: 4, name: 'Charter', category: 'Chartered' },
            'chartered': { id: 4, name: 'Charter', category: 'Chartered' },
            '包租': { id: 4, name: 'Charter', category: 'Chartered' },
            
            // 默认服务
            'default': { id: 2, name: 'Pickup', category: 'default' }
        };
        
        // 后台用户映射表
        this.backendUserMapping = {
            // 默认分配给首位用户（自动选择）
            'default': { id: 'auto', name: 'Auto Select First User', role: 'auto', phone: '' },
            'auto': { id: 'auto', name: 'Auto Select First User', role: 'auto', phone: '' },
            'first': { id: 'auto', name: 'Auto Select First User', role: 'auto', phone: '' }
        };
        
        // 新增：行驶区域映射表
        this.drivingRegionMapping = {
            // 基于地址关键词的区域映射
            keywords: {
                // KL/Selangor - ID: 1
                'kuala lumpur': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.9 },
                'kl': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.9 },
                'selangor': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.9 },
                'petaling jaya': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.9 },
                'subang': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.8 },
                'klia': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.9 },
                'klia2': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.9 },
                'shah alam': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.8 },
                'klang': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.8 },
                '吉隆坡': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.9 },
                '雪兰莪': { id: 1, name: 'Kl/selangor (KL)', confidence: 0.9 },
                
                // Penang - ID: 2
                'penang': { id: 2, name: 'Penang (PNG)', confidence: 0.9 },
                'georgetown': { id: 2, name: 'Penang (PNG)', confidence: 0.9 },
                'butterworth': { id: 2, name: 'Penang (PNG)', confidence: 0.8 },
                'penang airport': { id: 2, name: 'Penang (PNG)', confidence: 0.9 },
                '槟城': { id: 2, name: 'Penang (PNG)', confidence: 0.9 },
                
                // Johor - ID: 3
                'johor': { id: 3, name: 'Johor (JB)', confidence: 0.9 },
                'johor bahru': { id: 3, name: 'Johor (JB)', confidence: 0.9 },
                'jb': { id: 3, name: 'Johor (JB)', confidence: 0.9 },
                'senai airport': { id: 3, name: 'Johor (JB)', confidence: 0.9 },
                '新山': { id: 3, name: 'Johor (JB)', confidence: 0.9 },
                '柔佛': { id: 3, name: 'Johor (JB)', confidence: 0.9 },
                
                // Sabah - ID: 4
                'sabah': { id: 4, name: 'Sabah (SBH)', confidence: 0.9 },
                'kota kinabalu': { id: 4, name: 'Sabah (SBH)', confidence: 0.9 },
                'kk': { id: 4, name: 'Sabah (SBH)', confidence: 0.8 },
                '沙巴': { id: 4, name: 'Sabah (SBH)', confidence: 0.9 },
                '亚庇': { id: 4, name: 'Sabah (SBH)', confidence: 0.9 },
                
                // Singapore - ID: 5
                'singapore': { id: 5, name: 'Singapore (SG)', confidence: 0.9 },
                'changi': { id: 5, name: 'Singapore (SG)', confidence: 0.9 },
                '新加坡': { id: 5, name: 'Singapore (SG)', confidence: 0.9 },
                
                // Malacca - ID: 12
                'malacca': { id: 12, name: 'Malacca (MLK)', confidence: 0.9 },
                'melaka': { id: 12, name: 'Malacca (MLK)', confidence: 0.9 },
                '马六甲': { id: 12, name: 'Malacca (MLK)', confidence: 0.9 }
            },
            // 默认区域
            default: { id: 1, name: 'Kl/selangor (KL)', confidence: 0.5 }
        };
        
        // 新增：语言映射表
        this.languageMapping = {
            // 基于客户姓名特征的语言识别
            namePatterns: {
                // 中文姓名特征
                chinese: {
                    pattern: /[\u4e00-\u9fff]/,
                    languages: [4], // Chinese
                    confidence: 0.9
                },
                // 马来姓名特征
                malay: {
                    patterns: ['bin ', 'binti ', 'ahmad', 'muhammad', 'abdul', 'siti'],
                    languages: [3], // Malay
                    confidence: 0.8
                },
                // 印度姓名特征
                indian: {
                    patterns: ['kumar', 'singh', 'raj', 'devi', 'lal'],
                    languages: [2], // English (通常印度客户使用英文)
                    confidence: 0.7
                }
            },
            // 基于订单文本的语言检测
            textPatterns: {
                chinese: {
                    pattern: /[\u4e00-\u9fff]/,
                    languages: [4],
                    confidence: 0.8
                },
                malay: {
                    keywords: ['terima kasih', 'selamat', 'jalan', 'bandar'],
                    languages: [3],
                    confidence: 0.7
                }
            },
            // 默认语言组合
            default: {
                languages: [2, 4], // English + Chinese
                confidence: 0.5
            }
        };
    }
    
    /**
     * @function initialize - 初始化智能选择服务
     * @returns {Promise<boolean>} 初始化结果
     */
    async initialize() {
        try {
            this.logger.info('SmartSelection', '初始化智能选择服务');
            
            // 从应用状态获取最新的选择器数据
            await this.updateMappingFromAppState();
            
            this.initialized = true;
            this.logger.info('SmartSelection', '智能选择服务初始化完成');
            
            return true;
        } catch (error) {
            this.logger.error('SmartSelection', '初始化失败', error);
            return false;
        }
    }
    
    /**
     * @function updateMappingFromAppState - 从应用状态更新映射表
     */
    async updateMappingFromAppState() {
        try {
            const app = window.app;
            if (!app || !app.appState) {
                this.logger.warn('SmartSelection', '应用状态不可用，使用默认映射');
                return;
            }
            
            // 动态车型映射系统
            if (app.appState.carTypes && app.appState.carTypes.length > 0) {
                const vehicleMappingStrategy = this.createDynamicVehicleMapping(app.appState.carTypes);
                this.vehicleTypeMapping = vehicleMappingStrategy.mapping;
                
                this.logger.info('SmartSelection', '动态车型映射已更新', {
                    strategy: vehicleMappingStrategy.strategy,
                    totalVehicles: app.appState.carTypes.length,
                    passengerRanges: Object.keys(this.vehicleTypeMapping.passengerCount).length,
                    keywordMappings: Object.keys(this.vehicleTypeMapping.keywords).length,
                    vehicleCategories: vehicleMappingStrategy.categories
                });
            }
            
            // 动态服务类型映射系统
            if (app.appState.subCategories && app.appState.subCategories.length > 0) {
                const serviceMappingStrategy = this.createDynamicServiceMapping(app.appState.subCategories);
                this.serviceTypeMapping = serviceMappingStrategy.mapping;
                
                this.logger.info('SmartSelection', '动态服务类型映射已更新', {
                    strategy: serviceMappingStrategy.strategy,
                    totalServices: app.appState.subCategories.length,
                    serviceMappings: Object.keys(this.serviceTypeMapping).length,
                    serviceCategories: serviceMappingStrategy.categories,
                    defaultService: serviceMappingStrategy.defaultService
                });
            }
            
            // 动态后台用户映射系统
            if (app.appState.backendUsers && app.appState.backendUsers.length > 0) {
                const userMappingStrategy = this.createDynamicUserMapping(app.appState.backendUsers);
                this.backendUserMapping = userMappingStrategy.mapping;
                
                this.logger.info('SmartSelection', '动态后台用户映射已更新', {
                    strategy: userMappingStrategy.strategy,
                    defaultUserId: userMappingStrategy.defaultUser.id,
                    defaultUserName: userMappingStrategy.defaultUser.name,
                    defaultUserRole: userMappingStrategy.defaultUser.role,
                    totalUsers: app.appState.backendUsers.length,
                    mappingKeys: Object.keys(this.backendUserMapping).length,
                    userProfiles: userMappingStrategy.userProfiles
                });
            }
            
            this.logger.debug('SmartSelection', '所有映射表更新完成', {
                vehicleTypes: Object.keys(this.vehicleTypeMapping.passengerCount).length,
                serviceTypes: Object.keys(this.serviceTypeMapping).length,
                backendUsers: Object.keys(this.backendUserMapping).length
            });
            
        } catch (error) {
            this.logger.error('SmartSelection', '更新映射表失败', error);
        }
    }
    
    /**
     * @function handleApiSyncStatusChange - 处理API同步状态变化
     * @param {string} status - 同步状态
     * @param {object} data - 同步数据
     */
    handleApiSyncStatusChange(status, data) {
        this.logger.debug('SmartSelection', 'API同步状态变化', { status, data });

        if (status === 'sync_completed') {
            // API同步完成，更新映射表
            this.updateMappingFromAppState();
        }
    }

    /**
     * @function createDynamicVehicleMapping - 创建动态车型映射策略
     * @param {Array} carTypes - API返回的车型列表
     * @returns {Object} 车型映射策略对象
     */
    createDynamicVehicleMapping(carTypes) {
        try {
            // 车型分类模式识别
            const vehiclePatterns = {
                compact: /compact|小型|经济|economy/i,
                comfort: /comfort|舒适|standard/i,
                luxury: /luxury|豪华|premium|alphard|velfire|vellfire/i,
                suv: /suv|越野|sport.*utility/i,
                mpv: /mpv|multi.*purpose|商务/i,
                van: /van|面包|客车/i,
                bus: /bus|巴士|大巴|coach/i
            };

            const mapping = {
                passengerCount: {},
                keywords: {}
            };

            const categories = new Set();
            let strategy = 'dynamic_api_based';

            // 分析API返回的车型数据
            carTypes.forEach(carType => {
                const name = carType.name.toLowerCase();
                const id = carType.id;
                const seats = carType.seats || this.extractSeatsFromName(carType.name);

                // 根据座位数创建乘客数量映射
                if (seats) {
                    for (let i = 1; i <= seats; i++) {
                        if (!mapping.passengerCount[i] || mapping.passengerCount[i].seats < seats) {
                            mapping.passengerCount[i] = {
                                type: this.categorizeVehicle(name, vehiclePatterns),
                                id: id,
                                name: carType.name,
                                seats: seats
                            };
                        }
                    }
                }

                // 创建关键词映射
                const category = this.categorizeVehicle(name, vehiclePatterns);
                categories.add(category);
                
                mapping.keywords[name] = {
                    id: id,
                    name: carType.name,
                    category: category,
                    seats: seats
                };

                // 添加中文关键词映射
                this.addChineseKeywords(mapping.keywords, carType, category);
            });

            return {
                mapping: mapping,
                strategy: strategy,
                categories: Array.from(categories)
            };

        } catch (error) {
            this.logger.error('SmartSelection', '创建动态车型映射失败', error);
            return { mapping: this.vehicleTypeMapping, strategy: 'fallback', categories: [] };
        }
    }

    /**
     * @function createDynamicServiceMapping - 创建动态服务映射策略
     * @param {Array} subCategories - API返回的服务类型列表
     * @returns {Object} 服务映射策略对象
     */
    createDynamicServiceMapping(subCategories) {
        try {
            const mapping = {};
            const categories = new Set();
            let defaultService = null;
            let strategy = 'dynamic_api_based';

            subCategories.forEach(subCategory => {
                const name = subCategory.name.toLowerCase();
                const id = subCategory.id;
                const category = subCategory.category || 'default';

                categories.add(category);

                // 设置默认服务
                if (!defaultService || name.includes('default') || name.includes('pickup')) {
                    defaultService = { id: id, name: subCategory.name, category: category };
                }

                // 创建服务映射
                mapping[name] = { id: id, name: subCategory.name, category: category };
                
                // 添加中文和英文关键词映射
                this.addServiceKeywords(mapping, subCategory, category);
            });

            // 设置默认映射
            mapping['default'] = defaultService || { id: 1, name: 'Default Service', category: 'default' };

            return {
                mapping: mapping,
                strategy: strategy,
                categories: Array.from(categories),
                defaultService: defaultService
            };

        } catch (error) {
            this.logger.error('SmartSelection', '创建动态服务映射失败', error);
            return { mapping: this.serviceTypeMapping, strategy: 'fallback', categories: [] };
        }
    }

    /**
     * @function createDynamicUserMapping - 创建动态用户映射策略
     * @param {Array} backendUsers - API返回的后台用户列表
     * @returns {Object} 用户映射策略对象
     */
    createDynamicUserMapping(backendUsers) {
        try {
            const mapping = {};
            const userProfiles = [];
            let defaultUser = null;
            let strategy = 'dynamic_api_based';

            backendUsers.forEach(user => {
                const name = user.name.toLowerCase();
                const role = user.role_id || user.role || 'operator';
                const id = user.id;

                // 设置默认用户（优先选择super_admin）
                if (!defaultUser || role.includes('super') || role.includes('admin')) {
                    defaultUser = { id: id, name: user.name, role: role };
                }

                // 创建用户映射
                mapping[name] = { id: id, name: user.name, role: role, phone: user.phone };
                mapping[role] = { id: id, name: user.name, role: role, phone: user.phone };

                userProfiles.push({
                    id: id,
                    name: user.name,
                    role: role,
                    phone: user.phone
                });
            });

            // 设置默认映射
            mapping['default'] = defaultUser || { id: 1, name: 'Default User', role: 'admin' };
            mapping['fallback'] = mapping['default'];
            mapping['gomyhire'] = mapping['default'];
            mapping['chong_dealer'] = mapping['default'];

            return {
                mapping: mapping,
                strategy: strategy,
                defaultUser: defaultUser,
                userProfiles: userProfiles
            };

        } catch (error) {
            this.logger.error('SmartSelection', '创建动态用户映射失败', error);
            return { mapping: this.backendUserMapping, strategy: 'fallback', defaultUser: null, userProfiles: [] };
        }
    }

    /**
     * @function selectCarType - 智能车型选择
     * @param {Object} orderData - 订单数据
     * @returns {Object} 选择结果
     */
    selectCarType(orderData) {
        try {
            const context = {
                orderData: orderData,
                timestamp: Date.now(),
                method: 'selectCarType'
            };

            // 1. 优先尝试Profile车型规则
            const profileDefault = this.getProfileDefaults('carType', orderData);
            if (profileDefault && profileDefault.id) {
                const carTypes = this.appState.carTypes;
                const carType = carTypes?.find(ct => ct.id === profileDefault.id);
                if (carType) {
                    const result = {
                        car_type_id: profileDefault.id,
                        confidence: profileDefault.confidence,
                        method: 'profile_car_type_rule',
                        reasoning: profileDefault.reason,
                        car_type_name: carType.type
                    };

                    this.learningEngine.recordSelection({
                        type: 'car_type',
                        input: orderData,
                        output: result,
                        context: context,
                        timestamp: Date.now()
                    });

                    this.logger.info('SmartSelection', '车型选择完成（Profile规则）', {
                        selected_id: result.car_type_id,
                        confidence: result.confidence,
                        method: result.method,
                        reasoning: result.reasoning
                    });

                    return result;
                }
            }

            // 2. 多策略智能匹配
            const strategies = [
                () => this.selectByPassengerCount(orderData),
                () => this.selectByVehicleKeywords(orderData),
                () => this.selectByLuggageCount(orderData),
                () => this.selectByServiceType(orderData)
            ];

            let bestMatch = null;
            let highestScore = 0;

            strategies.forEach((strategy, index) => {
                try {
                    const result = strategy();
                    if (result && result.confidence > highestScore) {
                        bestMatch = result;
                        highestScore = result.confidence;
                        bestMatch.method = `strategy_${index + 1}`;
                    }
                } catch (error) {
                    this.logger.warn('SmartSelection', `车型选择策略${index + 1}失败`, error);
                }
            });

            // 如果没有匹配结果，使用默认值
            if (!bestMatch) {
                bestMatch = {
                    car_type_id: 5, // Compact 5 Seater
                    confidence: 0.3,
                    method: 'default_fallback',
                    reasoning: '使用默认车型'
                };
            }

            // 记录学习数据
            this.learningEngine.recordSelection({
                type: 'car_type',
                input: orderData,
                output: bestMatch,
                context: context,
                timestamp: Date.now()
            });

            this.logger.info('SmartSelection', '车型选择完成', {
                selected_id: bestMatch.car_type_id,
                confidence: bestMatch.confidence,
                method: bestMatch.method,
                reasoning: bestMatch.reasoning
            });

            return bestMatch;

        } catch (error) {
            this.logger.error('SmartSelection', '车型选择失败', error);
            return {
                car_type_id: 5,
                confidence: 0.1,
                method: 'error_fallback',
                reasoning: '选择过程出错，使用默认值'
            };
        }
    }

    /**
     * @function selectSubCategory - 智能服务类型选择
     * @param {Object} orderData - 订单数据
     * @returns {Object} 选择结果
     */
    selectSubCategory(orderData) {
        try {
            const context = {
                orderData: orderData,
                timestamp: Date.now(),
                method: 'selectSubCategory'
            };

            // 1. 检查Profile服务类型偏好
            const profileDefault = this.getProfileDefaults('subCategory', orderData);
            if (profileDefault && profileDefault.id) {
                const subCategories = this.appState.subCategories;
                const subCategory = subCategories?.find(sc => sc.id === profileDefault.id);
                if (subCategory) {
                    const result = {
                        sub_category_id: profileDefault.id,
                        confidence: profileDefault.confidence,
                        method: 'profile_service_preference',
                        reasoning: profileDefault.reason,
                        sub_category_name: subCategory.name
                    };

                    this.learningEngine.recordSelection({
                        type: 'sub_category',
                        input: orderData,
                        output: result,
                        context: context,
                        timestamp: Date.now()
                    });

                    this.logger.info('SmartSelection', '服务类型选择完成（Profile偏好）', {
                        selected_id: result.sub_category_id,
                        confidence: result.confidence,
                        method: result.method,
                        reasoning: result.reasoning
                    });

                    return result;
                }
            }

            // 2. 基于订单类型自动匹配服务类型（新增优化逻辑）
            const smartServiceMatch = this.smartServiceTypeMatch(orderData);
            if (smartServiceMatch && smartServiceMatch.confidence > 0.8) {
                const result = {
                    sub_category_id: smartServiceMatch.id,
                    confidence: smartServiceMatch.confidence,
                    method: 'smart_service_type_auto_match',
                    reasoning: smartServiceMatch.reasoning,
                    sub_category_name: smartServiceMatch.name
                };

                this.learningEngine.recordSelection({
                    type: 'sub_category',
                    input: orderData,
                    output: result,
                    context: context,
                    timestamp: Date.now()
                });

                this.logger.info('SmartSelection', '服务类型选择完成（智能匹配）', {
                    selected_id: result.sub_category_id,
                    confidence: result.confidence,
                    method: result.method,
                    reasoning: result.reasoning
                });

                return result;
            }

            // 3. 服务类型关键词匹配（备用逻辑）
            const serviceKeywords = [
                orderData.pickup || '',
                orderData.destination || '',
                orderData.extra_requirement || '',
                orderData.flight_info || ''
            ].join(' ').toLowerCase();

            let bestMatch = null;
            let highestScore = 0;

            // 遍历服务类型映射
            Object.entries(this.serviceTypeMapping).forEach(([keyword, service]) => {
                const matchResult = this.enhancedMatchingEngine.enhancedKeywordMatch(serviceKeywords, keyword);
                
                if (matchResult.found && matchResult.score > highestScore) {
                    bestMatch = {
                        sub_category_id: service.id,
                        confidence: matchResult.score,
                        method: 'keyword_matching',
                        reasoning: `匹配关键词: ${keyword}`,
                        matchDetails: matchResult
                    };
                    highestScore = matchResult.score;
                }
            });

            // 如果没有匹配结果，使用默认值
            if (!bestMatch) {
                const defaultService = this.serviceTypeMapping['default'];
                bestMatch = {
                    sub_category_id: defaultService.id,
                    confidence: 0.3,
                    method: 'default_fallback',
                    reasoning: '使用默认服务类型'
                };
            }

            // 记录学习数据
            this.learningEngine.recordSelection({
                type: 'sub_category',
                input: orderData,
                output: bestMatch,
                context: context,
                timestamp: Date.now()
            });

            this.logger.info('SmartSelection', '服务类型选择完成', {
                selected_id: bestMatch.sub_category_id,
                confidence: bestMatch.confidence,
                method: bestMatch.method,
                reasoning: bestMatch.reasoning
            });

            return bestMatch;

        } catch (error) {
            this.logger.error('SmartSelection', '服务类型选择失败', error);
            return {
                sub_category_id: 7,
                confidence: 0.1,
                method: 'error_fallback',
                reasoning: '选择过程出错，使用默认值'
            };
        }
    }

    /**
     * @function selectBackendUser - 智能后台用户选择
     * @param {Object} orderData - 订单数据
     * @returns {Object} 选择结果
     */
    selectBackendUser(orderData) {
        try {
            const context = {
                orderData: orderData,
                timestamp: Date.now(),
                method: 'selectBackendUser'
            };

            // 0. 优先使用Profile默认设置
            const profileDefault = this.getProfileDefaults('backendUser', orderData);
            if (profileDefault && profileDefault.id) {
                const users = this.appState.backendUsers;
                const user = users?.find(u => u.id === profileDefault.id);
                if (user) {
                    const result = {
                        incharge_by_backend_user_id: profileDefault.id,
                        confidence: profileDefault.confidence,
                        method: 'profile_default',
                        reasoning: profileDefault.reason,
                        selectedUser: user
                    };

                    this.learningEngine.recordSelection({
                        type: 'backend_user',
                        input: orderData,
                        output: result,
                        context: context,
                        timestamp: Date.now()
                    });

                    this.logger.info('SmartSelection', '后台用户选择完成（Profile默认）', {
                        selected_id: result.incharge_by_backend_user_id,
                        confidence: result.confidence,
                        method: result.method,
                        user_name: user.name,
                        user_role: user.role
                    });

                    return result;
                }
            }

            // 1. 自动选择首位可用用户ID（新增优化逻辑）
            const availableUsers = this.appState.backendUsers;
            if (availableUsers && availableUsers.length > 0) {
                // 按ID升序排序，选择首位用户
                const sortedUsers = availableUsers.sort((a, b) => a.id - b.id);
                const firstAvailableUser = sortedUsers[0];
                
                const result = {
                    incharge_by_backend_user_id: firstAvailableUser.id,
                    confidence: 0.9,
                    method: 'auto_select_first_user',
                    reasoning: `自动选择首位用户ID: ${firstAvailableUser.id} (${firstAvailableUser.name})`,
                    selectedUser: firstAvailableUser
                };

                this.learningEngine.recordSelection({
                    type: 'backend_user',
                    input: orderData,
                    output: result,
                    context: context,
                    timestamp: Date.now()
                });

                this.logger.info('SmartSelection', '后台用户选择完成（首位用户）', {
                    selected_id: result.incharge_by_backend_user_id,
                    confidence: result.confidence,
                    method: result.method,
                    user_name: firstAvailableUser.name,
                    user_role: firstAvailableUser.role || 'admin'
                });

                return result;
            }

            // 2. 根据OTA类型选择用户（备用逻辑）
            const otaType = orderData.ota || 'default';
            let selectedUser = this.backendUserMapping[otaType] || this.backendUserMapping['default'];

            const result = {
                incharge_by_backend_user_id: selectedUser.id,
                confidence: 0.7,
                method: 'ota_type_mapping_fallback',
                reasoning: `根据OTA类型 ${otaType} 选择用户（首位用户不可用时的备用选择）`,
                selectedUser: selectedUser
            };

            // 记录学习数据
            this.learningEngine.recordSelection({
                type: 'backend_user',
                input: orderData,
                output: result,
                context: context,
                timestamp: Date.now()
            });

            this.logger.info('SmartSelection', '后台用户选择完成', {
                selected_id: result.incharge_by_backend_user_id,
                confidence: result.confidence,
                method: result.method,
                user_name: selectedUser.name,
                user_role: selectedUser.role
            });

            return result;

        } catch (error) {
            this.logger.error('SmartSelection', '后台用户选择失败', error);
            return {
                incharge_by_backend_user_id: 1,
                confidence: 0.1,
                method: 'error_fallback',
                reasoning: '选择过程出错，使用默认用户'
            };
        }
    }

    /**
     * @function selectDrivingRegion - 智能行驶区域选择（新增）
     * @param {Object} orderData - 订单数据
     * @returns {Object} 选择结果
     */
    selectDrivingRegion(orderData) {
        try {
            this.logger.debug('SmartSelection', '开始区域智能选择', { orderData });
            
            const context = {
                orderData: orderData,
                timestamp: Date.now(),
                method: 'selectDrivingRegion'
            };

            // 1. 地址关键词匹配（高优先级）
            const addressRegion = this.matchRegionByAddress(orderData);
            if (addressRegion.confidence > 0.8) {
                const result = {
                    driving_region_id: addressRegion.id,
                    confidence: addressRegion.confidence,
                    method: 'address_keyword_match',
                    reasoning: `地址匹配: ${addressRegion.matchedKeyword}`,
                    region_name: addressRegion.name
                };

                // 记录学习数据
                this.learningEngine.recordSelection({
                    type: 'driving_region',
                    input: orderData,
                    output: result,
                    context: context,
                    timestamp: Date.now()
                });

                this.logger.info('SmartSelection', '区域选择完成（地址匹配）', result);
                return result;
            }
            
            // 2. 服务类型关联匹配
            const serviceRegion = this.matchRegionByService(orderData);
            if (serviceRegion.confidence > 0.6) {
                const result = {
                    driving_region_id: serviceRegion.id,
                    confidence: serviceRegion.confidence,
                    method: 'service_type_match',
                    reasoning: `服务类型关联: ${serviceRegion.reasoning}`,
                    region_name: serviceRegion.name
                };

                this.learningEngine.recordSelection({
                    type: 'driving_region',
                    input: orderData,
                    output: result,
                    context: context,
                    timestamp: Date.now()
                });

                this.logger.info('SmartSelection', '区域选择完成（服务匹配）', result);
                return result;
            }

            // 3. Profile默认区域（中等优先级）
            const profileDefault = this.getProfileDefaults('region', orderData);
            if (profileDefault && profileDefault.id) {
                const regions = this.appState.drivingRegions;
                const region = regions?.find(r => r.id === profileDefault.id);
                if (region) {
                    const result = {
                        driving_region_id: profileDefault.id,
                        confidence: profileDefault.confidence,
                        method: 'profile_default',
                        reasoning: profileDefault.reason,
                        region_name: region.name
                    };

                    this.learningEngine.recordSelection({
                        type: 'driving_region',
                        input: orderData,
                        output: result,
                        context: context,
                        timestamp: Date.now()
                    });

                    this.logger.info('SmartSelection', '区域选择完成（Profile默认）', result);
                    return result;
                }
            }
            
            // 4. 默认区域（最常用的KL/Selangor）
            const defaultRegion = this.getDefaultRegion();
            const result = {
                driving_region_id: defaultRegion.id,
                confidence: defaultRegion.confidence,
                method: 'default_region',
                reasoning: '使用默认区域（KL/Selangor）',
                region_name: defaultRegion.name
            };

            this.learningEngine.recordSelection({
                type: 'driving_region',
                input: orderData,
                output: result,
                context: context,
                timestamp: Date.now()
            });

            this.logger.info('SmartSelection', '区域选择完成（默认）', result);
            return result;
            
        } catch (error) {
            this.logger.error('SmartSelection', '区域选择失败', error);
            return this.getDefaultRegion();
        }
    }

    /**
     * @function selectLanguages - 智能语言选择（新增）
     * @param {Object} orderData - 订单数据
     * @returns {Object} 选择结果
     */
    selectLanguages(orderData) {
        try {
            this.logger.debug('SmartSelection', '开始语言智能选择', { orderData });
            
            const context = {
                orderData: orderData,
                timestamp: Date.now(),
                method: 'selectLanguages'
            };

            const languageRecommendations = [];
            
            // 1. 客户姓名语言特征识别
            const nameLanguages = this.detectCustomerNameLanguage(orderData.customer_name);
            if (nameLanguages.length > 0) {
                languageRecommendations.push(...nameLanguages);
            }
            
            // 2. 订单文本语言检测
            const textLanguages = this.detectOrderTextLanguage(orderData);
            if (textLanguages.length > 0) {
                languageRecommendations.push(...textLanguages);
            }
            
            // 3. 地理位置语言偏好
            const locationLanguages = this.detectLocationLanguage(orderData);
            if (locationLanguages.length > 0) {
                languageRecommendations.push(...locationLanguages);
            }
            
            // 4. 综合推荐（去重和权重计算）
            let finalRecommendation = this.calculateLanguageRecommendation(languageRecommendations);
            
            // 5. 如果智能检测置信度较低，使用Profile默认设置
            if (finalRecommendation.confidence < 0.7) {
                const profileDefault = this.getProfileDefaults('languages', orderData);
                if (profileDefault && profileDefault.ids && profileDefault.ids.length > 0) {
                    const languageNames = this.getLanguageNames(profileDefault.ids);
                    finalRecommendation = {
                        languages: profileDefault.ids,
                        confidence: profileDefault.confidence,
                        method: 'profile_default',
                        reasoning: profileDefault.reason,
                        names: languageNames
                    };
                    
                    this.logger.info('SmartSelection', '使用Profile默认语言设置', {
                        languages: profileDefault.ids,
                        names: languageNames
                    });
                }
            }
            
            const result = {
                languages_id_array: finalRecommendation.languages,
                confidence: finalRecommendation.confidence,
                method: finalRecommendation.method,
                reasoning: finalRecommendation.reasoning,
                language_names: finalRecommendation.names
            };

            // 记录学习数据
            this.learningEngine.recordSelection({
                type: 'languages',
                input: orderData,
                output: result,
                context: context,
                timestamp: Date.now()
            });

            this.logger.info('SmartSelection', '语言选择完成', result);
            return result;
            
        } catch (error) {
            this.logger.error('SmartSelection', '语言选择失败', error);
            return this.getDefaultLanguages();
        }
    }

    /**
     * @function matchRegionByAddress - 地址-区域匹配算法
     * @param {Object} orderData - 订单数据
     * @returns {Object} 匹配结果
     */
    matchRegionByAddress(orderData) {
        const { pickup, destination } = orderData;
        const fullAddress = `${pickup || ''} ${destination || ''}`.toLowerCase();
        
        let bestMatch = { id: null, confidence: 0, name: '', matchedKeyword: '' };
        
        // 遍历所有区域关键词
        for (const [keyword, region] of Object.entries(this.drivingRegionMapping.keywords)) {
            if (fullAddress.includes(keyword.toLowerCase())) {
                if (region.confidence > bestMatch.confidence) {
                    bestMatch = {
                        id: region.id,
                        confidence: region.confidence,
                        name: region.name,
                        matchedKeyword: keyword
                    };
                }
            }
        }
        
        return bestMatch;
    }

    /**
     * @function matchRegionByService - 服务类型关联区域匹配
     * @param {Object} orderData - 订单数据
     * @returns {Object} 匹配结果
     */
    matchRegionByService(orderData) {
        // 机场服务通常在主要城市
        const serviceText = `${orderData.pickup || ''} ${orderData.destination || ''}`.toLowerCase();
        
        if (serviceText.includes('airport') || serviceText.includes('机场')) {
            return {
                id: 1, // KL/Selangor (主要机场区域)
                confidence: 0.7,
                name: 'Kl/selangor (KL)',
                reasoning: '机场服务默认KL区域'
            };
        }
        
        return { id: null, confidence: 0, name: '', reasoning: '' };
    }

    /**
     * @function getDefaultRegion - 获取默认区域
     * @returns {Object} 默认区域
     */
    getDefaultRegion() {
        return this.drivingRegionMapping.default;
    }

    /**
     * @function detectCustomerNameLanguage - 客户姓名语言检测
     * @param {string} customerName - 客户姓名
     * @returns {Array} 语言推荐列表
     */
    detectCustomerNameLanguage(customerName) {
        if (!customerName) return [];
        
        const recommendations = [];
        const name = customerName.toLowerCase();
        
        // 中文姓名特征
        if (this.languageMapping.namePatterns.chinese.pattern.test(customerName)) {
            recommendations.push({
                languages: this.languageMapping.namePatterns.chinese.languages,
                confidence: this.languageMapping.namePatterns.chinese.confidence,
                source: 'chinese_name_pattern',
                reasoning: '检测到中文姓名特征'
            });
        }
        
        // 马来姓名特征
        const malayPatterns = this.languageMapping.namePatterns.malay.patterns;
        for (const pattern of malayPatterns) {
            if (name.includes(pattern)) {
                recommendations.push({
                    languages: this.languageMapping.namePatterns.malay.languages,
                    confidence: this.languageMapping.namePatterns.malay.confidence,
                    source: 'malay_name_pattern',
                    reasoning: `检测到马来姓名特征: ${pattern}`
                });
                break;
            }
        }
        
        // 印度姓名特征
        const indianPatterns = this.languageMapping.namePatterns.indian.patterns;
        for (const pattern of indianPatterns) {
            if (name.includes(pattern)) {
                recommendations.push({
                    languages: this.languageMapping.namePatterns.indian.languages,
                    confidence: this.languageMapping.namePatterns.indian.confidence,
                    source: 'indian_name_pattern',
                    reasoning: `检测到印度姓名特征: ${pattern}`
                });
                break;
            }
        }
        
        return recommendations;
    }

    /**
     * @function detectOrderTextLanguage - 订单文本语言检测
     * @param {Object} orderData - 订单数据
     * @returns {Array} 语言推荐列表
     */
    detectOrderTextLanguage(orderData) {
        const recommendations = [];
        const orderText = `${orderData.extra_requirement || ''} ${orderData.flight_info || ''}`;
        
        // 中文文本检测
        if (this.languageMapping.textPatterns.chinese.pattern.test(orderText)) {
            recommendations.push({
                languages: this.languageMapping.textPatterns.chinese.languages,
                confidence: this.languageMapping.textPatterns.chinese.confidence,
                source: 'chinese_text_pattern',
                reasoning: '订单文本包含中文'
            });
        }
        
        // 马来文本检测
        const malayKeywords = this.languageMapping.textPatterns.malay.keywords;
        for (const keyword of malayKeywords) {
            if (orderText.toLowerCase().includes(keyword)) {
                recommendations.push({
                    languages: this.languageMapping.textPatterns.malay.languages,
                    confidence: this.languageMapping.textPatterns.malay.confidence,
                    source: 'malay_text_pattern',
                    reasoning: `订单文本包含马来文关键词: ${keyword}`
                });
                break;
            }
        }
        
        return recommendations;
    }

    /**
     * @function detectLocationLanguage - 地理位置语言偏好检测
     * @param {Object} orderData - 订单数据
     * @returns {Array} 语言推荐列表
     */
    detectLocationLanguage(orderData) {
        const recommendations = [];
        const locationText = `${orderData.pickup || ''} ${orderData.destination || ''}`.toLowerCase();
        
        // 基于地理位置的语言偏好
        if (locationText.includes('chinatown') || locationText.includes('中国城')) {
            recommendations.push({
                languages: [4], // Chinese
                confidence: 0.8,
                source: 'location_preference',
                reasoning: '中国城区域偏好中文服务'
            });
        }
        
        if (locationText.includes('little india') || locationText.includes('印度街')) {
            recommendations.push({
                languages: [2], // English
                confidence: 0.7,
                source: 'location_preference',
                reasoning: '印度区域偏好英文服务'
            });
        }
        
        return recommendations;
    }

    /**
     * @function calculateLanguageRecommendation - 计算综合语言推荐
     * @param {Array} recommendations - 推荐列表
     * @returns {Object} 最终推荐结果
     */
    calculateLanguageRecommendation(recommendations) {
        if (recommendations.length === 0) {
            return {
                languages: this.languageMapping.default.languages,
                confidence: this.languageMapping.default.confidence,
                method: 'default_languages',
                reasoning: '使用默认语言组合（英文+中文）',
                names: ['English', 'Chinese']
            };
        }
        
        // 语言权重计算
        const languageWeights = {};
        let totalConfidence = 0;
        const reasonings = [];
        
        for (const rec of recommendations) {
            for (const langId of rec.languages) {
                if (!languageWeights[langId]) {
                    languageWeights[langId] = 0;
                }
                languageWeights[langId] += rec.confidence;
            }
            totalConfidence += rec.confidence;
            reasonings.push(rec.reasoning);
        }
        
        // 选择权重最高的语言（最多3种）
        const sortedLanguages = Object.entries(languageWeights)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([langId]) => parseInt(langId));
        
        // 确保至少包含英文
        if (!sortedLanguages.includes(2)) {
            sortedLanguages.unshift(2);
        }
        
        const avgConfidence = totalConfidence / recommendations.length;
        
        return {
            languages: sortedLanguages,
            confidence: Math.min(avgConfidence, 0.95),
            method: 'weighted_recommendation',
            reasoning: reasonings.join('; '),
            names: this.getLanguageNames(sortedLanguages)
        };
    }

    /**
     * @function getDefaultLanguages - 获取默认语言
     * @returns {Object} 默认语言配置
     */
    getDefaultLanguages() {
        return {
            languages_id_array: this.languageMapping.default.languages,
            confidence: this.languageMapping.default.confidence,
            method: 'default_fallback',
            reasoning: '使用默认语言配置',
            language_names: ['English', 'Chinese']
        };
    }

    /**
     * @function getLanguageNames - 获取语言名称
     * @param {Array} languageIds - 语言ID数组
     * @returns {Array} 语言名称数组
     */
    getLanguageNames(languageIds) {
        const nameMap = {
            2: 'English',
            3: 'Malay', 
            4: 'Chinese'
        };
        return languageIds.map(id => nameMap[id] || `Language_${id}`);
    }

    /**
     * @function updateSystemData - 更新五维系统数据（扩展版）
     * @param {Array} backendUsers - 后台用户列表
     * @param {Array} subCategories - 服务类型列表
     * @param {Array} carTypes - 车型列表
     * @param {Array} drivingRegions - 行驶区域列表（新增）
     * @param {Array} languages - 语言列表（新增）
     */
    updateSystemData(backendUsers, subCategories, carTypes, drivingRegions = [], languages = []) {
        try {
            this.logger.info('SmartSelection', '更新五维系统数据', {
                backendUsers: backendUsers?.length || 0,
                subCategories: subCategories?.length || 0,
                carTypes: carTypes?.length || 0,
                drivingRegions: drivingRegions?.length || 0,
                languages: languages?.length || 0
            });

            // 更新原有三维映射表
            if (backendUsers && backendUsers.length > 0) {
                const userMapping = this.createDynamicUserMapping(backendUsers);
                this.backendUserMapping = userMapping.mapping;
            }

            if (subCategories && subCategories.length > 0) {
                const serviceMapping = this.createDynamicServiceMapping(subCategories);
                this.serviceTypeMapping = serviceMapping.mapping;
            }

            if (carTypes && carTypes.length > 0) {
                const vehicleMapping = this.createDynamicVehicleMapping(carTypes);
                this.vehicleTypeMapping = vehicleMapping.mapping;
            }

            // 新增：更新区域映射表
            if (drivingRegions && drivingRegions.length > 0) {
                this.updateDrivingRegionMapping(drivingRegions);
            }

            // 新增：更新语言映射表
            if (languages && languages.length > 0) {
                this.updateLanguageMapping(languages);
            }

            this.logger.success('SmartSelection', '五维系统数据更新完成');

        } catch (error) {
            this.logger.error('SmartSelection', '系统数据更新失败', error);
        }
    }

    /**
     * @function updateDrivingRegionMapping - 更新区域映射表
     * @param {Array} drivingRegions - 区域数据
     */
    updateDrivingRegionMapping(drivingRegions) {
        // 保持现有关键词映射，但更新可用区域列表
        this.availableRegions = drivingRegions.map(region => ({
            id: region.id,
            name: region.name
        }));
        
        this.logger.info('SmartSelection', '区域映射表已更新', {
            totalRegions: drivingRegions.length,
            availableRegions: this.availableRegions.length
        });
    }

    /**
     * @function updateLanguageMapping - 更新语言映射表
     * @param {Array} languages - 语言数据
     */
    updateLanguageMapping(languages) {
        // 保持现有模式识别，但更新可用语言列表
        this.availableLanguages = languages.map(language => ({
            id: language.id,
            name: language.name
        }));
        
        this.logger.info('SmartSelection', '语言映射表已更新', {
            totalLanguages: languages.length,
            availableLanguages: this.availableLanguages.length
        });
    }

    /**
     * @function resetToDefaults - 重置为默认值
     */
    resetToDefaults() {
        try {
            this.logger.info('SmartSelection', '重置智能选择服务为默认值');
            
            // 重新初始化映射表
            this.initializeMappingTables();
            
            // 清除学习数据
            if (this.learningEngine) {
                this.learningEngine.clearLearningData();
            }
            
            this.logger.success('SmartSelection', '智能选择服务已重置');
            
        } catch (error) {
            this.logger.error('SmartSelection', '重置失败', error);
        }
    }

    // 辅助方法
    selectByPassengerCount(orderData) {
        const passengerCount = parseInt(orderData.passenger_number) || 1;
        const mapping = this.vehicleTypeMapping.passengerCount[passengerCount];
        
        if (mapping) {
            return {
                car_type_id: mapping.id,
                confidence: 0.8,
                reasoning: `根据乘客数量 ${passengerCount} 选择车型`
            };
        }
        return null;
    }

    selectByVehicleKeywords(orderData) {
        const text = [
            orderData.extra_requirement || '',
            orderData.customer_name || '',
            orderData.flight_info || ''
        ].join(' ').toLowerCase();

        for (const [keyword, vehicle] of Object.entries(this.vehicleTypeMapping.keywords)) {
            if (text.includes(keyword)) {
                return {
                    car_type_id: vehicle.id,
                    confidence: 0.7,
                    reasoning: `匹配车型关键词: ${keyword}`
                };
            }
        }
        return null;
    }

    selectByLuggageCount(orderData) {
        const luggageCount = parseInt(orderData.luggage_number) || 0;
        if (luggageCount > 4) {
            return {
                car_type_id: 16, // MPV
                confidence: 0.6,
                reasoning: `行李数量 ${luggageCount} 需要大车型`
            };
        }
        return null;
    }

    selectByServiceType(orderData) {
        const serviceText = [orderData.pickup || '', orderData.destination || ''].join(' ').toLowerCase();
        
        if (serviceText.includes('airport') || serviceText.includes('机场')) {
            return {
                car_type_id: 6, // Comfort
                confidence: 0.5,
                reasoning: '机场服务推荐舒适车型'
            };
        }
        return null;
    }

    extractSeatsFromName(name) {
        const match = name.match(/(\d+)\s*seat/i);
        return match ? parseInt(match[1]) : 4;
    }

    categorizeVehicle(name, patterns) {
        for (const [category, pattern] of Object.entries(patterns)) {
            if (pattern.test(name)) {
                return category;
            }
        }
        return 'standard';
    }

    addChineseKeywords(mapping, carType, category) {
        const chineseKeywords = {
            'compact': ['经济', '小车', '轿车'],
            'comfort': ['舒适', '标准'],
            'luxury': ['豪华', '高级'],
            'suv': ['越野', 'SUV'],
            'mpv': ['商务', 'MPV'],
            'van': ['面包', '客车']
        };

        const keywords = chineseKeywords[category] || [];
        keywords.forEach(keyword => {
            mapping[keyword] = {
                id: carType.id,
                name: carType.name,
                category: category,
                seats: carType.seats || this.extractSeatsFromName(carType.name)
            };
        });
    }

    addServiceKeywords(mapping, subCategory, category) {
        const serviceKeywords = {
            'pickup': ['接机', '接送', '接人'],
            'dropoff': ['送机', '送人'],
            'charter': ['包车', '包租'],
            'transfer': ['转乘', '中转']
        };

        const name = subCategory.name.toLowerCase();
        Object.entries(serviceKeywords).forEach(([type, keywords]) => {
            if (name.includes(type)) {
                keywords.forEach(keyword => {
                    mapping[keyword] = {
                        id: subCategory.id,
                        name: subCategory.name,
                        category: category
                    };
                });
            }
        });
    }

    // #region OTA Profile集成功能 v4.2.0

    /**
     * @function applyProfileToSelection - 应用Profile配置到智能选择
     * @param {Object} orderData - 订单数据
     * @param {Object} profileConfig - Profile配置
     * @returns {Object} 应用Profile后的选择结果
     */
    applyProfileToSelection(orderData, profileConfig = null) {
        try {
            this.logger.info('SmartSelection', '开始应用Profile配置', { 
                hasProfile: !!profileConfig,
                profileType: profileConfig?.templateType || 'none'
            });

            // 获取当前Profile配置
            const currentProfile = profileConfig || this.getCurrentProfile();
            
            if (!currentProfile) {
                this.logger.debug('SmartSelection', '无Profile配置，使用标准选择');
                return this.performStandardSelection(orderData);
            }

            // 应用Profile权重和默认值
            const profileEnhancedData = this.enhanceOrderDataWithProfile(orderData, currentProfile);
            
            // 执行Profile增强的智能选择
            const selectionResult = this.performProfileEnhancedSelection(profileEnhancedData, currentProfile);
            
            this.logger.success('SmartSelection', 'Profile配置应用完成', {
                profileType: currentProfile.templateType,
                appliedDefaults: Object.keys(selectionResult.appliedDefaults || {}).length
            });

            return selectionResult;

        } catch (error) {
            this.logger.error('SmartSelection', 'Profile配置应用失败', error);
            return this.performStandardSelection(orderData);
        }
    }

    /**
     * @function getCurrentProfile - 获取当前Profile配置
     * @returns {Object|null} 当前Profile配置
     */
    getCurrentProfile() {
        try {
            // 优先使用当前缓存的Profile配置
            if (this.currentProfileConfig) {
                return this.currentProfileConfig;
            }
            
            // 从OTA Profile Manager获取当前配置
            if (window.otaProfileManager) {
                const profile = window.otaProfileManager.getCurrentProfile();
                if (profile) {
                    // 转换为智能选择服务需要的格式
                    this.currentProfileConfig = {
                        templateType: profile.id,
                        name: profile.name,
                        weights: window.otaProfileManager.getProfileWeights(),
                        defaults: window.otaProfileManager.getProfileDefaults(),
                        rules: profile.config?.business?.specialRules || {}
                    };
                    return this.currentProfileConfig;
                }
            }
            
            // 从AppState获取Profile信息
            if (window.appState?.currentProfile) {
                const profile = window.appState.currentProfile;
                this.currentProfileConfig = {
                    templateType: profile.id,
                    name: profile.name,
                    weights: {},
                    defaults: {},
                    rules: {}
                };
                return this.currentProfileConfig;
            }
            
            return null;
        } catch (error) {
            this.logger.error('SmartSelection', '获取Profile配置失败', error);
            return null;
        }
    }

    /**
     * @function enhanceOrderDataWithProfile - 使用Profile增强订单数据
     * @param {Object} orderData - 原始订单数据
     * @param {Object} profileConfig - Profile配置
     * @returns {Object} 增强后的订单数据
     */
    enhanceOrderDataWithProfile(orderData, profileConfig) {
        const enhancedData = { ...orderData };
        
        // 应用Profile默认值
        if (profileConfig.defaults) {
            // 车型默认值
            if (profileConfig.defaults.carType && !enhancedData.car_type_preference) {
                enhancedData.car_type_preference = profileConfig.defaults.carType;
                enhancedData._profileApplied = enhancedData._profileApplied || {};
                enhancedData._profileApplied.carType = true;
            }
            
            // 服务类型默认值
            if (profileConfig.defaults.serviceType && !enhancedData.service_preference) {
                enhancedData.service_preference = profileConfig.defaults.serviceType;
                enhancedData._profileApplied = enhancedData._profileApplied || {};
                enhancedData._profileApplied.serviceType = true;
            }
            
            // 后台用户默认值
            if (profileConfig.defaults.backendUser && !enhancedData.backend_user_preference) {
                enhancedData.backend_user_preference = profileConfig.defaults.backendUser;
                enhancedData._profileApplied = enhancedData._profileApplied || {};
                enhancedData._profileApplied.backendUser = true;
            }
            
            // 区域默认值
            if (profileConfig.defaults.drivingRegion && !enhancedData.region_preference) {
                enhancedData.region_preference = profileConfig.defaults.drivingRegion;
                enhancedData._profileApplied = enhancedData._profileApplied || {};
                enhancedData._profileApplied.drivingRegion = true;
            }
            
            // 语言默认值
            if (profileConfig.defaults.languages && !enhancedData.language_preference) {
                enhancedData.language_preference = profileConfig.defaults.languages;
                enhancedData._profileApplied = enhancedData._profileApplied || {};
                enhancedData._profileApplied.languages = true;
            }
        }
        
        // 添加Profile权重信息
        enhancedData._profileWeights = profileConfig.weights || {};
        enhancedData._profileRules = profileConfig.rules || {};
        
        return enhancedData;
    }

    /**
     * @function performProfileEnhancedSelection - 执行Profile增强的智能选择
     * @param {Object} enhancedData - Profile增强的订单数据
     * @param {Object} profileConfig - Profile配置
     * @returns {Object} 选择结果
     */
    performProfileEnhancedSelection(enhancedData, profileConfig) {
        const results = {};
        const appliedDefaults = {};
        
        // 1. 车型选择（应用Profile权重）
        const carTypeResult = this.selectCarTypeWithProfile(enhancedData, profileConfig);
        results.carType = carTypeResult;
        if (enhancedData._profileApplied?.carType) {
            appliedDefaults.carType = carTypeResult.car_type_id;
        }
        
        // 2. 服务类型选择
        const serviceResult = this.selectSubCategoryWithProfile(enhancedData, profileConfig);
        results.subCategory = serviceResult;
        if (enhancedData._profileApplied?.serviceType) {
            appliedDefaults.serviceType = serviceResult.sub_category_id;
        }
        
        // 3. 后台用户选择
        const userResult = this.selectBackendUserWithProfile(enhancedData, profileConfig);
        results.backendUser = userResult;
        if (enhancedData._profileApplied?.backendUser) {
            appliedDefaults.backendUser = userResult.incharge_by_backend_user_id;
        }
        
        // 4. 区域选择
        const regionResult = this.selectDrivingRegionWithProfile(enhancedData, profileConfig);
        results.drivingRegion = regionResult;
        if (enhancedData._profileApplied?.drivingRegion) {
            appliedDefaults.drivingRegion = regionResult.driving_region_id;
        }
        
        // 5. 语言选择
        const languageResult = this.selectLanguagesWithProfile(enhancedData, profileConfig);
        results.languages = languageResult;
        if (enhancedData._profileApplied?.languages) {
            appliedDefaults.languages = languageResult.languages_id_array;
        }
        
        return {
            ...results,
            appliedDefaults,
            profileType: profileConfig.templateType,
            profileApplied: true
        };
    }

    /**
     * @function selectCarTypeWithProfile - 带Profile权重的车型选择
     * @param {Object} enhancedData - 增强订单数据
     * @param {Object} profileConfig - Profile配置
     * @returns {Object} 选择结果
     */
    selectCarTypeWithProfile(enhancedData, profileConfig) {
        // 如果有Profile默认值且没有明确偏好，直接使用默认值
        if (enhancedData.car_type_preference && enhancedData._profileApplied?.carType) {
            return {
                car_type_id: enhancedData.car_type_preference,
                confidence: 0.9,
                method: 'profile_default',
                reasoning: 'Profile默认车型配置'
            };
        }
        
        // 否则执行标准选择，但应用Profile权重
        const standardResult = this.selectCarType(enhancedData);
        
        // 应用Profile权重调整
        if (profileConfig.weights?.carType) {
            const weightAdjustment = profileConfig.weights.carType[standardResult.car_type_id] || 1.0;
            standardResult.confidence *= weightAdjustment;
            standardResult.method += '_profile_weighted';
            standardResult.reasoning += ` (Profile权重: ${weightAdjustment})`;
        }
        
        return standardResult;
    }

    /**
     * @function selectSubCategoryWithProfile - 带Profile权重的服务类型选择
     * @param {Object} enhancedData - 增强订单数据
     * @param {Object} profileConfig - Profile配置
     * @returns {Object} 选择结果
     */
    selectSubCategoryWithProfile(enhancedData, profileConfig) {
        if (enhancedData.service_preference && enhancedData._profileApplied?.serviceType) {
            return {
                sub_category_id: enhancedData.service_preference,
                confidence: 0.9,
                method: 'profile_default',
                reasoning: 'Profile默认服务类型配置'
            };
        }
        
        const standardResult = this.selectSubCategory(enhancedData);
        
        if (profileConfig.weights?.serviceType) {
            const weightAdjustment = profileConfig.weights.serviceType[standardResult.sub_category_id] || 1.0;
            standardResult.confidence *= weightAdjustment;
            standardResult.method += '_profile_weighted';
            standardResult.reasoning += ` (Profile权重: ${weightAdjustment})`;
        }
        
        return standardResult;
    }

    /**
     * @function selectBackendUserWithProfile - 带Profile权重的后台用户选择
     * @param {Object} enhancedData - 增强订单数据
     * @param {Object} profileConfig - Profile配置
     * @returns {Object} 选择结果
     */
    selectBackendUserWithProfile(enhancedData, profileConfig) {
        if (enhancedData.backend_user_preference && enhancedData._profileApplied?.backendUser) {
            return {
                incharge_by_backend_user_id: enhancedData.backend_user_preference,
                confidence: 0.9,
                method: 'profile_default',
                reasoning: 'Profile默认后台用户配置'
            };
        }
        
        const standardResult = this.selectBackendUser(enhancedData);
        
        if (profileConfig.weights?.backendUser) {
            const weightAdjustment = profileConfig.weights.backendUser[standardResult.incharge_by_backend_user_id] || 1.0;
            standardResult.confidence *= weightAdjustment;
            standardResult.method += '_profile_weighted';
            standardResult.reasoning += ` (Profile权重: ${weightAdjustment})`;
        }
        
        return standardResult;
    }

    /**
     * @function selectDrivingRegionWithProfile - 带Profile权重的区域选择
     * @param {Object} enhancedData - 增强订单数据
     * @param {Object} profileConfig - Profile配置
     * @returns {Object} 选择结果
     */
    selectDrivingRegionWithProfile(enhancedData, profileConfig) {
        if (enhancedData.region_preference && enhancedData._profileApplied?.drivingRegion) {
            return {
                driving_region_id: enhancedData.region_preference,
                confidence: 0.9,
                method: 'profile_default',
                reasoning: 'Profile默认区域配置'
            };
        }
        
        const standardResult = this.selectDrivingRegion(enhancedData);
        
        if (profileConfig.weights?.drivingRegion) {
            const weightAdjustment = profileConfig.weights.drivingRegion[standardResult.driving_region_id] || 1.0;
            standardResult.confidence *= weightAdjustment;
            standardResult.method += '_profile_weighted';
            standardResult.reasoning += ` (Profile权重: ${weightAdjustment})`;
        }
        
        return standardResult;
    }

    /**
     * @function selectLanguagesWithProfile - 带Profile权重的语言选择
     * @param {Object} enhancedData - 增强订单数据
     * @param {Object} profileConfig - Profile配置
     * @returns {Object} 选择结果
     */
    selectLanguagesWithProfile(enhancedData, profileConfig) {
        if (enhancedData.language_preference && enhancedData._profileApplied?.languages) {
            return {
                languages_id_array: enhancedData.language_preference,
                confidence: 0.9,
                method: 'profile_default',
                reasoning: 'Profile默认语言配置'
            };
        }
        
        const standardResult = this.selectLanguages(enhancedData);
        
        if (profileConfig.weights?.languages) {
            // 对每个选中的语言应用权重
            let totalWeight = 0;
            for (const langId of standardResult.languages_id_array) {
                totalWeight += profileConfig.weights.languages[langId] || 1.0;
            }
            const avgWeight = totalWeight / standardResult.languages_id_array.length;
            
            standardResult.confidence *= avgWeight;
            standardResult.method += '_profile_weighted';
            standardResult.reasoning += ` (Profile平均权重: ${avgWeight.toFixed(2)})`;
        }
        
        return standardResult;
    }

    /**
     * @function performStandardSelection - 执行标准智能选择（无Profile）
     * @param {Object} orderData - 订单数据
     * @returns {Object} 选择结果
     */
    performStandardSelection(orderData) {
        return {
            carType: this.selectCarType(orderData),
            subCategory: this.selectSubCategory(orderData),
            backendUser: this.selectBackendUser(orderData),
            drivingRegion: this.selectDrivingRegion(orderData),
            languages: this.selectLanguages(orderData),
            profileApplied: false
        };
    }

    // #endregion

    // #region 自动选择引擎 v4.2.0

    /**
     * @function performCompleteAutoSelection - 执行完整的五维自动选择
     * @param {Object} orderData - 订单数据
     * @param {Object} options - 选择选项
     * @returns {Object} 完整的选择结果
     */
    async performCompleteAutoSelection(orderData, options = {}) {
        try {
            this.logger.info('SmartSelection', '开始执行完整五维自动选择', { 
                orderData: !!orderData,
                options 
            });

            const startTime = Date.now();
            
            // 1. 数据预处理和验证
            const validatedData = this.validateAndPreprocessOrderData(orderData);
            
            // 2. 获取Profile配置
            const profileConfig = options.profileConfig || this.getCurrentProfile();
            
            // 3. 执行智能选择
            let selectionResult;
            if (profileConfig) {
                selectionResult = this.applyProfileToSelection(validatedData, profileConfig);
            } else {
                selectionResult = this.performStandardSelection(validatedData);
            }
            
            // 4. 结果验证和优化
            const optimizedResult = await this.optimizeSelectionResult(selectionResult, validatedData);
            
            // 5. 生成最终结果
            const finalResult = this.generateFinalSelectionResult(optimizedResult, validatedData, profileConfig);
            
            const executionTime = Date.now() - startTime;
            
            this.logger.success('SmartSelection', '五维自动选择完成', {
                executionTime: `${executionTime}ms`,
                profileApplied: finalResult.profileApplied,
                confidence: finalResult.overallConfidence
            });

            return finalResult;

        } catch (error) {
            this.logger.error('SmartSelection', '五维自动选择失败', error);
            return this.generateErrorFallbackResult(orderData);
        }
    }

    /**
     * @function validateAndPreprocessOrderData - 验证和预处理订单数据
     * @param {Object} orderData - 原始订单数据
     * @returns {Object} 验证后的订单数据
     */
    validateAndPreprocessOrderData(orderData) {
        const processed = { ...orderData };
        
        // 数据清理和标准化
        if (processed.customer_name) {
            processed.customer_name = processed.customer_name.trim();
        }
        
        if (processed.pickup) {
            processed.pickup = processed.pickup.trim();
        }
        
        if (processed.destination) {
            processed.destination = processed.destination.trim();
        }
        
        // 数字字段处理
        if (processed.passenger_number) {
            processed.passenger_number = parseInt(processed.passenger_number) || 1;
        }
        
        if (processed.luggage_number) {
            processed.luggage_number = parseInt(processed.luggage_number) || 0;
        }
        
        // 添加处理时间戳
        processed._processedAt = Date.now();
        
        return processed;
    }

    /**
     * @function optimizeSelectionResult - 优化选择结果
     * @param {Object} selectionResult - 原始选择结果
     * @param {Object} orderData - 订单数据
     * @returns {Object} 优化后的结果
     */
    async optimizeSelectionResult(selectionResult, orderData) {
        // 交叉验证和一致性检查
        const optimized = { ...selectionResult };
        
        // 车型和乘客数量一致性检查
        if (optimized.carType && orderData.passenger_number) {
            const carTypeCapacity = this.getCarTypeCapacity(optimized.carType.car_type_id);
            if (carTypeCapacity && orderData.passenger_number > carTypeCapacity) {
                this.logger.warn('SmartSelection', '车型容量不足，重新选择', {
                    selectedCarType: optimized.carType.car_type_id,
                    capacity: carTypeCapacity,
                    passengers: orderData.passenger_number
                });
                
                // 重新选择更大的车型
                optimized.carType = this.selectLargerCarType(orderData.passenger_number);
            }
        }
        
        // 服务类型和地址一致性检查
        if (optimized.subCategory && optimized.drivingRegion) {
            const serviceRegionCompatibility = this.checkServiceRegionCompatibility(
                optimized.subCategory.sub_category_id,
                optimized.drivingRegion.driving_region_id
            );
            
            if (!serviceRegionCompatibility.compatible) {
                this.logger.warn('SmartSelection', '服务类型与区域不兼容，调整选择', {
                    service: optimized.subCategory.sub_category_id,
                    region: optimized.drivingRegion.driving_region_id,
                    reason: serviceRegionCompatibility.reason
                });
                
                // 根据兼容性调整
                if (serviceRegionCompatibility.suggestedRegion) {
                    optimized.drivingRegion.driving_region_id = serviceRegionCompatibility.suggestedRegion;
                    optimized.drivingRegion.method += '_compatibility_adjusted';
                }
            }
        }
        
        return optimized;
    }

    /**
     * @function generateFinalSelectionResult - 生成最终选择结果
     * @param {Object} optimizedResult - 优化后的结果
     * @param {Object} orderData - 订单数据
     * @param {Object} profileConfig - Profile配置
     * @returns {Object} 最终结果
     */
    generateFinalSelectionResult(optimizedResult, orderData, profileConfig) {
        // 计算整体置信度
        const confidenceScores = [
            optimizedResult.carType?.confidence || 0,
            optimizedResult.subCategory?.confidence || 0,
            optimizedResult.backendUser?.confidence || 0,
            optimizedResult.drivingRegion?.confidence || 0,
            optimizedResult.languages?.confidence || 0
        ];
        
        const overallConfidence = confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length;
        
        return {
            // 五维选择结果
            car_type_id: optimizedResult.carType?.car_type_id,
            sub_category_id: optimizedResult.subCategory?.sub_category_id,
            incharge_by_backend_user_id: optimizedResult.backendUser?.incharge_by_backend_user_id,
            driving_region_id: optimizedResult.drivingRegion?.driving_region_id,
            languages_id_array: optimizedResult.languages?.languages_id_array || [],
            
            // 元数据
            overallConfidence: Math.round(overallConfidence * 100) / 100,
            profileApplied: optimizedResult.profileApplied || false,
            profileType: profileConfig?.templateType || null,
            appliedDefaults: optimizedResult.appliedDefaults || {},
            
            // 详细结果
            detailedResults: {
                carType: optimizedResult.carType,
                subCategory: optimizedResult.subCategory,
                backendUser: optimizedResult.backendUser,
                drivingRegion: optimizedResult.drivingRegion,
                languages: optimizedResult.languages
            },
            
            // 执行信息
            executedAt: Date.now(),
            version: '4.2.0',
            method: 'complete_auto_selection'
        };
    }

    /**
     * @function generateErrorFallbackResult - 生成错误回退结果
     * @param {Object} orderData - 订单数据
     * @returns {Object} 回退结果
     */
    generateErrorFallbackResult(orderData) {
        return {
            car_type_id: 6, // Comfort 默认
            sub_category_id: 7, // 默认服务
            incharge_by_backend_user_id: 1, // 默认用户
            driving_region_id: 1, // KL/Selangor 默认
            languages_id_array: [2, 4], // English + Chinese 默认
            
            overallConfidence: 0.1,
            profileApplied: false,
            profileType: null,
            appliedDefaults: {},
            
            detailedResults: {
                carType: { method: 'error_fallback', reasoning: '选择失败，使用默认值' },
                subCategory: { method: 'error_fallback', reasoning: '选择失败，使用默认值' },
                backendUser: { method: 'error_fallback', reasoning: '选择失败，使用默认值' },
                drivingRegion: { method: 'error_fallback', reasoning: '选择失败，使用默认值' },
                languages: { method: 'error_fallback', reasoning: '选择失败，使用默认值' }
            },
            
            executedAt: Date.now(),
            version: '4.2.0',
            method: 'error_fallback'
        };
    }

    // #endregion

    // #region 辅助方法

    /**
     * @function getCarTypeCapacity - 获取车型容量
     * @param {number} carTypeId - 车型ID
     * @returns {number|null} 车型容量
     */
    getCarTypeCapacity(carTypeId) {
        // 基于车型ID返回容量（这里需要根据实际数据调整）
        const capacityMap = {
            6: 4,   // Comfort
            16: 7,  // MPV
            17: 4,  // Luxury
            // 添加更多车型容量映射
        };
        
        return capacityMap[carTypeId] || null;
    }

    /**
     * @function selectLargerCarType - 选择更大的车型
     * @param {number} passengerCount - 乘客数量
     * @returns {Object} 车型选择结果
     */
    selectLargerCarType(passengerCount) {
        if (passengerCount > 4) {
            return {
                car_type_id: 16, // MPV
                confidence: 0.8,
                method: 'capacity_upgrade',
                reasoning: `乘客数量${passengerCount}，升级到MPV`
            };
        }
        
        return {
            car_type_id: 6, // Comfort
            confidence: 0.7,
            method: 'capacity_standard',
            reasoning: `乘客数量${passengerCount}，使用标准车型`
        };
    }

    /**
     * @function checkServiceRegionCompatibility - 检查服务类型与区域兼容性
     * @param {number} serviceId - 服务类型ID
     * @param {number} regionId - 区域ID
     * @returns {Object} 兼容性检查结果
     */
    checkServiceRegionCompatibility(serviceId, regionId) {
        // 这里可以添加具体的兼容性规则
        // 目前返回兼容
        return {
            compatible: true,
            reason: '服务类型与区域兼容',
            suggestedRegion: null
        };
    }

    // #endregion

    /**
     * @function applyProfile - 应用OTA Profile配置
     * @description 应用Profile配置到智能选择服务
     * @param {Object} profileConfig - Profile配置对象
     */
    applyProfile(profileConfig) {
        try {
            if (!profileConfig) {
                this.logger.warn('SmartSelection', '无效的Profile配置，使用默认设置');
                this.resetToDefaults();
                return;
            }

            // 缓存当前Profile配置
            this.currentProfileConfig = profileConfig;
            
            // 应用Profile权重设置
            this.applyProfileWeights(profileConfig);
            
            // 应用默认选择配置
            this.applyProfileDefaults(profileConfig);
            
            this.logger.success('SmartSelection', 'Profile配置应用成功', {
                profileName: profileConfig.name,
                profileId: profileConfig.id,
                defaultLanguages: profileConfig.defaultLanguages,
                defaultRegion: profileConfig.defaultRegion,
                defaultBackendUser: profileConfig.defaultBackendUser
            });
            
        } catch (error) {
            this.logger.error('SmartSelection', 'Profile配置应用失败', error);
            this.resetToDefaults();
        }
    }

    /**
     * @function applyProfileWeights - 应用Profile权重设置
     * @description 根据Profile配置调整选择权重
     * @param {Object} profileConfig - Profile配置对象
     */
    applyProfileWeights(profileConfig) {
        // 语言权重调整
        if (profileConfig.defaultLanguages) {
            this.languageWeights = {};
            profileConfig.defaultLanguages.forEach((langId, index) => {
                // 默认语言权重更高
                this.languageWeights[langId] = 1.0 - (index * 0.1);
            });
        }
        
        // 区域权重调整
        if (profileConfig.defaultRegion) {
            this.regionWeights = {};
            this.regionWeights[profileConfig.defaultRegion] = 1.0;
        }
        
        // 后台用户权重调整
        if (profileConfig.defaultBackendUser) {
            this.userWeights = {};
            this.userWeights[profileConfig.defaultBackendUser] = 1.0;
        }
        
        // 车型权重调整（基于Profile的车型规则）
        if (profileConfig.defaultCarTypeRules) {
            this.carTypeWeights = {};
            Object.values(profileConfig.defaultCarTypeRules).forEach(carTypeId => {
                this.carTypeWeights[carTypeId] = 1.0;
            });
        }
        
        this.logger.debug('SmartSelection', 'Profile权重已应用', {
            languageWeights: this.languageWeights,
            regionWeights: this.regionWeights,
            userWeights: this.userWeights,
            carTypeWeights: this.carTypeWeights
        });
    }

    /**
     * @function applyProfileDefaults - 应用Profile默认设置
     * @description 设置Profile的默认选择值
     * @param {Object} profileConfig - Profile配置对象
     */
    applyProfileDefaults(profileConfig) {
        // 设置默认选择缓存
        this.profileDefaults = {
            languages: profileConfig.defaultLanguages || [2, 4], // 默认英文+中文
            region: profileConfig.defaultRegion || 1,             // 默认KL/Selangor
            backendUser: profileConfig.defaultBackendUser || 1,   // 默认Super Admin
            carTypeRules: profileConfig.defaultCarTypeRules || {},
            subCategory: profileConfig.defaultSubCategory || null // 默认服务类型
        };
        
        // 如果有服务类型偏好，更新服务映射
        if (profileConfig.serviceTypePreferences) {
            this.profileServiceMapping = profileConfig.serviceTypePreferences;
        }
        
        this.logger.debug('SmartSelection', 'Profile默认设置已应用', this.profileDefaults);
    }

    /**
     * @function getProfileDefaults - 获取Profile默认设置
     * @description 获取当前Profile的默认选择值
     * @param {string} selectionType - 选择类型 (languages, region, backendUser, carType)
     * @returns {*} 对应的默认值
     */
    getProfileDefaults(selectionType, orderData = {}) {
        if (!this.profileDefaults) {
            return null;
        }
        
        switch (selectionType) {
            case 'languages':
                return {
                    ids: this.profileDefaults.languages,
                    confidence: 0.9,
                    reason: `基于${this.currentProfileConfig?.name || 'Profile'}默认语言设置`
                };
                
            case 'region':
                return {
                    id: this.profileDefaults.region,
                    confidence: 0.8,
                    reason: `基于${this.currentProfileConfig?.name || 'Profile'}默认区域设置`
                };
                
            case 'backendUser':
                return {
                    id: this.profileDefaults.backendUser,
                    confidence: 0.9,
                    reason: `基于${this.currentProfileConfig?.name || 'Profile'}默认用户设置`
                };
                
            case 'carType':
                // 根据乘客数量从Profile车型规则中选择
                const passengerCount = this.extractPassengerCount(orderData);
                for (const [range, carTypeId] of Object.entries(this.profileDefaults.carTypeRules)) {
                    if (this.isPassengerInRange(passengerCount, range)) {
                        return {
                            id: carTypeId,
                            confidence: 0.85,
                            reason: `基于${this.currentProfileConfig?.name || 'Profile'}车型规则: ${range} → 车型${carTypeId}`
                        };
                    }
                }
                break;
                
            case 'subCategory':
                if (this.profileDefaults.subCategory) {
                    return {
                        id: this.profileDefaults.subCategory,
                        confidence: 0.8,
                        reason: `基于${this.currentProfileConfig?.name || 'Profile'}默认服务类型设置`
                    };
                }
                break;
        }
        
        return null;
    }

    /**
     * @function isPassengerInRange - 检查乘客数量是否在范围内
     * @description 检查乘客数量是否符合Profile车型规则的范围
     * @param {number} passengerCount - 乘客数量
     * @param {string} range - 范围字符串 (如 "1-2", "5+")
     * @returns {boolean} 是否在范围内
     */
    isPassengerInRange(passengerCount, range) {
        if (range.includes('-')) {
            const [min, max] = range.split('-').map(num => parseInt(num.replace(/[^\d]/g, '')));
            return passengerCount >= min && passengerCount <= max;
        } else if (range.includes('+')) {
            const min = parseInt(range.replace(/[^\d]/g, ''));
            return passengerCount >= min;
        } else {
            const exact = parseInt(range.replace(/[^\d]/g, ''));
            return passengerCount === exact;
        }
    }

    /**
     * @function extractPassengerCount - 提取乘客数量
     * @description 从订单数据中提取乘客数量
     * @param {Object} orderData - 订单数据
     * @returns {number} 乘客数量
     */
    extractPassengerCount(orderData) {
        // 1. 直接从passenger_number字段获取
        if (orderData.passenger_number) {
            const passengerNum = parseInt(orderData.passenger_number);
            if (!isNaN(passengerNum) && passengerNum > 0) {
                return passengerNum;
            }
        }

        // 2. 从extra_requirement中提取
        const extraText = orderData.extra_requirement || '';
        const passengerMatch = extraText.match(/(\d+)\s*(?:人|passenger|pax)/i);
        if (passengerMatch) {
            const count = parseInt(passengerMatch[1]);
            if (!isNaN(count) && count > 0) {
                return count;
            }
        }

        // 3. 从其他文本字段中提取
        const allText = [
            orderData.customer_name || '',
            orderData.flight_info || '',
            orderData.pickup || '',
            orderData.destination || ''
        ].join(' ');

        const textMatch = allText.match(/(\d+)\s*(?:人|passenger|pax)/i);
        if (textMatch) {
            const count = parseInt(textMatch[1]);
            if (!isNaN(count) && count > 0) {
                return count;
            }
        }

        // 4. 默认返回2人
        return 2;
    }

    /**
     * @function smartServiceTypeMatch - 智能服务类型匹配
     * @description 基于订单类型自动匹配服务类型ID (pickup/dropoff/charter)
     * @param {Object} orderData - 订单数据
     * @returns {Object|null} 匹配结果
     */
    smartServiceTypeMatch(orderData) {
        try {
            const pickup = (orderData.pickup || '').toLowerCase();
            const destination = (orderData.destination || '').toLowerCase();
            const extraReq = (orderData.extra_requirement || '').toLowerCase();
            const flightInfo = (orderData.flight_info || '').toLowerCase();
            
            // 组合所有文本进行分析
            const allText = `${pickup} ${destination} ${extraReq} ${flightInfo}`;
            
            // 基于实际API数据更新服务类型ID映射
            const serviceTypeMapping = {
                // 接机服务 (Pickup)
                pickup: {
                    keywords: [
                        'pickup', 'pick up', 'arrival', 'arriving', 'landed',
                        '接机', '到达', '接客', '机场接', 'airport pickup',
                        'arrival pickup', 'flight arrival'
                    ],
                    id: 2, // Pickup (实际API ID)
                    confidence: 0.95
                },
                
                // 送机服务 (Drop-off) 
                dropoff: {
                    keywords: [
                        'dropoff', 'drop off', 'departure', 'departing', 'drop-off',
                        '送机', '出发', '送客', '机场送', 'airport dropoff',
                        'departure dropoff', 'flight departure'
                    ],
                    id: 3, // Dropoff (实际API ID)
                    confidence: 0.95
                },
                
                // 包车服务 (Charter)
                charter: {
                    keywords: [
                        'charter', 'full day', 'half day', 'hourly', 'waiting', 
                        'multiple stops', 'tour', 'sightseeing',
                        '包车', '全天', '半天', '小时', '等候', '多站点', 
                        '多个地点', '旅游', '观光'
                    ],
                    id: 4, // Charter (实际API ID)
                    confidence: 0.9
                }
            };

            // 机场关键词检测
            const airportKeywords = [
                'airport', 'klia', 'klia2', 'lcct', 'subang airport', 
                'sultan abdul aziz shah airport', 'penang airport',
                '机场', '航班', 'flight', 'terminal', 'gate'
            ];
            
            const hasAirportKeywords = airportKeywords.some(keyword => 
                allText.includes(keyword)
            );

            // 1. 优先级检测: 包车服务 (最高特征性)
            const charterMatches = serviceTypeMapping.charter.keywords.filter(keyword => 
                allText.includes(keyword)
            );
            
            if (charterMatches.length > 0) {
                return {
                    id: serviceTypeMapping.charter.id,
                    confidence: serviceTypeMapping.charter.confidence,
                    reasoning: `包车服务检测: 匹配关键词 [${charterMatches.join(', ')}]`,
                    name: 'Charter Service',
                    matchedKeywords: charterMatches,
                    serviceType: 'charter'
                };
            }

            // 2. 接机服务检测
            const pickupMatches = serviceTypeMapping.pickup.keywords.filter(keyword => 
                allText.includes(keyword)
            );
            
            if (pickupMatches.length > 0 || (hasAirportKeywords && !destination)) {
                return {
                    id: serviceTypeMapping.pickup.id,
                    confidence: pickupMatches.length > 0 ? 
                        serviceTypeMapping.pickup.confidence : 0.8,
                    reasoning: pickupMatches.length > 0 ?
                        `接机服务检测: 匹配关键词 [${pickupMatches.join(', ')}]` :
                        '机场关键词+无目的地，判断为接机服务',
                    name: 'Pickup Service',
                    matchedKeywords: pickupMatches,
                    serviceType: 'pickup'
                };
            }

            // 3. 送机服务检测
            const dropoffMatches = serviceTypeMapping.dropoff.keywords.filter(keyword => 
                allText.includes(keyword)
            );
            
            if (dropoffMatches.length > 0 || (hasAirportKeywords && destination.includes('airport'))) {
                return {
                    id: serviceTypeMapping.dropoff.id,
                    confidence: dropoffMatches.length > 0 ? 
                        serviceTypeMapping.dropoff.confidence : 0.8,
                    reasoning: dropoffMatches.length > 0 ?
                        `送机服务检测: 匹配关键词 [${dropoffMatches.join(', ')}]` :
                        '目的地为机场，判断为送机服务',
                    name: 'Dropoff Service',
                    matchedKeywords: dropoffMatches,
                    serviceType: 'dropoff'
                };
            }

            // 4. 基于地址信息推断 (fallback logic)
            if (pickup && destination) {
                // 检查是否包含机场相关地址
                if (hasAirportKeywords) {
                    if (pickup.includes('airport') || pickup.includes('klia')) {
                        // 从机场出发，可能是接机后的行程
                        return {
                            id: serviceTypeMapping.pickup.id,
                            confidence: 0.7,
                            reasoning: '起点为机场，推断为接机服务',
                            name: 'Airport Pickup Service',
                            serviceType: 'pickup'
                        };
                    } else if (destination.includes('airport') || destination.includes('klia')) {
                        // 到机场，送机服务
                        return {
                            id: serviceTypeMapping.dropoff.id,
                            confidence: 0.7,
                            reasoning: '目的地为机场，推断为送机服务',
                            name: 'Airport Dropoff Service',
                            serviceType: 'dropoff'
                        };
                    }
                }
                
                // 普通点对点服务，默认为包车
                return {
                    id: serviceTypeMapping.charter.id,
                    confidence: 0.6,
                    reasoning: '点对点行程，默认为包车服务',
                    name: 'Point to Point Charter',
                    serviceType: 'charter'
                };
            }

            // 5. 无法明确判断，返回null让其他方法处理
            return null;
            
        } catch (error) {
            logger.error('SmartSelection', '智能服务类型匹配失败', error);
            return null;
        }
    }

    /**
     * @function applyOtaPreset - 应用OTA预设配置
     * @description 应用从OTA Profile Manager传来的预设配置
     * @param {Object} otaPreset - OTA预设配置
     */
    applyOtaPreset(otaPreset) {
        try {
            if (!otaPreset || !otaPreset.otaConfig) {
                logger.warn('SmartSelection', 'OTA预设配置无效');
                return;
            }

            const { otaConfig } = otaPreset;
            
            // 更新当前OTA预设信息
            this.currentOtaPreset = {
                type: otaConfig.type,
                email: otaPreset.email,
                confidence: otaPreset.confidence,
                appliedAt: otaPreset.appliedAt
            };

            // 应用默认选择值
            if (otaConfig.defaultLanguages) {
                this.defaultSelections.languages = otaConfig.defaultLanguages;
            }
            
            if (otaConfig.defaultRegion) {
                this.defaultSelections.region = otaConfig.defaultRegion;
            }
            
            if (otaConfig.defaultBackendUser) {
                this.defaultSelections.backendUser = otaConfig.defaultBackendUser;
            }

            // 应用特殊配置
            if (otaConfig.specialConfig) {
                this.applyOtaSpecialConfig(otaConfig.specialConfig);
            }

            logger.success('SmartSelection', 'OTA预设配置应用成功', {
                otaType: otaConfig.type,
                email: otaPreset.email,
                defaults: this.defaultSelections
            });

        } catch (error) {
            logger.error('SmartSelection', 'OTA预设配置应用失败', error);
        }
    }

    /**
     * @function applyOtaSpecialConfig - 应用OTA特殊配置
     * @description 应用OTA类型相关的特殊配置
     * @param {Object} specialConfig - 特殊配置对象
     */
    applyOtaSpecialConfig(specialConfig) {
        try {
            // 更新语言偏好权重
            if (specialConfig.preferredLanguages) {
                this.selectionWeights.languages = specialConfig.preferredLanguages.reduce((weights, langId) => {
                    weights[langId] = (weights[langId] || 0) + 0.3; // 增加权重
                    return weights;
                }, this.selectionWeights.languages || {});
            }

            // 更新区域限制
            if (specialConfig.restrictedRegions) {
                this.restrictedRegions = specialConfig.restrictedRegions;
            } else if (specialConfig.preferredRegions) {
                this.preferredRegions = specialConfig.preferredRegions;
            }

            // 更新固定后台用户
            if (specialConfig.fixedBackendUser) {
                this.defaultSelections.backendUser = specialConfig.fixedBackendUser;
                this.forceBackendUser = specialConfig.fixedBackendUser;
            }

            // 更新服务类型偏好
            if (specialConfig.serviceTypePreference) {
                this.preferredServiceType = specialConfig.serviceTypePreference;
            }

            // 更新车型偏好
            if (specialConfig.carTypePreference) {
                this.carTypePreference = specialConfig.carTypePreference;
            }

            logger.info('SmartSelection', 'OTA特殊配置应用完成', {
                hasLanguagePrefs: !!specialConfig.preferredLanguages,
                hasRegionRestrictions: !!(specialConfig.restrictedRegions || specialConfig.preferredRegions),
                hasFixedUser: !!specialConfig.fixedBackendUser,
                hasServicePrefs: !!specialConfig.serviceTypePreference
            });

        } catch (error) {
            logger.error('SmartSelection', 'OTA特殊配置应用失败', error);
        }
    }
}

// 模块加载和初始化逻辑（原index.js功能）

/**
 * @function initializeSmartSelection - 初始化智能选择服务
 * @returns {Promise<SmartSelectionService>} 智能选择服务实例
 */
async function initializeSmartSelection() {
    try {
        if (typeof window !== 'undefined') {
            // 浏览器环境
            if (!window.smartSelection) {
                window.smartSelection = new SmartSelectionService();
            }
            
            await window.smartSelection.initialize();
            return window.smartSelection;
        } else {
            // Node.js环境
            const smartSelection = new SmartSelectionService();
            await smartSelection.initialize();
            return smartSelection;
        }
    } catch (error) {
        console.error('SmartSelection', '初始化失败', error);
        throw error;
    }
}

/**
 * @namespace SmartSelectionModules - 智能选择模块命名空间
 * @description 包含所有智能选择相关的类和功能
 */
const SmartSelectionModules = {
    // 版本信息
    version: '4.0.3',
    buildDate: '2025-01-06',
    
    // 模块描述
    modules: {
        'SmartSelectionService': '主服务类 - 智能选择核心功能',
        'EnhancedMatchingEngine': '增强匹配引擎 - 模糊匹配和同义词支持',
        'IntelligentLearningEngine': '智能学习引擎 - 历史数据学习和权重优化',
        'DynamicAccuracyCalculator': '动态精度计算器 - 置信度和可靠性评估',
        'DynamicApiSyncManager': '动态API同步管理器 - 实时数据同步'
    },
    
    // 功能特性
    features: {
        'fuzzyMatching': '模糊匹配算法',
        'synonymSupport': '同义词识别',
        'learningEngine': '机器学习优化',
        'dynamicScoring': '动态评分系统',
        'contextAnalysis': '上下文分析',
        'dynamicApiSync': '动态API同步',
        'pinyinMatching': '拼音匹配',
        'abbreviationMatch': '缩写识别',
        'semanticMatching': '语义匹配',
        'soundexMatching': '音似匹配'
    },
    
    // 性能指标
    performance: {
        'accuracy': '95%+',
        'responseTime': '<2秒',
        'memoryUsage': '轻量级',
        'compatibility': '全浏览器支持'
    }
};

// 模块加载状态检查
function checkModuleStatus() {
    const status = {
        loaded: false,
        modules: {},
        errors: []
    };
    
    try {
        if (typeof window !== 'undefined') {
            // 浏览器环境检查
            status.modules.DynamicApiSyncManager = typeof window.DynamicApiSyncManager !== 'undefined';
            status.modules.EnhancedMatchingEngine = typeof window.EnhancedMatchingEngine !== 'undefined';
            status.modules.IntelligentLearningEngine = typeof window.IntelligentLearningEngine !== 'undefined';
            status.modules.DynamicAccuracyCalculator = typeof window.DynamicAccuracyCalculator !== 'undefined';
            status.modules.SmartSelectionService = typeof window.SmartSelectionService !== 'undefined';
            
            status.loaded = Object.values(status.modules).every(loaded => loaded);
        }
    } catch (error) {
        status.errors.push(error.message);
    }
    
    return status;
}

// 导出到全局对象
if (typeof window !== 'undefined') {
    window.SmartSelectionService = SmartSelectionService;
    window.initializeSmartSelection = initializeSmartSelection;
    window.SmartSelectionModules = SmartSelectionModules;
    window.checkSmartSelectionModuleStatus = checkModuleStatus;
}

logger.info('模块', 'SmartSelection主模块加载完成', { version: '4.0.3' }); 