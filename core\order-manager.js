/**
 * @file order-manager.js - 统一订单管理模块
 * @description 合并订单处理和渲染功能的统一管理器
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.0.3
 */

/**
 * @class OrderManager - 统一订单管理类
 * @description 管理订单的解析、处理、显示、创建和渲染的完整流程
 */
class OrderManager {
    /**
     * @function constructor - 构造函数
     * @description 初始化订单管理器
     * @param {AppState} appState - 应用状态实例
     * @param {OrderParser} orderParser - 订单解析器实例
     * @param {SmartSelectionService} smartSelection - 智能选择服务实例
     */
    constructor(appState, orderParser, smartSelection) {
        this.appState = appState;
        this.orderParser = orderParser;
        this.smartSelection = smartSelection;
        this.currentOrders = [];
        this.processingState = {
            isProcessing: false,
            currentStep: '',
            progress: 0
        };
    }

    // ===== 订单处理功能 (原OrderProcessor) =====

    /**
     * @function handleProcessOrder - 处理订单
     * @description 处理用户输入的订单文本
     */
    async handleProcessOrder() {
        const textInput = document.getElementById('orderText').value.trim();
        const otaType = document.getElementById('otaSelect').value;

        if (!textInput) {
            window.app.interfaceController.showError('请输入订单文本');
            return;
        }

        logger.info('OrderManager', '开始处理订单', {
            textLength: textInput.length,
            otaType: otaType
        });

        try {
            this.processingState.isProcessing = true;
            window.app.interfaceController.showLoading('正在处理订单...');

            const result = await this.orderParser.parseOrders(textInput, otaType);

            if (result.success) {
                this.appState.processedOrders = result.orders;
                this.currentOrders = result.orders;
                this.displayOrderResults(result);
                logger.success('OrderManager', '订单处理完成', {
                    orderCount: result.orders.length
                });
            } else {
                throw new Error(result.error || '订单处理失败');
            }

        } catch (error) {
            logger.error('OrderManager', '订单处理失败', { error: error.message });
            window.app.interfaceController.showError('订单处理失败: ' + error.message);
        } finally {
            this.processingState.isProcessing = false;
            window.app.interfaceController.hideLoading();
        }
    }

    /**
     * @function handleCreateOrders - 创建订单（集成错误恢复机制）
     * @description 批量创建订单，包含完整的错误恢复机制
     */
    async handleCreateOrders() {
        try {
            logger.info('OrderManager', '开始创建订单流程');
            
            // 1. 数据一致性预检查
            const isDataValid = await window.app.dataConsistencyManager.validateUserData();
            if (!isDataValid) {
                logger.warn('OrderManager', '数据一致性检查失败，正在刷新数据...');
                await window.app.dataConsistencyManager.forceDataRefresh();
                
                // 更新UI选择器
                window.app.interfaceController.updateUISelectors();
                
                // 提示用户
                window.app.interfaceController.showError('系统数据已更新，请重新检查选择项');
                return;
            }

            // 显示加载状态
            window.app.interfaceController.showLoading('正在创建订单...');
            
            // 收集订单数据
            const orders = await this.collectAllOrders();
            
            if (!orders || orders.length === 0) {
                window.app.interfaceController.hideLoading();
                window.app.interfaceController.showError('没有找到要创建的订单');
                return;
            }

            logger.info('OrderManager', `准备创建 ${orders.length} 个订单`);

            // 创建结果存储
            const results = [];
            
            // 逐个处理订单（支持错误恢复）
            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                logger.debug('OrderManager', `正在处理第 ${i + 1} 个订单`, order);

                try {
                    // 尝试创建订单
                    const result = await window.app.apiService.createOrder(order);
                    results.push({ 
                        success: true, 
                        result, 
                        orderData: order,
                        index: i 
                    });
                    logger.success('OrderManager', `第 ${i + 1} 个订单创建成功`, result);

                } catch (error) {
                    logger.error('OrderManager', `第 ${i + 1} 个订单创建失败`, error);
                    
                    // 错误分析和恢复尝试
                    const errorAnalysis = window.app.resilienceManager.analyzeError(error);
                    
                    if (errorAnalysis.isRetryable) {
                        logger.info('OrderManager', '尝试错误恢复', errorAnalysis);
                        
                        const recoveryResult = await window.app.resilienceManager.attemptRecovery(
                            errorAnalysis, 
                            order
                        );
                        
                        if (recoveryResult.success) {
                            results.push({ 
                                success: true, 
                                result: recoveryResult.result, 
                                orderData: recoveryResult.orderData,
                                index: i,
                                recovered: true 
                            });
                            logger.success('OrderManager', `第 ${i + 1} 个订单错误恢复成功`);
                        } else {
                            results.push({ 
                                success: false, 
                                error: this.formatErrorMessage(error, recoveryResult),
                                orderData: order,
                                index: i,
                                errorAnalysis 
                            });
                        }
                    } else {
                        // 不可恢复的错误
                        results.push({ 
                            success: false, 
                            error: this.formatErrorMessage(error),
                            orderData: order,
                            index: i,
                            errorAnalysis 
                        });
                    }
                }
            }

            // 隐藏加载状态
            window.app.interfaceController.hideLoading();

            // 显示创建结果
            this.displayCreateResults(results);

            // 如果有成功的订单，添加到状态
            const successfulOrders = results.filter(r => r.success);
            if (successfulOrders.length > 0) {
                this.appState.processedOrders.push(...successfulOrders.map(r => r.orderData));
                
                // 显示成功通知
                if (window.notificationManager) {
                    window.notificationManager.success(
                        '订单创建成功',
                        `成功创建 ${successfulOrders.length}/${results.length} 个订单`
                    );
                }
            }

        } catch (error) {
            logger.error('OrderManager', '创建订单流程失败', error);
            window.app.interfaceController.hideLoading();
            window.app.interfaceController.showError('创建订单过程中发生错误: ' + error.message);
        }
    }

    /**
     * @function collectAllOrders - 收集所有订单数据
     * @description 从UI中收集所有要创建的订单数据
     * @returns {Promise<Array>} 订单数据数组
     */
    async collectAllOrders() {
        const orders = [];

        // 收集已处理的订单
        if (this.appState.processedOrders && this.appState.processedOrders.length > 0) {
            for (const order of this.appState.processedOrders) {
                const processedOrder = await this.processOrderForAPI(order);
                if (processedOrder) {
                    orders.push(processedOrder);
                }
            }
        }

        // 收集手动编辑的订单
        const manualOrders = await this.collectManualOrders();
        orders.push(...manualOrders);

        return orders;
    }

    /**
     * @function collectManualOrders - 收集手动编辑的订单
     * @description 从手动编辑表单中收集订单数据
     * @returns {Promise<Array>} 手动订单数据数组
     */
    async collectManualOrders() {
        const orders = [];
        const forms = document.querySelectorAll('.order-edit-form');

        for (const form of forms) {
            try {
                const orderData = await this.extractOrderDataFromForm(form);
                if (orderData && this.validateOrderData(orderData)) {
                    orders.push(orderData);
                }
            } catch (error) {
                logger.warn('OrderManager', '手动订单数据提取失败', error);
            }
        }

        return orders;
    }

    /**
     * @function extractOrderDataFromForm - 从表单提取订单数据
     * @description 从订单编辑表单中提取数据
     * @param {HTMLElement} form - 表单元素
     * @returns {Promise<object>} 订单数据对象
     */
    async extractOrderDataFromForm(form) {
        const formData = new FormData(form);
        const orderData = {};

        // 提取基本字段
        const fieldMapping = {
            'customerName': 'customer_name',
            'customerContact': 'customer_contact',
            'flightInfo': 'flight_number',
            'pickup': 'pickup_location',
            'destination': 'drop_location',
            'date': 'service_date',
            'time': 'service_time',
            'passengerNumber': 'passenger_count',
            'extraRequirement': 'extra_requirement'
        };

        for (const [formField, apiField] of Object.entries(fieldMapping)) {
            const value = formData.get(formField);
            if (value) {
                orderData[apiField] = value;
            }
        }

        // 处理选择器字段
        const backendUserId = formData.get('backendUser');
        const subCategoryId = formData.get('subCategory');
        const carTypeId = formData.get('carType');
        const drivingRegionId = formData.get('drivingRegion');

        if (backendUserId) orderData.backend_user_id = parseInt(backendUserId);
        if (subCategoryId) orderData.sub_category_id = parseInt(subCategoryId);
        if (carTypeId) orderData.car_type_id = parseInt(carTypeId);
        if (drivingRegionId) orderData.driving_region_id = parseInt(drivingRegionId);

        // 处理语言多选字段
        const languageSelect = form.querySelector('select[name="languages"]');
        if (languageSelect) {
            const selectedLanguages = Array.from(languageSelect.selectedOptions)
                .map(option => parseInt(option.value))
                .filter(id => !isNaN(id));
            
            if (selectedLanguages.length > 0) {
                orderData.languages_id_array = selectedLanguages;
            }
        }

        // 生成OTA参考号
        orderData.ota_reference_number = await this.generateOTAReference('manual', orderData);

        // 确保必需字段
        await this.ensureRequiredApiFields(orderData);

        return orderData;
    }

    /**
     * @function processOrderForAPI - 处理订单数据以适配API
     * @description 将解析的订单数据转换为API格式（支持五维数据）
     * @param {object} order - 原始订单数据
     * @returns {Promise<object>} API格式的订单数据
     */
    async processOrderForAPI(order) {
        try {
            // 复制订单数据
            const apiOrder = { ...order };

            // 确保日期格式正确（DD-MM-YYYY）
            if (apiOrder.service_date) {
                apiOrder.service_date = this.formatDateForAPI(apiOrder.service_date);
            }

            // 确保乘客数量为数字
            if (apiOrder.passenger_count) {
                apiOrder.passenger_count = parseInt(apiOrder.passenger_count) || 1;
            }

            // 生成OTA参考号（如果没有）
            if (!apiOrder.ota_reference_number) {
                apiOrder.ota_reference_number = await this.generateOTAReference('auto', apiOrder);
            }

            // 应用智能选择（五维选择）
            if (this.smartSelection) {
                const smartResult = await this.applySmartSelection(apiOrder);
                Object.assign(apiOrder, smartResult);
            }

            // 处理新增的API参数
            this.processEnhancedApiParameters(apiOrder);

            // 确保所有必需字段
            await this.ensureRequiredApiFields(apiOrder);

            // 清理和格式化最终API数据
            const finalApiData = this.formatFinalApiData(apiOrder);

            return finalApiData;

        } catch (error) {
            logger.error('OrderManager', '订单API格式转换失败', error);
            return null;
        }
    }

    /**
     * @function applySmartSelection - 应用智能选择
     * @description 使用智能选择服务自动选择车型、用户等（五维选择）
     * @param {object} orderData - 订单数据
     * @returns {Promise<object>} 智能选择结果
     */
    async applySmartSelection(orderData) {
        const selections = {};

        try {
            // 使用五维智能选择引擎
            if (this.smartSelection && this.smartSelection.performCompleteAutoSelection) {
                logger.info('OrderManager', '使用五维智能选择引擎');
                
                const completeResult = await this.smartSelection.performCompleteAutoSelection(orderData);
                
                if (completeResult && completeResult.success) {
                    // 映射五维选择结果到API字段
                    if (completeResult.selections.user && !orderData.backend_user_id) {
                        selections.backend_user_id = completeResult.selections.user.backendUserId;
                    }
                    
                    if (completeResult.selections.service && !orderData.sub_category_id) {
                        selections.sub_category_id = completeResult.selections.service.subCategoryId;
                    }
                    
                    if (completeResult.selections.vehicle && !orderData.car_type_id) {
                        selections.car_type_id = completeResult.selections.vehicle.carTypeId;
                    }
                    
                    if (completeResult.selections.region && !orderData.driving_region_id) {
                        selections.driving_region_id = completeResult.selections.region.regionId;
                    }
                    
                    if (completeResult.selections.languages && !orderData.languages_id_array) {
                        selections.languages_id_array = completeResult.selections.languages.map(lang => lang.languageId);
                    }
                    
                    // 保存完整的智能选择结果
                    selections._smartSelection = completeResult;
                    
                    logger.success('OrderManager', '五维智能选择完成', {
                        confidence: completeResult.confidence,
                        dimensions: Object.keys(completeResult.selections)
                    });
                    
                    return selections;
                }
            }

            // 降级到传统三维选择
            logger.info('OrderManager', '使用传统三维智能选择');
            
            // 智能选择车型
            if (!orderData.car_type_id && this.smartSelection) {
                const vehicleResult = this.smartSelection.selectVehicleType(orderData);
                if (vehicleResult && vehicleResult.carTypeId) {
                    selections.car_type_id = vehicleResult.carTypeId;
                    selections._smartSelection = selections._smartSelection || {};
                    selections._smartSelection.vehicle = vehicleResult;
                }
            }

            // 智能选择服务类型
            if (!orderData.sub_category_id && this.smartSelection) {
                const serviceResult = this.smartSelection.selectServiceType(orderData);
                if (serviceResult && serviceResult.subCategoryId) {
                    selections.sub_category_id = serviceResult.subCategoryId;
                    selections._smartSelection = selections._smartSelection || {};
                    selections._smartSelection.service = serviceResult;
                }
            }

            // 智能选择后台用户
            if (!orderData.backend_user_id && this.smartSelection) {
                const userResult = this.smartSelection.selectBackendUser(orderData);
                if (userResult && userResult.backendUserId) {
                    selections.backend_user_id = userResult.backendUserId;
                    selections._smartSelection = selections._smartSelection || {};
                    selections._smartSelection.user = userResult;
                }
            }

            return selections;

        } catch (error) {
            logger.warn('OrderManager', '智能选择应用失败', error);
            return {};
        }
    }

    /**
     * @function processEnhancedApiParameters - 处理增强API参数
     * @description 处理新增的driving_region_id和languages_id_array参数，实现参数覆盖机制
     * @param {object} apiOrder - API订单数据
     */
    processEnhancedApiParameters(apiOrder) {
        try {
            // 处理driving_region_id参数（可选，覆盖sub_category预设）
            if (apiOrder.driving_region_id !== undefined) {
                if (apiOrder.driving_region_id === null || apiOrder.driving_region_id === '') {
                    // 显式设置为null表示不使用区域覆盖
                    delete apiOrder.driving_region_id;
                } else {
                    // 确保driving_region_id为有效数字
                    const regionId = parseInt(apiOrder.driving_region_id);
                    if (!isNaN(regionId) && regionId > 0) {
                        apiOrder.driving_region_id = regionId;
                        logger.debug('OrderManager', '设置区域覆盖参数', { driving_region_id: regionId });
                    } else {
                        delete apiOrder.driving_region_id;
                        logger.warn('OrderManager', '无效的driving_region_id值，已移除', { value: apiOrder.driving_region_id });
                    }
                }
            }

            // 处理languages_id_array参数（可选，覆盖sub_category预设）
            // 根据API文档，支持数组格式[1,2,3]或对象格式{"0":"1","1":"2","2":"3"}
            if (apiOrder.language_ids !== undefined || apiOrder.languages_id_array !== undefined) {
                // 统一处理两种字段名，以languages_id_array为最终字段名
                const languageData = apiOrder.languages_id_array || apiOrder.language_ids;
                
                if (languageData === null || languageData === '') {
                    // 显式设置为null表示不使用语言覆盖
                    delete apiOrder.languages_id_array;
                    delete apiOrder.language_ids;
                } else {
                    const formattedLanguages = this.formatLanguagesArray(languageData);
                    
                    if (formattedLanguages && formattedLanguages.length > 0) {
                        // 根据API文档，使用languages_id_array作为字段名
                        apiOrder.languages_id_array = formattedLanguages;
                        delete apiOrder.language_ids; // 删除旧字段名
                        
                        logger.debug('OrderManager', '设置语言覆盖参数', { 
                            languages_id_array: formattedLanguages,
                            originalFormat: typeof languageData
                        });
                    } else {
                        delete apiOrder.languages_id_array;
                        delete apiOrder.language_ids;
                        logger.warn('OrderManager', '无效的languages数据，已移除', { value: languageData });
                    }
                }
            }

            // 记录参数覆盖状态
            const hasRegionOverride = !!apiOrder.driving_region_id;
            const hasLanguageOverride = !!apiOrder.languages_id_array;
            
            if (hasRegionOverride || hasLanguageOverride) {
                logger.info('OrderManager', '参数覆盖状态', {
                    regionOverride: hasRegionOverride ? apiOrder.driving_region_id : 'none',
                    languageOverride: hasLanguageOverride ? apiOrder.languages_id_array : 'none',
                    note: '这些参数将覆盖sub_category的预设值'
                });
            }

        } catch (error) {
            logger.warn('OrderManager', '增强API参数处理失败', error);
        }
    }

    /**
     * @function formatLanguagesArray - 格式化语言数组参数
     * @description 将各种格式的语言数据转换为标准数组格式
     * @param {Array|Object|string|number} languages - 语言数据
     * @returns {Array|null} 格式化后的语言ID数组
     */
    formatLanguagesArray(languages) {
        try {
            let languageArray = [];

            if (Array.isArray(languages)) {
                // 数组格式: [1, 2, 3]
                languageArray = languages;
            } else if (typeof languages === 'object' && languages !== null) {
                // 对象格式: {"0":"1","1":"2","2":"3"}
                languageArray = Object.values(languages);
            } else if (typeof languages === 'string') {
                // 字符串格式处理
                if (languages.trim().startsWith('[') && languages.trim().endsWith(']')) {
                    // JSON数组字符串: "[1,2,3]"
                    try {
                        languageArray = JSON.parse(languages);
                    } catch {
                        logger.warn('OrderManager', '无法解析JSON格式的语言数组', { languages });
                        return null;
                    }
                } else if (languages.trim().startsWith('{') && languages.trim().endsWith('}')) {
                    // JSON对象字符串: '{"0":"1","1":"2"}'
                    try {
                        const obj = JSON.parse(languages);
                        languageArray = Object.values(obj);
                    } catch {
                        logger.warn('OrderManager', '无法解析JSON格式的语言对象', { languages });
                        return null;
                    }
                } else {
                    // 逗号分隔字符串: "1,2,3"
                    languageArray = languages.split(',').map(id => id.trim());
                }
            } else if (typeof languages === 'number') {
                // 单个数字
                languageArray = [languages];
            } else {
                logger.warn('OrderManager', '不支持的语言数据格式', { languages, type: typeof languages });
                return null;
            }

            // 确保数组中的每个元素都是有效的数字ID
            const validLanguages = languageArray
                .map(id => parseInt(id))
                .filter(id => !isNaN(id) && id > 0);

            // 去重
            const uniqueLanguages = [...new Set(validLanguages)];

            if (uniqueLanguages.length === 0) {
                logger.warn('OrderManager', '语言数组中没有有效的ID', { originalArray: languageArray });
                return null;
            }

            logger.debug('OrderManager', '语言数组格式化成功', {
                original: languages,
                formatted: uniqueLanguages,
                count: uniqueLanguages.length
            });

            return uniqueLanguages;

        } catch (error) {
            logger.error('OrderManager', '语言数组格式化失败', { languages, error });
            return null;
        }
    }

    /**
     * @function formatFinalApiData - 格式化最终API数据
     * @description 清理和格式化最终的API数据，移除不需要的字段
     * @param {object} apiOrder - API订单数据
     * @returns {object} 格式化后的API数据
     */
    formatFinalApiData(apiOrder) {
        try {
            // 创建最终API数据对象
            const finalData = { ...apiOrder };

            // 移除内部使用的字段
            delete finalData._smartSelection;
            delete finalData._originalText;
            delete finalData._parseMetadata;

            // 确保必需字段存在且格式正确
            const requiredStringFields = ['customer_name', 'ota_reference_number'];
            requiredStringFields.forEach(field => {
                if (finalData[field]) {
                    finalData[field] = String(finalData[field]).trim();
                }
            });

            // 确保数字字段格式正确
            const numericFields = ['sub_category_id', 'car_type_id', 'incharge_by_backend_user_id', 'passenger_count', 'driving_region_id'];
            numericFields.forEach(field => {
                if (finalData[field] !== undefined && finalData[field] !== null) {
                    const numValue = parseInt(finalData[field]);
                    if (!isNaN(numValue)) {
                        finalData[field] = numValue;
                    } else {
                        delete finalData[field];
                    }
                }
            });

            // 处理可选字段
            const optionalFields = ['pickup_location', 'drop_location', 'service_date', 'service_time', 'flight_number', 'extra_requirement'];
            optionalFields.forEach(field => {
                if (finalData[field] && String(finalData[field]).trim() === '') {
                    delete finalData[field];
                }
            });

            // 验证languages_id_array数组格式
            if (finalData.languages_id_array && Array.isArray(finalData.languages_id_array)) {
                finalData.languages_id_array = finalData.languages_id_array.filter(id => Number.isInteger(id) && id > 0);
                if (finalData.languages_id_array.length === 0) {
                    delete finalData.languages_id_array;
                }
            }

            // 清理旧字段名（确保没有重复字段）
            if (finalData.language_ids) {
                delete finalData.language_ids;
            }

            logger.debug('OrderManager', '最终API数据格式化完成', {
                fields: Object.keys(finalData),
                hasEnhancedParams: !!(finalData.driving_region_id || finalData.languages_id_array),
                enhancedParams: {
                    driving_region_id: finalData.driving_region_id || 'none',
                    languages_id_array: finalData.languages_id_array || 'none'
                }
            });

            return finalData;

        } catch (error) {
            logger.error('OrderManager', '最终API数据格式化失败', error);
            return apiOrder; // 返回原始数据作为降级
        }
    }

    /**
     * @function generateOTAReference - 生成OTA参考号
     * @description 根据OTA类型生成不同格式的参考号
     * @param {string} otaType - OTA类型
     * @param {object} orderData - 订单数据
     * @returns {Promise<string>} OTA参考号
     */
    async generateOTAReference(otaType, orderData) {
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
        const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');

        switch (otaType) {
            case 'chong-dealer':
                return this.generateChongDealerReference(orderData, dateStr, timeStr);
                
            case 'fallback':
                return await this.generateFallbackReference(orderData, dateStr, timeStr);
                
            default:
                // 默认格式
                const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
                return `OTA${dateStr}${timeStr}${randomNum}`;
        }
    }

    /**
     * @function generateChongDealerReference - 生成Chong Dealer参考号
     * @description 为Chong Dealer订单生成专用格式的参考号
     * @param {object} orderData - 订单数据
     * @param {string} dateStr - 日期字符串
     * @param {string} timeStr - 时间字符串
     * @returns {string} Chong Dealer参考号
     */
    generateChongDealerReference(orderData, dateStr, timeStr) {
        // 提取客人姓名首字母
        const customerInitial = orderData.customer_name ? 
            orderData.customer_name.charAt(0).toUpperCase() : 'X';
        
        // 提取航班号（如果有）
        const flightCode = orderData.flight_number ? 
            orderData.flight_number.replace(/[^A-Z0-9]/g, '').slice(0, 6) : '';
        
        // 随机2位数
        const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0');
        
        return `CD${dateStr}${timeStr}${customerInitial}${flightCode}${randomNum}`;
    }

    /**
     * @function generateFallbackReference - 生成Fallback参考号
     * @description 为通用模板生成参考号，尝试提取原始订单号
     * @param {object} orderData - 订单数据
     * @param {string} dateStr - 日期字符串
     * @param {string} timeStr - 时间字符串
     * @returns {Promise<string>} Fallback参考号
     */
    async generateFallbackReference(orderData, dateStr, timeStr) {
        try {
            // 尝试提取原始订单号
            const originalOrderNumber = await this.extractOriginalOrderNumber(orderData);
            
            if (originalOrderNumber) {
                return `FB${dateStr}${originalOrderNumber}`;
            } else {
                // 如果没有找到原始订单号，使用默认格式
                const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
                return `FB${dateStr}${timeStr}${randomNum}`;
            }
        } catch (error) {
            logger.warn('OrderManager', 'Fallback参考号生成失败，使用默认格式', error);
            const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            return `FB${dateStr}${timeStr}${randomNum}`;
        }
    }

    /**
     * @function extractOriginalOrderNumber - 提取原始订单号
     * @description 从订单数据中提取原始订单号
     * @param {object} orderData - 订单数据
     * @returns {Promise<string|null>} 原始订单号或null
     */
    async extractOriginalOrderNumber(orderData) {
        try {
            // 构建搜索文本
            const searchText = [
                orderData.customer_name,
                orderData.flight_number,
                orderData.extra_requirement,
                orderData.pickup_location,
                orderData.drop_location
            ].filter(Boolean).join(' ');

            if (!searchText) return null;

            // 使用LLM服务提取订单号
            if (this.orderParser && this.orderParser.llmService) {
                const extractedNumber = await this.orderParser.llmService.extractOrderNumber(searchText);
                if (extractedNumber) {
                    return extractedNumber;
                }
            }

            // 备用正则表达式提取
            return this.extractOrderNumberWithRegex(searchText);

        } catch (error) {
            logger.warn('OrderManager', '原始订单号提取失败', error);
            return null;
        }
    }

    /**
     * @function extractOrderNumberWithRegex - 使用正则表达式提取订单号
     * @description 使用正则表达式从文本中提取订单号
     * @param {string} text - 搜索文本
     * @returns {string|null} 提取的订单号或null
     */
    extractOrderNumberWithRegex(text) {
        // 常见订单号模式
        const patterns = [
            /[A-Z]{2,3}\d{6,}/g,           // 字母+数字组合 (如: ABC123456)
            /\d{8,}/g,                     // 纯数字 (如: 12345678)
            /[A-Z]\d{7,}/g,                // 单字母+数字 (如: A1234567)
            /\d{4}-\d{4}/g,                // 分隔符格式 (如: 1234-5678)
            /[A-Z]{1,2}-\d{6,}/g           // 字母-数字格式 (如: AB-123456)
        ];

        for (const pattern of patterns) {
            const matches = text.match(pattern);
            if (matches && matches.length > 0) {
                return matches[0];
            }
        }

        return null;
    }

    /**
     * @function ensureRequiredApiFields - 确保必需的API字段
     * @description 确保订单数据包含所有必需的API字段（支持五维数据）
     * @param {object} orderData - 订单数据
     */
    async ensureRequiredApiFields(orderData) {
        // 必需字段默认值
        const requiredDefaults = {
            sub_category_id: 1,        // 默认服务类型
            car_type_id: 1,           // 默认车型
            incharge_by_backend_user_id: 1  // 默认后台用户
        };

        // 设置默认值
        for (const [field, defaultValue] of Object.entries(requiredDefaults)) {
            if (!orderData[field]) {
                orderData[field] = defaultValue;
            }
        }

        // 确保OTA参考号
        if (!orderData.ota_reference_number) {
            orderData.ota_reference_number = await this.generateOTAReference('auto', orderData);
        }

        // 映射backend_user_id到incharge_by_backend_user_id
        if (orderData.backend_user_id && !orderData.incharge_by_backend_user_id) {
            orderData.incharge_by_backend_user_id = orderData.backend_user_id;
        }

        // 处理增强字段的默认值和验证
        await this.ensureEnhancedApiFields(orderData);
    }

    /**
     * @function ensureEnhancedApiFields - 确保增强API字段
     * @description 处理driving_region_id和language_ids的默认值和验证
     * @param {object} orderData - 订单数据
     */
    async ensureEnhancedApiFields(orderData) {
        try {
            // 处理driving_region_id默认值
            if (!orderData.driving_region_id && this.appState.drivingRegions && this.appState.drivingRegions.length > 0) {
                // 尝试根据地址信息智能选择区域
                const regionResult = await this.selectDefaultDrivingRegion(orderData);
                if (regionResult) {
                    orderData.driving_region_id = regionResult.regionId;
                    logger.info('OrderManager', '自动选择驾驶区域', {
                        regionId: regionResult.regionId,
                        regionName: regionResult.regionName,
                        confidence: regionResult.confidence
                    });
                }
            }

            // 处理languages_id_array默认值
            if (!orderData.languages_id_array && this.appState.languages && this.appState.languages.length > 0) {
                // 尝试根据客户信息智能选择语言
                const languageResult = await this.selectDefaultLanguages(orderData);
                if (languageResult && languageResult.length > 0) {
                    orderData.languages_id_array = languageResult.map(lang => lang.languageId);
                    logger.info('OrderManager', '自动选择语言', {
                        languageIds: orderData.languages_id_array,
                        languages: languageResult.map(lang => lang.languageName)
                    });
                }
            }

            // 验证增强字段的有效性
            this.validateEnhancedApiFields(orderData);

        } catch (error) {
            logger.warn('OrderManager', '增强API字段处理失败', error);
        }
    }

    /**
     * @function selectDefaultDrivingRegion - 选择默认驾驶区域
     * @description 根据订单信息智能选择默认的驾驶区域
     * @param {object} orderData - 订单数据
     * @returns {Promise<object|null>} 区域选择结果
     */
    async selectDefaultDrivingRegion(orderData) {
        try {
            // 如果有智能选择服务且支持区域选择
            if (this.smartSelection && this.smartSelection.selectDrivingRegion) {
                const regionResult = this.smartSelection.selectDrivingRegion(orderData);
                if (regionResult && regionResult.regionId) {
                    return regionResult;
                }
            }

            // 降级策略：根据地址关键词匹配
            const addressText = [
                orderData.pickup_location,
                orderData.drop_location
            ].filter(Boolean).join(' ').toLowerCase();

            if (addressText) {
                // 简单的关键词匹配
                const regionMappings = [
                    { keywords: ['kl', 'kuala lumpur', 'selangor', 'petaling'], regionId: 1, regionName: 'KL/Selangor' },
                    { keywords: ['penang', 'georgetown', 'butterworth'], regionId: 2, regionName: 'Penang' },
                    { keywords: ['johor', 'jb', 'johor bahru'], regionId: 3, regionName: 'Johor' },
                    { keywords: ['singapore', 'changi', 'sg'], regionId: 4, regionName: 'Singapore' },
                    { keywords: ['sabah', 'kota kinabalu', 'kk'], regionId: 5, regionName: 'Sabah' },
                    { keywords: ['malacca', 'melaka'], regionId: 6, regionName: 'Malacca' }
                ];

                for (const mapping of regionMappings) {
                    if (mapping.keywords.some(keyword => addressText.includes(keyword))) {
                        return {
                            regionId: mapping.regionId,
                            regionName: mapping.regionName,
                            confidence: 0.7,
                            method: 'keyword_matching'
                        };
                    }
                }
            }

            // 如果无法匹配，返回第一个可用区域作为默认值
            if (this.appState.drivingRegions && this.appState.drivingRegions.length > 0) {
                const defaultRegion = this.appState.drivingRegions[0];
                return {
                    regionId: defaultRegion.id,
                    regionName: defaultRegion.name,
                    confidence: 0.3,
                    method: 'default_fallback'
                };
            }

            return null;

        } catch (error) {
            logger.warn('OrderManager', '默认驾驶区域选择失败', error);
            return null;
        }
    }

    /**
     * @function selectDefaultLanguages - 选择默认语言
     * @description 根据订单信息智能选择默认的语言
     * @param {object} orderData - 订单数据
     * @returns {Promise<Array>} 语言选择结果
     */
    async selectDefaultLanguages(orderData) {
        try {
            // 如果有智能选择服务且支持语言选择
            if (this.smartSelection && this.smartSelection.selectLanguages) {
                const languageResult = this.smartSelection.selectLanguages(orderData);
                if (languageResult && languageResult.length > 0) {
                    return languageResult;
                }
            }

            // 降级策略：根据客户姓名和文本内容推断语言
            const defaultLanguages = [];

            // 检查是否包含中文字符
            const hasChineseChars = /[\u4e00-\u9fff]/.test(
                [orderData.customer_name, orderData.extra_requirement, orderData.pickup_location, orderData.drop_location]
                .filter(Boolean).join('')
            );

            // 查找可用的语言
            const availableLanguages = this.appState.languages || [];
            const englishLang = availableLanguages.find(lang => 
                lang.name && (lang.name.toLowerCase().includes('english') || lang.code === 'en')
            );
            const chineseLang = availableLanguages.find(lang => 
                lang.name && (lang.name.toLowerCase().includes('chinese') || lang.code === 'zh')
            );

            // 默认添加英语
            if (englishLang) {
                defaultLanguages.push({
                    languageId: englishLang.id,
                    languageName: englishLang.name,
                    confidence: 0.8
                });
            }

            // 如果检测到中文，添加中文
            if (hasChineseChars && chineseLang) {
                defaultLanguages.push({
                    languageId: chineseLang.id,
                    languageName: chineseLang.name,
                    confidence: 0.9
                });
            }

            // 如果没有找到合适的语言，返回第一个可用语言
            if (defaultLanguages.length === 0 && availableLanguages.length > 0) {
                defaultLanguages.push({
                    languageId: availableLanguages[0].id,
                    languageName: availableLanguages[0].name,
                    confidence: 0.3
                });
            }

            return defaultLanguages;

        } catch (error) {
            logger.warn('OrderManager', '默认语言选择失败', error);
            return [];
        }
    }

    /**
     * @function validateEnhancedApiFields - 验证增强API字段
     * @description 验证driving_region_id和language_ids的有效性
     * @param {object} orderData - 订单数据
     */
    validateEnhancedApiFields(orderData) {
        try {
            // 验证driving_region_id
            if (orderData.driving_region_id) {
                const validRegionIds = (this.appState.drivingRegions || []).map(region => region.id);
                if (!validRegionIds.includes(orderData.driving_region_id)) {
                    logger.warn('OrderManager', '无效的驾驶区域ID', {
                        providedId: orderData.driving_region_id,
                        validIds: validRegionIds
                    });
                    delete orderData.driving_region_id;
                }
            }

            // 验证languages_id_array
            if (orderData.languages_id_array && Array.isArray(orderData.languages_id_array)) {
                const validLanguageIds = (this.appState.languages || []).map(lang => lang.id);
                const validatedIds = orderData.languages_id_array.filter(id => validLanguageIds.includes(id));
                
                if (validatedIds.length !== orderData.languages_id_array.length) {
                    logger.warn('OrderManager', '部分语言ID无效', {
                        providedIds: orderData.languages_id_array,
                        validIds: validatedIds,
                        availableIds: validLanguageIds
                    });
                }
                
                orderData.languages_id_array = validatedIds.length > 0 ? validatedIds : undefined;
                if (!orderData.languages_id_array) {
                    delete orderData.languages_id_array;
                }
            }

        } catch (error) {
            logger.warn('OrderManager', '增强API字段验证失败', error);
        }
    }

    /**
     * @function formatDateForAPI - 格式化日期为API格式
     * @description 将日期格式化为DD-MM-YYYY格式
     * @param {string} dateStr - 日期字符串
     * @returns {string} 格式化后的日期
     */
    formatDateForAPI(dateStr) {
        try {
            const date = new Date(dateStr);
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}-${month}-${year}`;
        } catch (error) {
            logger.warn('OrderManager', '日期格式化失败', { dateStr, error });
            return dateStr;
        }
    }

    /**
     * @function validateOrderData - 验证订单数据
     * @description 验证订单数据的完整性和有效性
     * @param {object} orderData - 订单数据
     * @returns {boolean} 验证结果
     */
    validateOrderData(orderData) {
        // 必需字段检查
        const requiredFields = ['customer_name', 'ota_reference_number'];
        
        for (const field of requiredFields) {
            if (!orderData[field] || orderData[field].toString().trim() === '') {
                logger.warn('OrderManager', `订单验证失败：缺少必需字段 ${field}`, orderData);
                return false;
            }
        }

        return true;
    }

    /**
     * @function formatErrorMessage - 格式化错误消息
     * @description 格式化错误消息以便用户理解
     * @param {Error} error - 错误对象
     * @param {object} recoveryResult - 恢复结果
     * @returns {string} 格式化的错误消息
     */
    formatErrorMessage(error, recoveryResult = null) {
        let message = error.message || '未知错误';
        
        if (recoveryResult && !recoveryResult.success) {
            message += ` (恢复尝试失败: ${recoveryResult.error || '未知原因'})`;
        }
        
        return message;
    }

    /**
     * @function handleEditResults - 处理编辑结果
     * @description 启用订单编辑模式
     */
    handleEditResults() {
        logger.info('OrderManager', '启用订单编辑模式');

        // 显示手动编辑区域
        window.app.interfaceController.toggleSection('manualEditSection', true);

        // 为每个订单创建编辑表单
        this.createOrderEditForms();

        window.app.interfaceController.showInfo('编辑模式已启用，您可以修改订单信息');
    }

    /**
     * @function handleAddOrder - 添加新订单
     * @description 添加新的空白订单表单
     */
    handleAddOrder() {
        this.addNewOrderForm();
    }

    /**
     * @function handleRefreshResults - 刷新结果
     * @description 重新处理当前订单文本
     */
    async handleRefreshResults() {
        const textInput = document.getElementById('orderText').value.trim();
        if (!textInput) {
            window.app.interfaceController.showError('没有可重新处理的订单文本');
            return;
        }

        await this.handleProcessOrder();
    }

    /**
     * @function handleReAnalyze - 重新分析
     * @description 使用备用LLM重新分析订单
     */
    async handleReAnalyze() {
        const textInput = document.getElementById('orderText').value.trim();
        if (!textInput) {
            window.app.interfaceController.showError('没有可重新分析的订单文本');
            return;
        }

        try {
            window.app.interfaceController.showLoading('正在重新分析...');

            // 强制使用备用LLM重新分析
            const result = await this.orderParser.parseOrders(textInput, 'fallback');

            if (result.success) {
                this.appState.processedOrders = result.orders;
                this.currentOrders = result.orders;
                this.displayOrderResults(result);
                window.app.interfaceController.showSuccess('重新分析完成');
            } else {
                throw new Error(result.error || '重新分析失败');
            }

        } catch (error) {
            logger.error('OrderManager', '重新分析失败', error);
            window.app.interfaceController.showError('重新分析失败: ' + error.message);
        } finally {
            window.app.interfaceController.hideLoading();
        }
    }

    // ===== 订单渲染功能 (原OrderRenderer) =====

    /**
     * @function displayOrderResults - 显示订单处理结果
     * @description 在UI中显示订单解析和处理的结果
     * @param {object} result - 处理结果
     */
    displayOrderResults(result) {
        const resultsDiv = document.getElementById('orderResults');
        if (!resultsDiv) return;

        let html = `
            <div class="results-header">
                <h3>订单处理结果</h3>
                <div class="results-summary">
                    <span class="order-count">识别到 ${result.orders.length} 个订单</span>
                    <span class="ota-type">OTA类型: ${result.otaType || '未知'}</span>
                </div>
            </div>
        `;

        if (result.orders.length > 0) {
            html += '<div class="orders-list">';
            result.orders.forEach((order, index) => {
                html += this.renderOrderCard(order, index);
            });
            html += '</div>';

            // 显示操作按钮
            html += `
                <div class="results-actions">
                    <button id="editBtn" class="btn btn-secondary">编辑订单</button>
                    <button id="createOrderBtn" class="btn btn-primary">创建订单</button>
                </div>
            `;
        } else {
            html += '<div class="no-orders">未识别到有效订单</div>';
        }

        resultsDiv.innerHTML = html;

        // 显示结果区域
        window.app.interfaceController.toggleSection('resultPreview', true);
        window.app.interfaceController.scrollToSection('resultPreview');
    }

    /**
     * @function renderOrderCard - 渲染订单卡片
     * @description 渲染单个订单的显示卡片
     * @param {object} order - 订单数据
     * @param {number} index - 订单索引
     * @returns {string} 订单卡片HTML
     */
    renderOrderCard(order, index) {
        return `
            <div class="order-card" data-order-index="${index}">
                <div class="order-header">
                    <h4>订单 ${index + 1}</h4>
                    <span class="order-reference">${order.ota_reference_number || '待生成'}</span>
                </div>
                <div class="order-details">
                    <div class="detail-row">
                        <label>客人姓名:</label>
                        <span>${order.customer_name || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <label>联系方式:</label>
                        <span>${order.customer_contact || '未提供'}</span>
                    </div>
                    <div class="detail-row">
                        <label>接送地点:</label>
                        <span>${order.pickup_location || '未知'} → ${order.drop_location || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <label>服务时间:</label>
                        <span>${order.service_date || '未知'} ${order.service_time || ''}</span>
                    </div>
                    <div class="detail-row">
                        <label>乘客人数:</label>
                        <span>${order.passenger_count || 1}人</span>
                    </div>
                    ${order.flight_number ? `
                    <div class="detail-row">
                        <label>航班信息:</label>
                        <span>${order.flight_number}</span>
                    </div>
                    ` : ''}
                    ${order.extra_requirement ? `
                    <div class="detail-row">
                        <label>特殊要求:</label>
                        <span>${order.extra_requirement}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * @function displayCreateResults - 显示创建结果
     * @description 显示订单创建的结果
     * @param {Array} results - 创建结果数组
     */
    displayCreateResults(results) {
        const resultsDiv = document.getElementById('orderResults');
        if (!resultsDiv) return;

        const successCount = results.filter(r => r.success).length;
        const failureCount = results.length - successCount;

        let html = `
            <div class="create-results-header">
                <h3>订单创建结果</h3>
                <div class="results-summary">
                    <span class="success-count">成功: ${successCount}</span>
                    <span class="failure-count">失败: ${failureCount}</span>
                    <span class="total-count">总计: ${results.length}</span>
                </div>
            </div>
        `;

        html += '<div class="create-results-list">';
        results.forEach((result, index) => {
            html += this.renderCreateResultCard(result, index);
        });
        html += '</div>';

        resultsDiv.innerHTML = html;
        window.app.interfaceController.scrollToSection('resultPreview');
    }

    /**
     * @function renderCreateResultCard - 渲染创建结果卡片
     * @description 渲染单个订单创建结果的卡片
     * @param {object} result - 创建结果
     * @param {number} index - 结果索引
     * @returns {string} 结果卡片HTML
     */
    renderCreateResultCard(result, index) {
        const statusClass = result.success ? 'success' : 'failure';
        const statusIcon = result.success ? '✅' : '❌';
        const recoveredBadge = result.recovered ? '<span class="recovered-badge">已恢复</span>' : '';

        return `
            <div class="create-result-card ${statusClass}">
                <div class="result-header">
                    <span class="status-icon">${statusIcon}</span>
                    <h4>订单 ${index + 1}</h4>
                    ${recoveredBadge}
                </div>
                <div class="result-details">
                    <div class="customer-info">
                        <strong>${result.orderData.customer_name || '未知客人'}</strong>
                        <span>${result.orderData.pickup_location || '未知'} → ${result.orderData.drop_location || '未知'}</span>
                    </div>
                    ${result.success ? `
                        <div class="success-info">
                            <span class="order-id">订单ID: ${result.result.id || '未知'}</span>
                            <span class="reference">参考号: ${result.orderData.ota_reference_number || '未知'}</span>
                        </div>
                    ` : `
                        <div class="error-info">
                            <span class="error-message">${result.error || '未知错误'}</span>
                        </div>
                    `}
                </div>
            </div>
        `;
    }

    /**
     * @function createOrderEditForms - 创建订单编辑表单
     * @description 为每个处理的订单创建编辑表单
     */
    createOrderEditForms() {
        const editContainer = document.getElementById('manualEditContainer');
        if (!editContainer) return;

        let html = '<h3>编辑订单信息</h3>';

        if (this.appState.processedOrders.length > 0) {
            this.appState.processedOrders.forEach((order, index) => {
                html += this.createOrderEditForm(order, index);
            });
        } else {
            html += this.createOrderEditForm({}, 0);
        }

        editContainer.innerHTML = html;
    }

    /**
     * @function createOrderEditForm - 创建单个订单编辑表单
     * @description 创建单个订单的编辑表单
     * @param {object} order - 订单数据
     * @param {number} index - 订单索引
     * @returns {string} 表单HTML
     */
    createOrderEditForm(order, index) {
        return `
            <div class="order-edit-form" data-order-index="${index}">
                <h4>订单 ${index + 1}</h4>
                <form class="edit-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerName_${index}">客人姓名 *</label>
                            <input type="text" id="customerName_${index}" name="customerName" 
                                   value="${order.customer_name || ''}" required>
                        </div>
                        <div class="form-group">
                            <label for="customerContact_${index}">联系方式</label>
                            <input type="text" id="customerContact_${index}" name="customerContact" 
                                   value="${order.customer_contact || ''}">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="pickup_${index}">接送地点 *</label>
                            <input type="text" id="pickup_${index}" name="pickup" 
                                   value="${order.pickup_location || ''}" required>
                        </div>
                        <div class="form-group">
                            <label for="destination_${index}">目的地 *</label>
                            <input type="text" id="destination_${index}" name="destination" 
                                   value="${order.drop_location || ''}" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="date_${index}">服务日期</label>
                            <input type="date" id="date_${index}" name="date" 
                                   value="${order.service_date || ''}">
                        </div>
                        <div class="form-group">
                            <label for="time_${index}">服务时间</label>
                            <input type="time" id="time_${index}" name="time" 
                                   value="${order.service_time || ''}">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="passengerNumber_${index}">乘客人数</label>
                            <input type="number" id="passengerNumber_${index}" name="passengerNumber" 
                                   value="${order.passenger_count || 1}" min="1">
                        </div>
                        <div class="form-group">
                            <label for="flightInfo_${index}">航班信息</label>
                            <input type="text" id="flightInfo_${index}" name="flightInfo" 
                                   value="${order.flight_number || ''}">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="extraRequirement_${index}">特殊要求</label>
                        <textarea id="extraRequirement_${index}" name="extraRequirement" 
                                  rows="2">${order.extra_requirement || ''}</textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="backendUser_${index}">后台用户</label>
                            <select id="backendUser_${index}" name="backendUser">
                                <option value="">选择后台用户...</option>
                                ${this.renderBackendUserOptions(order.backend_user_id)}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="subCategory_${index}">服务类型</label>
                            <select id="subCategory_${index}" name="subCategory">
                                <option value="">选择服务类型...</option>
                                ${this.renderSubCategoryOptions(order.sub_category_id)}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="carType_${index}">车型</label>
                            <select id="carType_${index}" name="carType">
                                <option value="">选择车型...</option>
                                ${this.renderCarTypeOptions(order.car_type_id)}
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="drivingRegion_${index}">行驶区域</label>
                            <select id="drivingRegion_${index}" name="drivingRegion">
                                <option value="">智能选择</option>
                                ${this.renderDrivingRegionOptions(order.driving_region_id)}
                            </select>
                            <small class="form-help">可选：根据地址智能匹配</small>
                        </div>
                        <div class="form-group">
                            <label for="languages_${index}">语言要求</label>
                            <select id="languages_${index}" name="languages" multiple>
                                ${this.renderLanguageOptions(order.languages_id_array)}
                            </select>
                            <small class="form-help">可选：按住Ctrl多选</small>
                        </div>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * @function renderBackendUserOptions - 渲染后台用户选项
     * @description 渲染后台用户下拉选项
     * @param {number} selectedId - 选中的ID
     * @returns {string} 选项HTML
     */
    renderBackendUserOptions(selectedId) {
        return this.appState.backendUsers.map(user => 
            `<option value="${user.id}" ${user.id === selectedId ? 'selected' : ''}>
                ${user.name} (${user.phone})
            </option>`
        ).join('');
    }

    /**
     * @function renderSubCategoryOptions - 渲染服务类型选项
     * @description 渲染服务类型下拉选项
     * @param {number} selectedId - 选中的ID
     * @returns {string} 选项HTML
     */
    renderSubCategoryOptions(selectedId) {
        return this.appState.subCategories.map(category => 
            `<option value="${category.id}" ${category.id === selectedId ? 'selected' : ''}>
                ${category.name}
            </option>`
        ).join('');
    }

    /**
     * @function renderCarTypeOptions - 渲染车型选项
     * @description 渲染车型下拉选项
     * @param {number} selectedId - 选中的ID
     * @returns {string} 选项HTML
     */
    renderCarTypeOptions(selectedId) {
        return this.appState.carTypes.map(carType => 
            `<option value="${carType.id}" ${carType.id === selectedId ? 'selected' : ''}>
                ${carType.type} (${carType.seat_number}座)
            </option>`
        ).join('');
    }

    /**
     * @function renderDrivingRegionOptions - 渲染行驶区域选项
     * @description 渲染行驶区域下拉选项
     * @param {number} selectedId - 选中的ID
     * @returns {string} 选项HTML
     */
    renderDrivingRegionOptions(selectedId) {
        if (!this.appState.drivingRegions || this.appState.drivingRegions.length === 0) {
            return '<option value="" disabled>数据加载中...</option>';
        }
        
        return this.appState.drivingRegions.map(region => 
            `<option value="${region.id}" ${region.id === selectedId ? 'selected' : ''}>
                ${region.name}
            </option>`
        ).join('');
    }

    /**
     * @function renderLanguageOptions - 渲染语言选项
     * @description 渲染语言多选选项
     * @param {Array} selectedIds - 选中的ID数组
     * @returns {string} 选项HTML
     */
    renderLanguageOptions(selectedIds = []) {
        if (!this.appState.languages || this.appState.languages.length === 0) {
            return '<option value="" disabled>数据加载中...</option>';
        }
        
        return this.appState.languages.map(language => {
            const isSelected = selectedIds.includes(language.id);
            return `<option value="${language.id}" ${isSelected ? 'selected' : ''}>
                ${language.name}
            </option>`;
        }).join('');
    }

    /**
     * @function addNewOrderForm - 添加新订单表单
     * @description 添加新的空白订单编辑表单
     */
    addNewOrderForm() {
        const editContainer = document.getElementById('manualEditContainer');
        if (!editContainer) return;

        const currentForms = editContainer.querySelectorAll('.order-edit-form');
        const newIndex = currentForms.length;
        
        const newFormHTML = this.createOrderEditForm({}, newIndex);
        editContainer.insertAdjacentHTML('beforeend', newFormHTML);
        
        window.app.interfaceController.showInfo('已添加新订单表单');
        window.app.interfaceController.scrollToSection('manualEditSection');
    }
}

// 导出到全局作用域（向后兼容）
window.OrderManager = OrderManager;

logger.info('模块', 'OrderManager统一模块加载完成', { version: 'v4.0.3' }); 