<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统</title>
    <link rel="stylesheet" href="assets/styles.css">
    <link rel="stylesheet" href="assets/logger.css">
    <link rel="stylesheet" href="assets/notification.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <!-- 登录窗口 -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <h2>系统登录</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" value="" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" value="" required>
                </div>
                <button type="submit">登录</button>
                <div id="loginError" class="error-message"></div>
            </form>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="mainApp" class="hidden">
        <header>
            <h1>OTA订单处理系统</h1>
            <div class="header-controls">
                <!-- 本地模式指示器 -->
                <div class="llm-status-indicator local-mode-indicator" id="localModeIndicator" title="本地模式状态">
                    <div class="status-light connected" id="localModeLight"></div>
                    <span class="status-text" id="localModeText">本地模式</span>
                    <span class="llm-label">数据模式</span>
                </div>

                <!-- Gemini 连接状态指示器 -->
                <div class="llm-status-indicator gemini-indicator" id="geminiStatusIndicator" title="点击检测 Gemini API 连接状态">
                    <div class="status-light" id="geminiStatusLight"></div>
                    <span class="status-text" id="geminiStatusText">Gemini 检测中...</span>
                    <span class="llm-label">AI服务</span>
                </div>

                <div class="user-info">
                    <span id="userInfo"></span>
                    <button id="logoutBtn" style="display: none;">退出登录</button>
                </div>
            </div>
        </header>

        <main>
            <!-- 订单输入区域 -->
            <section id="orderInput" class="section">
                <h2>订单内容输入</h2>
                <div class="input-tabs">
                    <button class="tab-btn active" data-tab="text">文字输入</button>
                    <button class="tab-btn" data-tab="image">图片上传</button>
                </div>

                <!-- 文字输入 -->
                <div id="textInput" class="tab-content active">
                    <textarea id="orderText" placeholder="请输入订单内容...\n\n示例：\n1.28接机：KE671 22.20抵达\n1.30送机：AK378 16.20起飞\n\n联系人：张梦媛\n人数：6\n车型：商务十座\n酒店：Santa Grand Signature\n\nJY"></textarea>
                </div>

                <!-- 图片上传 -->
                <div id="imageInput" class="tab-content">
                    <div class="upload-area" id="uploadArea">
                        <p>点击或拖拽图片到此处上传</p>
                        <input type="file" id="imageFile" accept="image/*" multiple title="选择要处理的图片文件">
                    </div>
                    <div id="imagePreview" class="image-preview"></div>
                    
                    <!-- 图片分析结果预览 -->
                    <div id="imageAnalysisResult" class="image-analysis-result hidden">
                        <h3>图片分析结果</h3>
                        <div class="analysis-tabs">
                            <button class="analysis-tab-btn active" data-analysis-tab="text">提取文字</button>
                            <button class="analysis-tab-btn" data-analysis-tab="details">详细分析</button>
                        </div>
                        <div id="extractedText" class="analysis-content active">
                            <textarea id="extractedTextContent" readonly placeholder="从图片中提取的文字将显示在这里..."></textarea>
                        </div>
                        <div id="analysisDetails" class="analysis-content">
                            <div id="imageLabels" class="analysis-section">
                                <h4>图片标签</h4>
                                <div id="labelsList"></div>
                            </div>
                            <div id="imageObjects" class="analysis-section">
                                <h4>检测到的物体</h4>
                                <div id="objectsList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- OTA选择 -->
                <div class="ota-selection">
                    <label for="otaSelect">选择OTA类型:</label>
                    <select id="otaSelect">
                        <option value="auto">自动识别</option>
                        <option value="chong-dealer">Chong Dealer</option>
                        <option value="fallback">通用回退模板</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <button id="processBtn" class="primary-btn">处理订单</button>
            </section>

            <!-- 处理结果预览 -->
            <section id="resultPreview" class="section hidden">
                <h2>处理结果预览</h2>
                <div class="result-controls">
                    <button id="editBtn">编辑结果</button>
                    <button id="refreshBtn">重新处理</button>
                </div>

                <!-- 智能选择控制器 -->
                <div class="smart-selection-controls">
                    <h3>智能选择设置</h3>
                    
                    <!-- OTA Profile 选择器 -->
                    <div class="profile-section">
                        <div class="form-group">
                            <label for="otaProfileSelect">OTA 模板:</label>
                            <select id="otaProfileSelect" class="form-control">
                                <option value="general">通用模板</option>
                                <option value="chong-dealer">Chong Dealer 模板</option>
                            </select>
                        </div>
                        
                        <div class="profile-status">
                            <span class="profile-indicator" id="profileIndicator">
                                <i class="icon-profile">👤</i>
                                <span id="currentProfileName">通用模板</span>
                            </span>
                            <button type="button" class="btn-profile-info" id="profileInfoBtn" title="查看Profile配置">
                                <i class="icon-info">ℹ️</i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 核心选择器 -->
                    <div class="selection-row">
                        <div class="form-group">
                            <label for="backendUserSelect">负责用户:</label>
                            <select id="backendUserSelect">
                                <option value="">选择用户</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="subCategorySelect">服务类型:</label>
                            <select id="subCategorySelect">
                                <option value="">自动识别</option>
                                <option value="pickup">接机服务</option>
                                <option value="dropoff">送机服务</option>
                                <option value="charter">包车服务</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="carTypeSelect">车型:</label>
                            <select id="carTypeSelect">
                                <option value="">选择车型</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 扩展选择器 -->
                    <div class="selection-row">
                        <div class="form-group">
                            <label for="drivingRegionSelect">行驶区域:</label>
                            <select id="drivingRegionSelect" class="form-control">
                                <option value="">智能选择</option>
                                <!-- 动态生成区域选项 -->
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="languagesSelect">服务语言:</label>
                            <select id="languagesSelect" class="form-control" multiple>
                                <!-- 支持多选的语言选项 -->
                            </select>
                            <small class="form-help">按住Ctrl键可多选</small>
                        </div>
                    </div>
                </div>

                <div id="resultContent" class="result-content">
                    <!-- 订单处理结果 -->
                    <div id="orderResults"></div>

                    <!-- 手动编辑区域 -->
                    <div id="manualEditSection" class="manual-edit-section hidden">
                        <h3>手动编辑订单</h3>
                        <div class="edit-controls">
                            <button id="addOrderBtn" class="secondary-btn">添加新订单</button>
                            <button id="reAnalyzeBtn" class="secondary-btn">重新分析</button>
                        </div>
                        <div id="orderEditForms" class="order-edit-forms">
                            <!-- 动态生成的订单编辑表单 -->
                        </div>
                    </div>

                    <!-- 图片处理结果 -->
                    <div id="imageResults"></div>
                </div>
                <div class="action-buttons">
                    <button id="createOrderBtn" class="primary-btn">创建订单</button>
                    <button id="exportBtn">导出结果</button>
                </div>
            </section>

            <!-- 订单创建状态 -->
            <section id="orderStatus" class="section hidden">
                <h2>订单创建状态</h2>
                <div id="statusContent">
                    <!-- 创建结果 -->
                    <div id="createResults"></div>
                </div>
            </section>
        </main>
    </div>

    <!-- 加载提示 -->
    <div id="loadingModal" class="modal hidden">
        <div class="modal-content">
            <div class="loading-spinner"></div>
            <p id="loadingText">正在处理...</p>
        </div>
    </div>

    <!-- Profile 配置预览弹窗 -->
    <div class="profile-preview-modal" id="profilePreviewModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Profile 配置预览</h3>
                <button class="modal-close" id="profileModalClose">&times;</button>
            </div>
            <div class="modal-body" id="profilePreviewContent">
                <!-- 动态生成 Profile 配置内容 -->
            </div>
        </div>
    </div>

    <!-- 关键路径优先加载 -->
    
    <!-- 第一优先级：基础设施（同步加载） -->
    <script src="core/config.js"></script>
    <script src="core/logger.js"></script>
    <script src="core/test-data-provider.js"></script>
    <script src="core/local-data-provider.js"></script>

    <!-- 第二优先级：状态管理（同步加载） -->
    <script src="core/app-state.js"></script>

    <!-- 第三优先级：核心服务（同步加载） -->
    <script src="services/api-service.js"></script>
    <script src="core/interface-controller.js"></script>
    <script src="components/notification.js"></script>

    <!-- 第四优先级：模块加载器（同步加载） -->
    <script src="core/module-loader.js"></script>
    
    <!-- 第五优先级：主应用（同步加载） -->
    <script src="core/app.js"></script>
    
    <!-- 延迟加载的核心业务模块 -->
    <script>
        // 自动启用本地模式
        document.addEventListener('DOMContentLoaded', () => {
            if (window.localDataProvider) {
                window.localDataProvider.enableLocalMode();
                logger.info('系统', '本地模式已自动启用，将跳过登录环节');
            }
        });

        // 页面加载完成后开始分层加载
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // 第二批：核心业务模块（DOMContentLoaded后立即加载）
                const coreBusinessModules = [
                    'core/prompts.js',
                    'services/llm-service.js', 
                    'services/order-parser.js',
                    'core/order-manager.js',
                    'core/resilience-manager.js',
                    'core/performance-optimizer.js'
                ];
                
                logger.info('LazyLoader', '开始加载核心业务模块');
                const startTime = Date.now();
                
                // 并行加载所有核心业务模块
                const loadPromises = coreBusinessModules.map(src => {
                    return new Promise((resolve, reject) => {
                        const script = document.createElement('script');
                        script.src = src;
                        script.onload = resolve;
                        script.onerror = reject;
                        document.head.appendChild(script);
                    });
                });
                
                await Promise.all(loadPromises);
                
                const loadTime = Date.now() - startTime;
                logger.success('LazyLoader', '核心业务模块加载完成', { 
                    loadTime: `${loadTime}ms`,
                    modules: coreBusinessModules.length 
                });
                
                // 通知主应用核心模块已加载
                if (window.app && window.app.onCoreModulesLoaded) {
                    window.app.onCoreModulesLoaded();
                }
                
                // 设置用户交互时加载增强功能模块
                setupEnhancedModuleLoading();
                
            } catch (error) {
                logger.error('LazyLoader', '核心业务模块加载失败', error);
            }
        });
        
        // 设置增强功能模块的按需加载
        function setupEnhancedModuleLoading() {
            // 监听用户首次交互或3秒后自动加载
            const interactionEvents = ['click', 'keydown', 'touchstart'];
            let hasLoaded = false;
            
            const loadEnhancedModules = async () => {
                if (hasLoaded) return;
                hasLoaded = true;
                
                // 移除事件监听器
                interactionEvents.forEach(event => {
                    document.removeEventListener(event, loadEnhancedModules);
                });
                
                logger.info('LazyLoader', '开始按需加载增强功能模块');
                
                // 使用ModuleLoader按需加载
                try {
                    // 这些模块将在用户实际需要时才加载
                    logger.debug('LazyLoader', '增强功能模块将在需要时按需加载');
                } catch (error) {
                    logger.error('LazyLoader', '增强功能模块加载失败', error);
                }
            };
            
            // 监听用户交互
            interactionEvents.forEach(event => {
                document.addEventListener(event, loadEnhancedModules, { once: true });
            });
            
            // 3秒后自动触发（如果用户没有交互）
            setTimeout(loadEnhancedModules, 3000);
        }
        
        // 为特定功能设置按需加载触发器
        document.addEventListener('DOMContentLoaded', () => {
            // 图片上传功能触发器
            const imageTab = document.querySelector('[data-tab="image"]');
            const imageFile = document.getElementById('imageFile');
            const uploadArea = document.getElementById('uploadArea');
            
            // 图片标签页点击时预加载
            if (imageTab) {
                imageTab.addEventListener('click', async () => {
                    if (window.moduleLoader && !window.moduleLoader.isModuleLoaded('imageProcessing')) {
                        logger.info('LazyLoader', '预加载图像处理模块');
                        await window.moduleLoader.loadImageProcessing();
                    }
                });
            }
            
            // 文件选择时确保模块已加载
            if (imageFile) {
                imageFile.addEventListener('change', async () => {
                    if (window.moduleLoader && !window.moduleLoader.isModuleLoaded('imageProcessing')) {
                        logger.info('LazyLoader', '按需加载图像处理模块');
                        await window.moduleLoader.loadImageProcessing();
                    }
                });
            }
            
            // 上传区域点击时预加载
            if (uploadArea) {
                uploadArea.addEventListener('click', async () => {
                    if (window.moduleLoader && !window.moduleLoader.isModuleLoaded('imageProcessing')) {
                        logger.info('LazyLoader', '预加载图像处理模块');
                        await window.moduleLoader.loadImageProcessing();
                    }
                });
            }
            
            // 智能选择功能触发器
            const processBtn = document.getElementById('processBtn');
            if (processBtn) {
                processBtn.addEventListener('click', async () => {
                    if (window.moduleLoader && !window.moduleLoader.isModuleLoaded('smartSelection')) {
                        logger.info('LazyLoader', '按需加载智能选择模块');
                        await window.moduleLoader.loadSmartSelection();
                    }
                });
            }
            
            // 地址搜索功能触发器
            const addressInputs = document.querySelectorAll('input[data-address-search], .address-input');
            addressInputs.forEach(input => {
                input.addEventListener('focus', async () => {
                    if (window.moduleLoader && !window.moduleLoader.isModuleLoaded('addressSearch')) {
                        logger.info('LazyLoader', '按需加载地址搜索模块');
                        await window.moduleLoader.loadAddressSearch();
                    }
                }, { once: true }); // 只触发一次
            });
            
            // 手动编辑表单中的地址输入框（动态生成的）
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const addressInputs = node.querySelectorAll('input[name="pickup"], input[name="destination"]');
                            addressInputs.forEach(input => {
                                input.addEventListener('focus', async () => {
                                    if (window.moduleLoader && !window.moduleLoader.isModuleLoaded('addressSearch')) {
                                        logger.info('LazyLoader', '按需加载地址搜索模块');
                                        await window.moduleLoader.loadAddressSearch();
                                    }
                                }, { once: true });
                            });
                        }
                    });
                });
            });
            
            // 监听订单编辑表单的动态添加
            const orderEditForms = document.getElementById('orderEditForms');
            if (orderEditForms) {
                observer.observe(orderEditForms, { childList: true, subtree: true });
            }
        });
    </script>
    
    <!-- 外部API异步加载 - 使用Google推荐的最佳实践 -->
    <script>
        // Google Maps API 最佳实践加载方式
        function initGoogleMapsAPI() {
            if (window.logger) {
                logger.info('ExternalAPI', 'Google Maps API加载完成');
            }
            // 通知地址搜索服务API已就绪
            if (window.addressSearchService) {
                window.addressSearchService.onGoogleMapsAPIReady();
            }
        }
    </script>
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBsBoEOqYR2_Y3UZ5e-5k7h8MZTvZ7XbUE&libraries=places&language=zh-CN&region=MY&loading=async&callback=initGoogleMapsAPI">
    </script>
</body>
</html>