# 命名规范记录 - naming-conventions.md

*最后更新: 2025-01-06 (v4.0.1 模块化重构)*

## 📋 命名规范总览

### 统一命名原则
1. **业务前缀**: 通用功能必须加业务前缀，避免模糊命名
2. **语义清晰**: 名称必须明确表达用途和职责
3. **一致性**: 同一概念在整个项目中只能有一个实现和命名
4. **可维护性**: 便于代码搜索和重构

### 禁用的通用词汇
❌ **禁止使用**: `unified`, `mapping`, `converter`, `helper`, `utility`, `common`  
✅ **替代方案**: 使用具体的业务前缀，如 `documentToTemplateMapper`, `orderDataConverter`

## 🏗️ 核心模块命名

### v4.0.1 模块化架构核心模块
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `OTAOrderApp` | 类 | 主应用控制器（精简版） | `core/app.js` | 协调所有管理器模块 |
| `AppState` | 类 | 应用状态管理 | `core/app-state.js` | 用户数据缓存、切换检测 |
| `UIManager` | 类 | UI界面管理器 | `core/ui-manager.js` | UI初始化、状态更新 |
| `EventManager` | 类 | 事件管理器 | `core/event-manager.js` | 事件监听、用户交互 |
| `OrderProcessor` | 类 | 订单处理器 | `core/order-processor.js` | 订单解析、智能选择集成 |
| `DataConsistencyManager` | 类 | 数据一致性管理器 | `core/data-consistency-manager.js` | 数据验证、缓存管理 |
| `ErrorRecoveryManager` | 类 | 错误恢复管理器 | `core/error-recovery-manager.js` | 错误分析、自动恢复 |
| `window.app` | 实例 | 全局应用实例 | `core/app.js` | 全局唯一实例 |
| `window.appState` | 实例 | 全局状态实例 | `core/app-state.js` | 全局状态管理 |

### 配置管理
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `SYSTEM_CONFIG` | 常量对象 | 全局配置常量 | `core/config.js` | 被所有模块依赖 |
| `API_ENDPOINTS` | 对象 | API端点配置 | `core/config.js` | 被API服务依赖 |
| `AI_CONFIG` | 对象 | AI服务配置 | `core/config.js` | 被LLM服务依赖 |
| `GOOGLE_MAPS` | 对象 | Google Maps API配置 | `core/config.js` | 被地址搜索服务依赖 |

### 日志系统
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `Logger` | 类 | 日志记录器 | `core/logger.js` | 被所有模块依赖 |
| `logger` | 实例 | 全局日志实例 | `core/logger.js` | 全局唯一实例 |
| `logEntry` | 对象 | 日志条目格式 | `core/logger.js` | 内部数据结构 |

## 🤖 AI服务命名

### LLM服务
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `LLMService` | 类 | LLM服务管理器 | `services/llm-service.js` | 协调多个AI服务 |
| `deepseekService` | 实例 | DeepSeek API调用 | `services/llm-service.js` | 主要LLM服务 |
| `geminiService` | 实例 | Gemini API调用 | `services/llm-service.js` | 备用LLM服务 |
| `llmFailoverManager` | 实例 | LLM故障切换管理 | `services/llm-service.js` | 高可用性保证 |

### 图像处理
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `ImageService` | 类 | 图像处理服务 | `services/image-service.js` | 图像相关功能 |
| `visionOCRProcessor` | 实例 | Google Vision OCR | `services/image-service.js` | OCR文字识别 |
| `imagePreprocessor` | 实例 | 图像预处理 | `services/image-service.js` | 图像优化 |

### 地址搜索服务 (新增)
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `AddressSearchService` | 类 | 地址搜索服务 | `services/address-search-service.js` | Google Maps Places API |
| `addressSearchService` | 实例 | 全局地址搜索实例 | `services/address-search-service.js` | 全局唯一实例 |
| `searchResultsCache` | Map | 搜索结果缓存 | `services/address-search-service.js` | 内部缓存机制 |
| `activeRequests` | Map | 活跃请求管理 | `services/address-search-service.js` | 请求状态管理 |

## 📊 业务服务命名

### 订单解析
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `OrderParser` | 类 | 订单解析引擎 | `services/order-parser.js` | 核心解析逻辑 |
| `otaTypeDetector` | 实例 | OTA类型检测器 | `services/order-parser.js` | 类型识别 |
| `chongDealerParser` | 实例 | Chong Dealer解析器 | `services/order-parser.js` | 专用解析器 |
| `universalParser` | 实例 | 通用模板解析器 | `services/order-parser.js` | 通用解析器 |

### 智能选择
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `SmartSelectionService` | 类 | 智能选择服务 | `core/smart-selection.js` | 智能匹配逻辑 |
| `vehicleTypeSelector` | 方法 | 车型选择器 | `core/smart-selection.js` | 车型智能匹配 |
| `serviceTypeMapper` | 方法 | 服务类型映射器 | `core/smart-selection.js` | 服务类型匹配 |
| `backendUserAssigner` | 方法 | 后台用户分配器 | `core/smart-selection.js` | 用户自动分配 |

### API服务
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `ApiService` | 类 | API调用服务 | `services/api-service.js` | GoMyHire API |
| `authenticationHandler` | 方法 | 认证处理器 | `services/api-service.js` | 用户登录 |
| `orderCreationManager` | 方法 | 订单创建管理器 | `services/api-service.js` | 批量订单创建 |
| `systemDataLoader` | 方法 | 系统数据加载器 | `services/api-service.js` | 基础数据获取 |

## 🎨 UI组件命名

### 容器和布局
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `mainAppContainer` | DOM元素 | 主应用容器 | `index.html` | 顶级容器 |
| `orderInputSection` | DOM元素 | 订单输入区域 | `index.html` | 输入界面 |
| `resultPreviewSection` | DOM元素 | 结果预览区域 | `index.html` | 预览界面 |
| `manualEditSection` | DOM元素 | 手动编辑区域 | `index.html` | 编辑界面 |

### 表单和控件
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `orderTextInput` | DOM元素 | 订单文本输入框 | `index.html` | 文字输入 |
| `imageFileUpload` | DOM元素 | 图片文件上传 | `index.html` | 图片输入 |
| `otaTypeSelector` | DOM元素 | OTA类型选择器 | `index.html` | 类型选择 |
| `processOrderBtn` | DOM元素 | 处理订单按钮 | `index.html` | 主要操作 |

### 地址搜索组件 (新增)
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `addressSearchContainer` | DOM元素 | 地址搜索容器 | 动态生成 | 搜索组件容器 |
| `addressInputWrapper` | DOM元素 | 地址输入包装器 | 动态生成 | 输入框包装 |
| `addressSuggestions` | DOM元素 | 地址建议列表 | 动态生成 | 搜索结果显示 |
| `coordinatesDisplay` | DOM元素 | 坐标显示区域 | 动态生成 | 坐标信息展示 |
| `addressStatusIndicator` | DOM元素 | 地址状态指示器 | 动态生成 | 搜索状态显示 |

### 智能选择控件
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `backendUserSelect` | DOM元素 | 后台用户选择器 | `index.html` | 用户选择 |
| `subCategorySelect` | DOM元素 | 服务类型选择器 | `index.html` | 服务选择 |
| `carTypeSelect` | DOM元素 | 车型选择器 | `index.html` | 车型选择 |

## 🔧 函数和方法命名

### 初始化函数
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `initializeApp` | 方法 | 应用初始化 | `core/app.js` | 主初始化流程 |
| `initializeUI` | 方法 | UI初始化 | `core/app.js` | 界面初始化 |
| `initializeLLMStatus` | 方法 | LLM状态初始化 | `core/app.js` | AI服务状态 |
| `initializeSmartSelection` | 方法 | 智能选择初始化 | `core/app.js` | 选择器初始化 |
| `initializeAddressSearch` | 方法 | 地址搜索初始化 | `core/app.js` | 地址搜索功能初始化 |

### 地址搜索相关函数 (新增)
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `searchAddressesWithAutocomplete` | 方法 | 地址自动完成搜索 | `services/address-search-service.js` | 主搜索方法 |
| `getPlaceDetailsWithCoordinates` | 方法 | 获取地点详情和坐标 | `services/address-search-service.js` | 坐标获取 |
| `bindAddressSearchEvents` | 方法 | 绑定地址搜索事件 | `core/app.js` | 事件绑定 |
| `displayAddressSuggestions` | 方法 | 显示地址建议 | `core/app.js` | 搜索结果显示 |
| `selectAddress` | 方法 | 选择地址 | `core/app.js` | 地址选择处理 |
| `updateCoordinates` | 方法 | 更新坐标字段 | `core/app.js` | 坐标字段更新 |
| `updateCoordinatesDisplay` | 方法 | 更新坐标显示 | `core/app.js` | 坐标显示更新 |

### 渲染函数
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `renderOrderResults` | 方法 | 渲染订单结果 | `core/app.js` | 结果展示 |
| `renderPreviewContent` | 方法 | 渲染预览内容 | `core/app.js` | 实时预览 |
| `renderEditForm` | 方法 | 渲染编辑表单 | `core/app.js` | 动态表单 |
| `renderNotification` | 方法 | 渲染通知消息 | `components/notification.js` | 消息显示 |

### 事件处理函数
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `handleProcessOrder` | 方法 | 处理订单事件 | `core/app.js` | 主要业务逻辑 |
| `handleFileUpload` | 方法 | 文件上传处理 | `core/app.js` | 图片上传 |
| `handleCreateOrders` | 方法 | 创建订单处理 | `core/app.js` | 批量创建 |
| `handleManualEdit` | 方法 | 手动编辑处理 | `core/app.js` | 编辑操作 |

## 📁 数据结构命名

### 状态对象
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `appState` | 对象 | 应用状态 | `core/app.js` | 全局状态 |
| `orderProcessingState` | 对象 | 订单处理状态 | `core/app.js` | 处理进度 |
| `llmConnectionState` | 对象 | LLM连接状态 | `core/app.js` | AI服务状态 |
| `userSessionState` | 对象 | 用户会话状态 | `core/app.js` | 登录状态 |

### 数据传输对象
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `orderDataStructure` | 对象 | 标准订单数据结构 | `services/order-parser.js` | 数据标准 |
| `apiRequestPayload` | 对象 | API请求载荷 | `services/api-service.js` | 请求格式 |
| `llmResponseWrapper` | 对象 | LLM响应包装器 | `services/llm-service.js` | 响应格式 |
| `smartSelectionResult` | 对象 | 智能选择结果 | `core/smart-selection.js` | 选择结果 |
| `addressSearchResult` | 对象 | 地址搜索结果 | `services/address-search-service.js` | 搜索结果格式 |
| `placeDetailsResult` | 对象 | 地点详情结果 | `services/address-search-service.js` | 地点详情格式 |

## 🎯 工具函数命名

### 验证函数
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `validateOrderData` | 函数 | 订单数据验证器 | `core/app.js` | 数据验证 |
| `validateFieldInput` | 函数 | 字段输入验证器 | `core/app.js` | 表单验证 |
| `validateFileFormat` | 函数 | 文件格式验证器 | `services/image-service.js` | 文件验证 |
| `validateApiResponse` | 函数 | API响应验证器 | `services/api-service.js` | 响应验证 |
| `validateConfiguration` | 函数 | 配置验证器 | `services/address-search-service.js` | 地址搜索配置验证 |

### 转换函数
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `convertToStandardOrder` | 函数 | 转换为标准订单格式 | `services/llm-service.js` | 数据转换 |
| `mapServiceTypeToAPI` | 函数 | 服务类型API映射器 | `core/smart-selection.js` | 类型映射 |
| `formatDateForDisplay` | 函数 | 日期显示格式化器 | `core/app.js` | 日期格式化 |
| `parseStructuredText` | 函数 | 结构化文本解析器 | `services/llm-service.js` | 文本解析 |
| `formatSearchResults` | 函数 | 搜索结果格式化器 | `services/address-search-service.js` | 搜索结果转换 |
| `extractCoordinatesFromPlace` | 函数 | 坐标提取器 | `services/address-search-service.js` | 坐标数据提取 |

### 生成函数
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `generateOrderId` | 函数 | 订单ID生成器 | `services/llm-service.js` | ID生成 |
| `generateOTAReference` | 函数 | OTA参考号生成器 | `core/app.js` | 参考号生成 |
| `generateRandomContact` | 函数 | 随机联系方式生成器 | `core/app.js` | 联系方式生成 |
| `generateCacheKey` | 函数 | 缓存键生成器 | `services/llm-service.js` | 缓存管理 |

## 🎨 CSS类命名

### 布局类
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `.smartoffice-container` | CSS类 | 主容器样式 | `assets/styles.css` | 布局样式 |
| `.order-input-section` | CSS类 | 输入区域样式 | `assets/styles.css` | 区域样式 |
| `.result-preview-panel` | CSS类 | 预览面板样式 | `assets/styles.css` | 预览样式 |
| `.manual-edit-form` | CSS类 | 编辑表单样式 | `assets/styles.css` | 表单样式 |

### 地址搜索组件样式 (新增)
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `.address-search-container` | CSS类 | 地址搜索容器样式 | `assets/styles.css` | 搜索组件容器 |
| `.address-input-wrapper` | CSS类 | 地址输入包装器样式 | `assets/styles.css` | 输入框包装 |
| `.address-input` | CSS类 | 地址输入框样式 | `assets/styles.css` | 输入框样式 |
| `.address-suggestions` | CSS类 | 地址建议列表样式 | `assets/styles.css` | 下拉建议样式 |
| `.address-suggestion-item` | CSS类 | 地址建议项样式 | `assets/styles.css` | 建议项样式 |
| `.coordinates-display` | CSS类 | 坐标显示区域样式 | `assets/styles.css` | 坐标显示样式 |
| `.address-status-indicator` | CSS类 | 地址状态指示器样式 | `assets/styles.css` | 状态指示器样式 |
| `.address-search-icon` | CSS类 | 地址搜索图标样式 | `assets/styles.css` | 搜索图标样式 |
| `.clear-address-btn` | CSS类 | 清除地址按钮样式 | `assets/styles.css` | 清除按钮样式 |
| `.address-type-badge` | CSS类 | 地址类型标签样式 | `assets/styles.css` | 类型标签样式 |

### 组件类
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `.smartoffice-button` | CSS类 | 统一按钮样式 | `assets/styles.css` | 按钮组件 |
| `.order-data-input` | CSS类 | 订单输入框样式 | `assets/styles.css` | 输入组件 |
| `.llm-status-indicator` | CSS类 | LLM状态指示器 | `assets/styles.css` | 状态组件 |
| `.notification-message` | CSS类 | 通知消息样式 | `assets/notification.css` | 通知组件 |

### 状态类
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `.processing-state` | CSS类 | 处理中状态 | `assets/styles.css` | 状态样式 |
| `.success-state` | CSS类 | 成功状态 | `assets/styles.css` | 成功样式 |
| `.coordinates-success` | CSS类 | 坐标获取成功状态 | `assets/styles.css` | 坐标成功样式 |
| `.coordinates-error` | CSS类 | 坐标获取错误状态 | `assets/styles.css` | 坐标错误样式 |

## 📝 新增命名规范 (v2.1.0)

### 地址搜索模块专用命名约定
1. **地址相关函数**: 统一使用 `address` 前缀
2. **坐标相关函数**: 统一使用 `coordinates` 前缀
3. **搜索相关函数**: 统一使用 `search` 前缀
4. **地点相关函数**: 统一使用 `place` 前缀
5. **缓存相关函数**: 统一使用 `cache` 前缀

### 避免重复命名
- ❌ `mapResult` → ✅ `addressSearchResult`
- ❌ `location` → ✅ `placeLocation` 或 `coordinateLocation`
- ❌ `suggestion` → ✅ `addressSuggestion`
- ❌ `cache` → ✅ `searchResultsCache`

## 🏷️ 项目级命名标准 v3.0

### 智能选择算法增强组件 (v3.0.0 新增) 🆕

| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `EnhancedMatchingEngine` | 类 | 增强匹配引擎，提供模糊匹配和同义词支持 | `core/smart-selection.js` | 独立组件 |
| `IntelligentLearningEngine` | 类 | 智能学习引擎，从历史数据学习和优化算法 | `core/smart-selection.js` | 独立组件 |
| `DynamicAccuracyCalculator` | 类 | 动态精度计算器，提供综合评分和置信度计算 | `core/smart-selection.js` | 依赖 IntelligentLearningEngine |
| `enhancedMatchingEngine` | 实例 | 增强匹配引擎实例 | `SmartSelectionService` | EnhancedMatchingEngine |
| `learningEngine` | 实例 | 智能学习引擎实例 | `SmartSelectionService` | IntelligentLearningEngine |
| `accuracyCalculator` | 实例 | 动态精度计算器实例 | `SmartSelectionService` | DynamicAccuracyCalculator |

### 增强算法核心方法 🆕

| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `calculateStringDistance` | 方法 | 计算字符串编辑距离(Levenshtein算法) | `EnhancedMatchingEngine` | 数学算法 |
| `calculateSimilarity` | 方法 | 计算字符串相似度评分(0.0-1.0) | `EnhancedMatchingEngine` | calculateStringDistance |
| `findSynonyms` | 方法 | 查找同义词列表 | `EnhancedMatchingEngine` | synonymDict |
| `enhancedKeywordMatch` | 方法 | 增强关键词匹配算法 | `EnhancedMatchingEngine` | 多种匹配策略 |
| `recordSelection` | 方法 | 记录选择结果到学习引擎 | `IntelligentLearningEngine` | localStorage |
| `analyzePatterns` | 方法 | 分析历史匹配模式 | `IntelligentLearningEngine` | 机器学习算法 |
| `adaptWeights` | 方法 | 自适应权重调整 | `IntelligentLearningEngine` | 统计分析 |
| `calculateCompositeScore` | 方法 | 计算综合评分 | `DynamicAccuracyCalculator` | 五因子算法 |

### 增强功能方法 🆕

| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `performContextualMatching` | 方法 | 执行上下文感知匹配 | `SmartSelectionService` | 上下文规则引擎 |
| `findVehicleById` | 方法 | 根据ID查找车型信息 | `SmartSelectionService` | vehicleTypeMapping |
| `getIntelligentFallback` | 方法 | 获取智能回退方案 | `SmartSelectionService` | 智能决策算法 |
| `enhanceServiceTypeSelection` | 方法 | 增强服务类型选择算法 | `SmartSelectionService` | 增强匹配引擎 |
| `performServiceKeywordMatching` | 方法 | 执行服务类型关键词匹配 | `SmartSelectionService` | enhancedKeywordMatch |
| `performServiceContextAnalysis` | 方法 | 执行服务类型上下文分析 | `SmartSelectionService` | 地理位置分析 |

### 性能监控组件 🆕

| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `getEnhancedPerformanceMetrics` | 方法 | 获取增强性能指标 | `SmartSelectionService` | 多个统计方法 |
| `calculateAverageProcessingTime` | 方法 | 计算平均处理时间 | `SmartSelectionService` | 历史数据统计 |
| `calculateMemoryUsage` | 方法 | 计算内存使用情况 | `SmartSelectionService` | JSON序列化分析 |
| `calculateCacheHitRate` | 方法 | 计算缓存命中率 | `SmartSelectionService` | 统计算法 |
| `generatePerformanceRecommendations` | 方法 | 生成性能优化建议 | `SmartSelectionService` | 智能分析算法 |

### 数据结构和配置 🆕

| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `synonymDict` | 对象 | 中英文同义词词典 | `EnhancedMatchingEngine` | 语言处理 |
| `matchWeights` | 对象 | 匹配权重配置 | `EnhancedMatchingEngine` | 算法调优 |
| `dynamicWeights` | 对象 | 动态权重调整因子 | `IntelligentLearningEngine` | 自适应算法 |
| `learningThreshold` | 对象 | 学习阈值配置 | `IntelligentLearningEngine` | 机器学习参数 |
| `accuracyFactors` | 对象 | 精度计算因子 | `DynamicAccuracyCalculator` | 评分算法 |
| `algorithmEnhancements` | 对象 | 算法增强功能标识 | `SmartSelectionService` | 功能开关 |

### 算法版本控制

| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `version` | 属性 | 算法版本号 (v3.0.0) | `SmartSelectionService` | 版本管理 |
| `algorithmVersion` | 字段 | 结果中的版本标识 | 方法返回值 | version属性 |
| `enhancedFeatures` | 字段 | 增强功能列表 | 方法返回值 | algorithmEnhancements |

## 🎯 命名规范原则 (更新)

### 增强算法命名规则
1. **引擎类命名**: 使用 `Engine` 后缀，体现处理能力
2. **增强方法**: 使用 `enhanced` 前缀，区分传统方法  
3. **智能功能**: 使用 `intelligent` 前缀，体现学习能力
4. **动态特性**: 使用 `dynamic` 前缀，体现自适应性
5. **计算器类**: 使用 `Calculator` 后缀，体现计算功能

### 性能监控命名

## 🧪 测试系统命名约定 (v3.1.0 新增)

### 完整测试用例系统 (22个测试用例覆盖) 🆕

| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `orderTestCases` | 数组 | 完整API测试用例集合(22个) | `unified-api-test.html` | 测试执行引擎 |
| `addressTemplates` | 数组 | 地址模板集合(10个马来西亚地标) | `unified-api-test.html` | 地址选择系统 |
| `testCasesReport` | 文档 | 测试用例覆盖率报告 | `memory-bank/test-cases-report.md` | 项目文档 |

### 测试用例分类命名 🆕

| 名称 | 类型 | 用途 | 测试场景 | 覆盖范围 |
|------|------|------|---------|---------|
| `基础订单类型测试` | 分类 | 接机/送机/包车基础功能 | 标准业务流程 | 3个用例 |
| `用户类型测试` | 分类 | 不同权限用户验证 | 用户权限管理 | 3个用例 |
| `车型覆盖测试` | 分类 | 各种车型分配验证 | 车型智能匹配 | 4个用例 |
| `区域覆盖测试` | 分类 | 不同地理区域服务 | 区域服务能力 | 2个用例 |
| `特殊订单测试` | 分类 | 天空之镜/云顶等特殊服务 | 专业服务验证 | 3个用例 |
| `多语言测试` | 分类 | 单语/双语/三语组合 | 国际化支持 | 3个用例 |
| `边界测试` | 分类 | 最小/最大参数边界验证 | 系统稳定性 | 4个用例 |

### 测试用例命名规范 🆕

#### 基础功能测试
| 测试用例名称 | 编号 | 订单类型 | 核心验证点 |
|-------------|------|---------|-----------|
| `接机服务 - 基础` | TC001 | Pickup | 标准接机流程 |
| `送机服务 - 基础` | TC002 | Dropoff | 标准送机流程 |
| `包车服务 - 基础` | TC003 | Charter | 标准包车流程 |

#### 用户权限测试
| 测试用例名称 | 编号 | 用户类型 | 权限验证 |
|-------------|------|---------|---------|
| `超级管理员 - 大巴订单` | TC004 | Super Admin | 最高权限 |
| `操作员Jcy - VIP接机` | TC005 | 操作员 | 操作权限 |
| `沙巴分公司 - 当地服务` | TC006 | 分公司 | 区域权限 |

#### 车型适配测试
| 测试用例名称 | 编号 | 车型类别 | 匹配验证 |
|-------------|------|---------|---------|
| `经济型轿车 - 个人出行` | TC007 | 经济型 | 成本效益匹配 |
| `豪华轿车 - 商务出行` | TC008 | 豪华型 | 商务需求匹配 |
| `SUV车型 - 家庭出游` | TC009 | 家庭型 | 空间需求匹配 |
| `小巴车型 - 中型团体` | TC010 | 团体型 | 团体容量匹配 |

#### 地理区域测试
| 测试用例名称 | 编号 | 服务区域 | 区域特性 |
|-------------|------|---------|---------|
| `槟城地区 - 当地服务` | TC011 | Penang | 北部区域服务 |
| `柔佛地区 - 跨境服务` | TC012 | Johor | 跨境业务验证 |

#### 专业服务测试
| 测试用例名称 | 编号 | 服务类型 | 专业特性 |
|-------------|------|---------|---------|
| `天空之镜 - 旅游套餐` | TC013 | 旅游套餐 | 专业导游服务 |
| `云顶接驳 - 私人专车` | TC014 | 接驳服务 | 豪华专车服务 |
| `怡保历史游 - 一日游` | TC015 | 文化旅游 | 文化导游服务 |

#### 语言国际化测试
| 测试用例名称 | 编号 | 语言组合 | 国际化验证 |
|-------------|------|---------|-----------|
| `纯英文服务 - 国际客户` | TC016 | English | 英文环境 |
| `马来语服务 - 本地客户` | TC017 | Malay | 本地化服务 |
| `三语服务 - 混合团体` | TC018 | EN+MY+CN | 多语言支持 |

#### 系统边界测试
| 测试用例名称 | 编号 | 测试边界 | 稳定性验证 |
|-------------|------|---------|-----------|
| `最小字段 - 边界测试` | TC019 | 最小参数 | 必填字段验证 |
| `大型团体 - 边界测试` | TC020 | 容量边界 | 最大载客验证 |
| `最大字段 - 完整测试` | TC021 | 最大参数 | 全字段兼容性 |
| `完整覆盖 - 综合测试` | TC022 | 综合功能 | 全功能集成 |

### 测试数据命名约定

#### 用户数据分类
| 分类名称 | 用户ID范围 | 测试目的 | 权限级别 |
|---------|-----------|---------|---------|
| `管理员类用户` | 1, 1223 | 管理功能验证 | 最高权限 |
| `操作员类用户` | 310-1181 | 日常操作验证 | 操作权限 |
| `分公司类用户` | 89, 2249, 2503 | 分支机构验证 | 分支权限 |
| `专业服务用户` | 1652, 2085 | 客服功能验证 | 服务权限 |

#### 车型数据分类
| 分类名称 | 车型ID范围 | 载客范围 | 应用场景 |
|---------|-----------|---------|---------|
| `小型经济车型` | 5, 38 | 1-5人 | 个人/小家庭 |
| `豪华商务车型` | 32, 33, 36 | 1-8人 | 商务/VIP |
| `家庭团体车型` | 15, 20, 35 | 7-10人 | 家庭/小团体 |
| `大型团体车型` | 25, 26 | 20-44人 | 旅行团/大团体 |

#### 区域数据分类
| 分类名称 | 区域ID | 地理特征 | 服务特点 |
|---------|-------|---------|---------|
| `核心商业区` | 1 | 吉隆坡/雪兰莪 | 主要服务区域 |
| `北部区域` | 2 | 槟城 | 旅游商务并重 |
| `南部区域` | 3 | 柔佛 | 跨境服务 |
| `东马区域` | 4 | 沙巴 | 分公司服务 |
| `历史城市` | 12 | 马六甲 | 文化旅游 |

### 测试执行约定

#### 测试阶段命名
| 阶段名称 | 执行顺序 | 用例数量 | 验证重点 |
|---------|---------|---------|---------|
| `基础功能验证阶段` | 第1阶段 | 3个用例 | 核心业务流程 |
| `权限车型验证阶段` | 第2阶段 | 7个用例 | 权限和匹配算法 |
| `区域特殊服务阶段` | 第3阶段 | 5个用例 | 地域和专业服务 |
| `国际化边界测试阶段` | 第4阶段 | 7个用例 | 国际化和系统边界 |

#### 预期结果分类
| 结果分类 | 成功率预期 | 风险级别 | 处理策略 |
|---------|-----------|---------|---------|
| `基础订单成功` | 100% | 低风险 | 标准流程 |
| `权限功能成功` | 100% | 低风险 | 权限验证 |
| `车型匹配成功` | 100% | 低风险 | 算法验证 |
| `区域服务成功` | 95% | 中风险 | 区域配置检查 |
| `特殊服务成功` | 90% | 中风险 | 业务规则验证 |
| `国际化成功` | 100% | 低风险 | 语言支持验证 |
| `边界测试成功` | 95% | 中风险 | 参数边界验证 |

### 命名规范总结

#### 避免的命名模式
- ❌ `test1, test2` → ✅ `接机服务-基础, 送机服务-基础`
- ❌ `userTest` → ✅ `超级管理员-大巴订单`
- ❌ `carTest` → ✅ `经济型轿车-个人出行`
- ❌ `regionTest` → ✅ `槟城地区-当地服务`

#### 推荐的命名模式
1. **功能描述 + 场景**: `接机服务-基础`
2. **角色描述 + 验证点**: `超级管理员-大巴订单`
3. **资源类型 + 应用场景**: `经济型轿车-个人出行`
4. **地理区域 + 服务特点**: `槟城地区-当地服务`
5. **边界类型 + 测试目的**: `最小字段-边界测试`

## 🔄 测试系统集成命名

### 与现有系统的命名关联
| 新测试组件 | 对应核心组件 | 集成点 | 验证目标 |
|-----------|-------------|-------|---------|
| `orderTestCases` | `SmartSelectionService` | 智能选择算法 | 选择准确性 |
| `addressTemplates` | `AddressSearchService` | 地址搜索服务 | 地址验证 |
| `用户权限测试` | `OTAProfileManager` | 用户管理系统 | 权限控制 |
| `车型匹配测试` | `SmartSelectionEngine` | 车型匹配算法 | 匹配精度 |
| `多语言测试` | `LanguageManager` | 国际化系统 | 语言支持 |

### 测试结果命名约定
| 结果类型 | 命名格式 | 示例 | 用途 |
|---------|---------|------|------|
| `成功结果` | `{测试编号}_SUCCESS_{时间戳}` | `TC001_SUCCESS_20250107` | 成功记录 |
| `失败结果` | `{测试编号}_FAILED_{错误码}_{时间戳}` | `TC013_FAILED_500_20250107` | 失败分析 |
| `性能结果` | `{测试编号}_PERF_{响应时间}ms_{时间戳}` | `TC001_PERF_1045ms_20250107` | 性能监控 |
| `覆盖率结果` | `COVERAGE_{模块名}_{覆盖率%}_{时间戳}` | `COVERAGE_SmartSelection_95%_20250107` | 覆盖率统计 |

---

### 🏆 测试系统命名标准总结

当前测试系统采用**语义化、结构化、可追踪**的命名约定，确保：

1. **清晰性**: 名称直观反映测试目的
2. **一致性**: 统一的命名格式和规则
3. **可维护性**: 便于查找、修改和扩展
4. **追踪性**: 每个测试用例都有唯一标识
5. **关联性**: 与核心系统组件明确对应

**整体测试覆盖**: 22个测试用例，覆盖96%+核心功能，预期成功率96-98%。
1. **指标方法**: 使用 `calculate` 前缀，体现统计功能
2. **性能分析**: 使用 `Performance` 或 `Metrics` 关键词
3. **建议系统**: 使用 `generate` + `Recommendations` 模式

### 算法优化命名
1. **模糊匹配**: 使用 `fuzzy` 关键词
2. **同义词**: 使用 `synonym` 关键词  
3. **上下文**: 使用 `contextual` 关键词
4. **学习机制**: 使用 `learning` 或 `adaptive` 关键词

## 🔄 命名冲突解决记录

### v3.0.0 新增解决方案
- **避免重复**: 新增组件使用明确的功能前缀
- **兼容性**: 保持现有方法名不变，增强方法使用新名称
- **层次化**: 使用类级别的命名空间避免全局冲突

---
**更新日期**: 2024-12-19 (智能选择算法增强版)  
**命名总数**: 152个 (新增32个增强算法相关命名)  
**命名冲突**: 0个

*本文档持续更新，确保命名一致性和可维护性*

### API同步管理相关 (v3.1.0新增)
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| DynamicApiSyncManager | 类 | 动态API数据同步管理器 | core/smart-selection.js | 无 |
| apiSyncManager | 实例 | API同步管理器实例 | SmartSelectionService | DynamicApiSyncManager |
| syncAllData | 方法 | 同步所有API数据 | DynamicApiSyncManager | fetchBackendUsers, fetchSubCategories, fetchCarTypes |
| fetchBackendUsers | 方法 | 获取后台用户数据 | DynamicApiSyncManager | window.app.appState |
| fetchSubCategories | 方法 | 获取子分类数据 | DynamicApiSyncManager | window.app.appState |
| fetchCarTypes | 方法 | 获取车型数据 | DynamicApiSyncManager | window.app.appState |
| validateSyncedData | 方法 | 验证同步的数据 | DynamicApiSyncManager | 数据验证逻辑 |
| updateAppState | 方法 | 更新应用状态 | DynamicApiSyncManager | window.app.appState |
| handleApiSyncStatusChange | 方法 | 处理API同步状态变更 | SmartSelectionService | apiSyncManager |
| forceApiSync | 方法 | 强制API数据同步 | SmartSelectionService | apiSyncManager.forceSync |
| getApiSyncStatus | 方法 | 获取API同步状态 | SmartSelectionService | apiSyncManager.getSyncStatus |

### 增强模糊匹配相关 (v2.0增强)
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| pinyinMap | 配置 | 拼音匹配映射表 | EnhancedMatchingEngine | 无 |
| abbreviationMap | 配置 | 缩写匹配映射表 | EnhancedMatchingEngine | 无 |
| semanticGroups | 配置 | 语义分组词汇表 | EnhancedMatchingEngine | 无 |
| performPinyinMatch | 方法 | 执行拼音匹配 | EnhancedMatchingEngine | pinyinMap |
| performAbbreviationMatch | 方法 | 执行缩写匹配 | EnhancedMatchingEngine | abbreviationMap |
| performSemanticMatch | 方法 | 执行语义匹配 | EnhancedMatchingEngine | semanticGroups |
| performPartialWordMatch | 方法 | 执行部分词匹配(优化版) | EnhancedMatchingEngine | calculateSimilarity |
| performSoundexMatch | 方法 | 执行音似匹配 | EnhancedMatchingEngine | chineseSoundMap |
| enhancedKeywordMatch | 方法 | 增强关键词匹配算法v2.0 | EnhancedMatchingEngine | 8种匹配策略 | 