/**
 * @file image-service.js - 图像处理服务
 * @description 处理图片上传、OCR识别和图像分析功能
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

/**
 * @class ImageService - 图像处理服务类
 * @description 提供图片上传、OCR识别、图像分析等功能
 */
class ImageService {
    /**
     * @function constructor - 构造函数
     */
    constructor() {
        this.maxFileSize = SYSTEM_CONFIG.UPLOAD.MAX_FILE_SIZE;
        this.allowedTypes = SYSTEM_CONFIG.UPLOAD.ALLOWED_TYPES;
        this.maxFiles = SYSTEM_CONFIG.UPLOAD.MAX_FILES;
        this.visionApiKey = SYSTEM_CONFIG.API.GOOGLE_VISION.API_KEY;
        this.visionApiUrl = SYSTEM_CONFIG.API.GOOGLE_VISION.API_URL;
    }

    /**
     * @function validateFile - 验证上传文件
     * @param {File} file - 文件对象
     * @returns {object} 验证结果
     */
    validateFile(file) {
        const errors = [];

        // 检查文件大小
        if (file.size > this.maxFileSize) {
            errors.push(`文件大小超过限制 (${(this.maxFileSize / 1024 / 1024).toFixed(1)}MB)`);
        }

        // 检查文件类型
        if (!this.allowedTypes.includes(file.type)) {
            errors.push(`不支持的文件类型: ${file.type}`);
        }

        // 检查文件名
        if (!file.name || file.name.length > 255) {
            errors.push('文件名无效或过长');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * @function processImageFiles - 处理多个图片文件
     * @param {FileList} files - 文件列表
     * @returns {Promise<object>} 处理结果
     */
    async processImageFiles(files) {
        logger.info('图像处理', '开始处理图片文件', {
            fileCount: files.length
        });

        if (files.length > this.maxFiles) {
            throw new Error(`最多只能上传 ${this.maxFiles} 个文件`);
        }

        const results = [];
        const errors = [];

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            try {
                logger.info('图像处理', `处理第 ${i + 1} 个文件`, {
                    fileName: file.name,
                    fileSize: file.size,
                    fileType: file.type
                });

                // 验证文件
                const validation = this.validateFile(file);
                if (!validation.isValid) {
                    errors.push({
                        fileName: file.name,
                        errors: validation.errors
                    });
                    continue;
                }

                // 处理单个文件
                const result = await this.processSingleImage(file);
                results.push({
                    fileName: file.name,
                    ...result
                });

            } catch (error) {
                logger.error('图像处理', `文件处理失败: ${file.name}`, {
                    error: error.message
                });
                
                errors.push({
                    fileName: file.name,
                    errors: [error.message]
                });
            }
        }

        logger.success('图像处理', '图片文件处理完成', {
            successCount: results.length,
            errorCount: errors.length
        });

        return {
            success: results.length > 0,
            results: results,
            errors: errors,
            totalFiles: files.length,
            processedFiles: results.length
        };
    }

    /**
     * @function processSingleImage - 处理单个图片
     * @param {File} file - 图片文件
     * @returns {Promise<object>} 处理结果
     */
    async processSingleImage(file) {
        const startTime = Date.now();

        try {
            // 1. 转换为Base64
            const base64Data = await this.fileToBase64(file);
            
            // 2. 图片质量评估
            const qualityAssessment = await this.assessImageQuality(base64Data);
            
            // 3. OCR文字识别
            const ocrResult = await this.performOCR(base64Data);
            
            // 4. 图像分析
            const analysisResult = await this.analyzeImage(base64Data);

            const processingTime = Date.now() - startTime;

            return {
                success: true,
                base64Data: base64Data,
                qualityAssessment: qualityAssessment,
                ocrResult: ocrResult,
                analysisResult: analysisResult,
                processingTime: processingTime,
                fileInfo: {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    lastModified: file.lastModified
                }
            };

        } catch (error) {
            const processingTime = Date.now() - startTime;
            
            logger.error('图像处理', '单个图片处理失败', {
                fileName: file.name,
                error: error.message,
                processingTime: processingTime
            });

            return {
                success: false,
                error: error.message,
                processingTime: processingTime
            };
        }
    }

    /**
     * @function fileToBase64 - 将文件转换为Base64
     * @param {File} file - 文件对象
     * @returns {Promise<string>} Base64字符串
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = () => {
                // 移除data:image/...;base64,前缀
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            
            reader.onerror = () => {
                reject(new Error('文件读取失败'));
            };
            
            reader.readAsDataURL(file);
        });
    }

    /**
     * @function assessImageQuality - 评估图片质量
     * @param {string} base64Data - Base64图片数据
     * @returns {Promise<object>} 质量评估结果
     */
    async assessImageQuality(base64Data) {
        // 简单的质量评估逻辑
        // 在实际项目中可以使用更复杂的图像分析算法
        
        try {
            // 创建临时图片元素来获取尺寸信息
            const img = new Image();
            const imageInfo = await new Promise((resolve, reject) => {
                img.onload = () => {
                    resolve({
                        width: img.width,
                        height: img.height,
                        aspectRatio: img.width / img.height
                    });
                };
                img.onerror = () => reject(new Error('图片加载失败'));
                img.src = `data:image/jpeg;base64,${base64Data}`;
            });

            // 基于尺寸的质量评估
            let qualityScore = 0;
            const recommendations = [];

            // 分辨率检查
            const totalPixels = imageInfo.width * imageInfo.height;
            if (totalPixels >= 1920 * 1080) {
                qualityScore += 30;
            } else if (totalPixels >= 1280 * 720) {
                qualityScore += 20;
            } else if (totalPixels >= 640 * 480) {
                qualityScore += 10;
            } else {
                recommendations.push('图片分辨率较低，建议使用更高分辨率的图片');
            }

            // 宽高比检查
            if (imageInfo.aspectRatio >= 0.5 && imageInfo.aspectRatio <= 2.0) {
                qualityScore += 20;
            } else {
                recommendations.push('图片宽高比异常，可能影响识别效果');
            }

            // 文件大小评估（基于Base64长度）
            const estimatedSize = (base64Data.length * 3) / 4; // 估算原始大小
            if (estimatedSize > 100 * 1024 && estimatedSize < 5 * 1024 * 1024) {
                qualityScore += 20;
            } else if (estimatedSize <= 100 * 1024) {
                recommendations.push('图片文件较小，可能影响文字识别精度');
            }

            // 基础质量分
            qualityScore += 30;

            let qualityLevel = 'poor';
            if (qualityScore >= 80) qualityLevel = 'excellent';
            else if (qualityScore >= 60) qualityLevel = 'good';
            else if (qualityScore >= 40) qualityLevel = 'fair';

            return {
                score: qualityScore,
                level: qualityLevel,
                imageInfo: imageInfo,
                estimatedSize: estimatedSize,
                recommendations: recommendations,
                ocrSuitability: qualityScore >= 40
            };

        } catch (error) {
            logger.error('图像处理', '图片质量评估失败', { error: error.message });
            return {
                score: 0,
                level: 'unknown',
                error: error.message,
                ocrSuitability: false
            };
        }
    }

    /**
     * @function performOCR - 执行OCR文字识别
     * @param {string} base64Data - Base64图片数据
     * @returns {Promise<object>} OCR识别结果
     */
    async performOCR(base64Data) {
        if (!this.visionApiKey || this.visionApiKey === 'your-api-key-here') {
            logger.warn('图像处理', 'Google Vision API Key未配置，跳过OCR识别');
            return {
                success: false,
                error: 'Google Vision API Key未配置',
                text: '',
                confidence: 0
            };
        }

        try {
            const requestBody = {
                requests: [{
                    image: {
                        content: base64Data
                    },
                    features: [{
                        type: SYSTEM_CONFIG.API.GOOGLE_VISION.FEATURES.DOCUMENT_TEXT_DETECTION,
                        maxResults: SYSTEM_CONFIG.API.GOOGLE_VISION.MAX_RESULTS
                    }]
                }]
            };

            const response = await fetch(
                `${this.visionApiUrl}?key=${this.visionApiKey}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody),
                    signal: AbortSignal.timeout(SYSTEM_CONFIG.API.GOOGLE_VISION.TIMEOUT)
                }
            );

            if (!response.ok) {
                throw new Error(`Google Vision API请求失败: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.responses && data.responses[0]) {
                const result = data.responses[0];
                
                if (result.error) {
                    throw new Error(`Vision API错误: ${result.error.message}`);
                }

                const textAnnotations = result.textAnnotations || [];
                const fullText = textAnnotations.length > 0 ? textAnnotations[0].description : '';
                
                // 计算平均置信度
                let totalConfidence = 0;
                let confidenceCount = 0;
                
                if (result.fullTextAnnotation && result.fullTextAnnotation.pages) {
                    for (const page of result.fullTextAnnotation.pages) {
                        for (const block of page.blocks || []) {
                            if (block.confidence !== undefined) {
                                totalConfidence += block.confidence;
                                confidenceCount++;
                            }
                        }
                    }
                }

                const averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : 0;

                logger.success('图像处理', 'OCR识别完成', {
                    textLength: fullText.length,
                    confidence: averageConfidence,
                    annotationCount: textAnnotations.length
                });

                return {
                    success: true,
                    text: fullText,
                    confidence: averageConfidence,
                    textAnnotations: textAnnotations,
                    fullTextAnnotation: result.fullTextAnnotation
                };
            } else {
                return {
                    success: false,
                    error: '未找到文字内容',
                    text: '',
                    confidence: 0
                };
            }

        } catch (error) {
            logger.error('图像处理', 'OCR识别失败', { error: error.message });
            return {
                success: false,
                error: error.message,
                text: '',
                confidence: 0
            };
        }
    }

    /**
     * @function analyzeImage - 分析图像内容
     * @param {string} base64Data - Base64图片数据
     * @returns {Promise<object>} 分析结果
     */
    async analyzeImage(base64Data) {
        // 简单的图像分析逻辑
        // 在实际项目中可以集成更多的图像分析功能
        
        try {
            const analysis = {
                hasText: false,
                isDocument: false,
                isScreenshot: false,
                suggestedProcessing: []
            };

            // 基于OCR结果的简单分析
            // 这里可以添加更复杂的图像分析逻辑

            analysis.suggestedProcessing.push('OCR文字识别');
            
            return {
                success: true,
                analysis: analysis
            };

        } catch (error) {
            logger.error('图像处理', '图像分析失败', { error: error.message });
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * @function extractOrdersFromImages - 从图片中提取订单信息
     * @param {array} imageResults - 图片处理结果数组
     * @returns {Promise<object>} 订单提取结果
     */
    async extractOrdersFromImages(imageResults) {
        logger.info('图像处理', '开始从图片中提取订单信息', {
            imageCount: imageResults.length
        });

        const allText = [];
        const extractedOrders = [];

        // 收集所有OCR文本
        for (const result of imageResults) {
            if (result.success && result.ocrResult && result.ocrResult.success) {
                allText.push(result.ocrResult.text);
            }
        }

        if (allText.length === 0) {
            return {
                success: false,
                error: '未能从图片中识别到文字内容',
                orders: []
            };
        }

        // 合并所有文本
        const combinedText = allText.join('\n\n--- 图片分隔 ---\n\n');

        // 使用订单解析服务处理文本
        const orderParser = new OrderParser();
        const parseResult = await orderParser.parseOrders(combinedText);

        logger.success('图像处理', '图片订单提取完成', {
            textLength: combinedText.length,
            orderCount: parseResult.orders?.length || 0,
            success: parseResult.success
        });

        return {
            success: parseResult.success,
            orders: parseResult.orders || [],
            combinedText: combinedText,
            imageCount: imageResults.length,
            ocrResults: imageResults.map(r => r.ocrResult),
            metadata: parseResult.metadata
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageService;
} else if (typeof window !== 'undefined') {
    window.ImageService = ImageService;
}
