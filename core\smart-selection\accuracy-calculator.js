/**
 * @file accuracy-calculator.js - 动态精度计算器
 * @description 提供统一的精度计算和置信度评估功能
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 */

/**
 * @class DynamicAccuracyCalculator - 动态精度计算器
 * @description 提供统一的精度计算和置信度评估功能
 */
class DynamicAccuracyCalculator {
    constructor(learningEngine) {
        this.learningEngine = learningEngine;
        
        // 精度计算因子
        this.accuracyFactors = {
            matchPrecision: 0.3,     // 匹配精确度
            methodReliability: 0.25,  // 方法可靠性
            historicalSuccess: 0.2,   // 历史成功率
            contextRelevance: 0.15,   // 上下文相关性
            dataQuality: 0.1         // 数据质量
        };
    }
    
    /**
     * @function calculateCompositeScore - 计算综合评分
     * @param {object} matchResult - 匹配结果
     * @param {object} context - 上下文信息
     * @returns {object} 综合评分结果
     */
    calculateCompositeScore(matchResult, context = {}) {
        const scores = {
            matchPrecision: this.calculateMatchPrecision(matchResult),
            methodReliability: this.calculateMethodReliability(matchResult.method),
            historicalSuccess: this.calculateHistoricalSuccess(matchResult.method, context),
            contextRelevance: this.calculateContextRelevance(matchResult, context),
            dataQuality: this.calculateDataQuality(context.orderData)
        };
        
        // 计算加权总分
        let compositeScore = 0;
        for (const [factor, weight] of Object.entries(this.accuracyFactors)) {
            compositeScore += (scores[factor] || 0) * weight;
        }
        
        // 应用学习引擎的动态权重调整
        const learningBoost = this.learningEngine.getOptimalWeight(matchResult.method);
        compositeScore = compositeScore * (0.7 + learningBoost * 0.3);
        
        return {
            compositeScore: Math.min(1.0, Math.max(0.0, compositeScore)),
            detailedScores: scores,
            confidence: this.calculateConfidence(compositeScore, scores),
            reliability: this.calculateReliability(matchResult, compositeScore)
        };
    }
    
    /**
     * @function calculateMatchPrecision - 计算匹配精确度
     * @param {object} matchResult - 匹配结果
     * @returns {number} 精确度评分
     */
    calculateMatchPrecision(matchResult) {
        const baseScore = matchResult.confidence || 0.5;
        
        // 根据匹配方法调整精确度
        const methodBonus = {
            'exact_match': 0.2,
            'passenger_count_exact': 0.15,
            'enhanced_keyword_match': 0.1,
            'synonym_match': 0.08,
            'fuzzy_match': 0.05,
            'default_fallback': -0.1
        };
        
        const bonus = methodBonus[matchResult.method] || 0;
        return Math.min(1.0, baseScore + bonus);
    }
    
    /**
     * @function calculateMethodReliability - 计算方法可靠性
     * @param {string} method - 匹配方法
     * @returns {number} 可靠性评分
     */
    calculateMethodReliability(method) {
        // 基于历史数据的方法可靠性评分
        const reliabilityMap = {
            'passenger_count_exact': 0.9,
            'enhanced_keyword_match': 0.85,
            'service_mapping': 0.8,
            'ota_mapping': 0.85,
            'fuzzy_match': 0.7,
            'contextual_analysis': 0.75,
            'default_fallback': 0.4
        };
        
        const baseReliability = reliabilityMap[method] || 0.6;
        const learningAdjustment = this.learningEngine.calculateMethodAccuracy(method);
        
        return (baseReliability + learningAdjustment) / 2;
    }
    
    /**
     * @function calculateHistoricalSuccess - 计算历史成功率
     * @param {string} method - 匹配方法
     * @param {object} context - 上下文信息
     * @returns {number} 历史成功率
     */
    calculateHistoricalSuccess(method, context) {
        const patterns = this.learningEngine.analyzePatterns(context.orderData || {});
        
        if (patterns.suggestedMethod === method) {
            return patterns.historicalAccuracy;
        }
        
        return this.learningEngine.calculateMethodAccuracy(method);
    }
    
    /**
     * @function calculateContextRelevance - 计算上下文相关性
     * @param {object} matchResult - 匹配结果
     * @param {object} context - 上下文信息
     * @returns {number} 相关性评分
     */
    calculateContextRelevance(matchResult, context) {
        let relevanceScore = 0.5; // 基础分
        
        const orderData = context.orderData || {};
        
        // 乘客数量与车型的匹配度
        if (matchResult.seats && orderData.passenger_count) {
            const passengers = parseInt(orderData.passenger_count) || 1;
            if (matchResult.seats >= passengers && matchResult.seats <= passengers + 2) {
                relevanceScore += 0.2; // 座位数刚好合适
            } else if (matchResult.seats >= passengers) {
                relevanceScore += 0.1; // 座位数足够
            }
        }
        
        // OTA平台相关性
        if (matchResult.method === 'ota_mapping' && orderData.ota) {
            relevanceScore += 0.2;
        }
        
        // 服务类型相关性
        if (matchResult.method === 'service_mapping' && orderData.service_type) {
            relevanceScore += 0.15;
        }
        
        // 时间敏感性
        if (orderData.flight_number && matchResult.method === 'flight_service') {
            relevanceScore += 0.15;
        }
        
        return Math.min(1.0, relevanceScore);
    }
    
    /**
     * @function calculateDataQuality - 计算数据质量
     * @param {object} orderData - 订单数据
     * @returns {number} 数据质量评分
     */
    calculateDataQuality(orderData = {}) {
        let qualityScore = 0;
        const maxScore = 1.0;
        
        // 检查关键字段的完整性
        const keyFields = ['customer_name', 'service_type', 'passenger_count'];
        const optionalFields = ['flight_number', 'pickup_location', 'destination', 'remarks'];
        
        keyFields.forEach(field => {
            if (orderData[field] && orderData[field].toString().trim()) {
                qualityScore += 0.25; // 每个关键字段25%
            }
        });
        
        optionalFields.forEach(field => {
            if (orderData[field] && orderData[field].toString().trim()) {
                qualityScore += 0.0625; // 每个可选字段6.25%
            }
        });
        
        return Math.min(maxScore, qualityScore);
    }
    
    /**
     * @function calculateConfidence - 计算置信度
     * @param {number} compositeScore - 综合评分
     * @param {object} detailedScores - 详细评分
     * @returns {number} 置信度
     */
    calculateConfidence(compositeScore, detailedScores) {
        // 基于综合评分的置信度
        let confidence = compositeScore;
        
        // 如果各项评分比较均衡，提升置信度
        const scores = Object.values(detailedScores);
        const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - avgScore, 2), 0) / scores.length;
        
        if (variance < 0.1) { // 评分比较均衡
            confidence *= 1.1;
        }
        
        return Math.min(1.0, confidence);
    }
    
    /**
     * @function calculateReliability - 计算可靠性等级
     * @param {object} matchResult - 匹配结果
     * @param {number} compositeScore - 综合评分
     * @returns {string} 可靠性等级
     */
    calculateReliability(matchResult, compositeScore) {
        if (compositeScore >= 0.9) return 'excellent';
        if (compositeScore >= 0.8) return 'good';
        if (compositeScore >= 0.7) return 'fair';
        if (compositeScore >= 0.6) return 'poor';
        return 'unreliable';
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicAccuracyCalculator;
} else if (typeof window !== 'undefined') {
    window.DynamicAccuracyCalculator = DynamicAccuracyCalculator;
}
