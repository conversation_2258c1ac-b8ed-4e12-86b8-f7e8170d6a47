/**
 * @file api-service.js - API服务管理类
 * @description 负责所有GoMyHire API调用，包括登录、获取数据、创建订单等
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2025-01-08
 * @version v4.2.0
 */

/**
 * @class ApiService - API服务类
 * @description 管理所有与GoMyHire API的交互
 */
class ApiService {
    /**
     * @function constructor - 构造函数
     * @param {object} appState - 应用状态管理器
     */
    constructor(appState) {
        this.appState = appState;
        this.baseUrl = SYSTEM_CONFIG.API.BASE_URL;
        this.endpoints = SYSTEM_CONFIG.API.ENDPOINTS;
    }

    /**
     * @function buildUrl - 构建API URL
     * @param {string} endpoint - 端点名称
     * @returns {string} 完整的API URL
     */
    buildUrl(endpoint) {
        return `${this.baseUrl}${this.endpoints[endpoint]}`;
    }

    /**
     * @function validateToken - 验证token有效性
     * @returns {Promise<boolean>} token是否有效
     */
    async validateToken() {
        // 本地模式下直接返回true
        if (window.localDataProvider && window.localDataProvider.isLocalModeEnabled()) {
            logger.info('API', '本地模式：跳过token验证');
            return true;
        }

        try {
            const token = this.appState.token;
            if (!token) return false;
            
            // 尝试调用一个需要认证的API来验证token
            await this.getBackendUsers();
            return true;
        } catch (error) {
            logger.warn('API', 'Token验证失败', error.message);
            return false;
        }
    }

    /**
     * @function login - 用户登录
     * @param {string} email - 邮箱
     * @param {string} password - 密码
     * @returns {Promise<object>} 登录结果
     */
    async login(email, password) {
        const url = this.buildUrl('login');
        logger.info('API', '发起登录请求', { email, url });
        
        try {
            const requestData = { email, password };
            logger.debug('API', '登录请求数据', requestData);
            
            const response = await axios.post(url, requestData);
            
            logger.debug('API', '登录响应数据', {
                status: response.status,
                data: response.data
            });
            
            if (response.data.status && response.data.token) {
                // 提取实际token（去掉数字|前缀）
                const actualToken = response.data.token.split('|')[1] || response.data.token;
                
                logger.debug('API', 'Token处理完成', {
                    originalToken: response.data.token.substring(0, 20) + '...',
                    processedToken: actualToken.substring(0, 20) + '...'
                });
                
                this.appState.setToken(actualToken);
                this.appState.setUserInfo(response.data.user || { email });
                
                logger.success('API', '登录API调用成功');
                return { success: true, token: actualToken, user: response.data.user || { email } };
            } else {
                logger.error('API', '登录响应格式错误', response.data);
                throw new Error('登录失败：无效的响应');
            }
        } catch (error) {
            logger.error('API', '登录API调用失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '登录失败，请检查网络连接');
        }
    }

    /**
     * @function getBackendUsers - 获取后台用户列表
     * @returns {Promise<array>} 用户列表
     */
    async getBackendUsers() {
        // 本地模式下返回本地数据
        if (window.localDataProvider && window.localDataProvider.isLocalModeEnabled()) {
            logger.info('API', '本地模式：使用本地后端用户数据');
            const users = window.localDataProvider.data.backendUsers;
            this.appState.cacheSystemData('backendUsers', users);
            return users;
        }

        const url = this.buildUrl('backend_users');
        logger.info('API', '发起获取后端用户请求', { url });
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取后端用户失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            // 添加调试日志查看实际响应结构
            logger.debug('API', '后端用户API原始响应', {
                status: response.status,
                data: response.data,
                dataType: typeof response.data,
                hasData: !!response.data.data,
                dataLength: response.data.data ? response.data.data.length : 'N/A'
            });

            const users = response.data.data || [];

            // 记录详细的API响应日志
            logger.logApiResponse('GET', url, response.status, {
                total_count: users.length,
                users: users,
                raw_response: response.data
            });

            this.appState.cacheSystemData('backendUsers', users);

            logger.success('API', '获取后端用户成功', {
                userCount: users.length,
                userNames: users.map(u => u.name || u.username || u.id).slice(0, 5)
            });
            return users;
        } catch (error) {
            logger.error('API', '获取后端用户失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取后端用户失败');
        }
    }

    /**
     * @function getSubCategories - 获取子分类列表
     * @returns {Promise<array>} 子分类列表
     */
    async getSubCategories() {
        // 本地模式下返回本地数据
        if (window.localDataProvider && window.localDataProvider.isLocalModeEnabled()) {
            logger.info('API', '本地模式：使用本地子分类数据');
            const categories = window.localDataProvider.data.subCategories;
            this.appState.cacheSystemData('subCategories', categories);
            return categories;
        }

        const url = this.buildUrl('sub_category');
        logger.info('API', '发起获取子分类请求', { url });
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取子分类失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            // 添加调试日志查看实际响应结构
            logger.debug('API', '子分类API原始响应', {
                status: response.status,
                data: response.data,
                dataType: typeof response.data,
                hasData: !!response.data.data,
                dataLength: response.data.data ? response.data.data.length : 'N/A'
            });

            const categories = response.data.data || [];

            // 记录详细的API响应日志
            logger.logApiResponse('GET', url, response.status, {
                total_count: categories.length,
                categories: categories,
                raw_response: response.data
            });

            this.appState.cacheSystemData('subCategories', categories);

            logger.success('API', '获取子分类成功', {
                categoryCount: categories.length,
                categoryNames: categories.map(c => c.name || c.title || c.id).slice(0, 5)
            });
            return categories;
        } catch (error) {
            logger.error('API', '获取子分类失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取子分类失败');
        }
    }

    /**
     * @function getCarTypes - 获取车型列表
     * @returns {Promise<array>} 车型列表
     */
    async getCarTypes() {
        // 本地模式下返回本地数据
        if (window.localDataProvider && window.localDataProvider.isLocalModeEnabled()) {
            logger.info('API', '本地模式：使用本地车型数据');
            const carTypes = window.localDataProvider.data.carTypes;
            this.appState.cacheSystemData('carTypes', carTypes);
            return carTypes;
        }

        const url = this.buildUrl('car_types');
        logger.info('API', '发起获取车型请求', { url });
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取车型失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            const carTypes = response.data.data || [];

            // 记录详细的API响应日志
            logger.logApiResponse('GET', url, response.status, {
                total_count: carTypes.length,
                car_types: carTypes
            });

            this.appState.cacheSystemData('carTypes', carTypes);

            logger.success('API', '获取车型成功', {
                carTypeCount: carTypes.length,
                carTypeNames: carTypes.map(c => c.name || c.title || c.type || c.id).slice(0, 5)
            });
            return carTypes;
        } catch (error) {
            logger.error('API', '获取车型失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取车型失败');
        }
    }

    /**
     * @function getDrivingRegions - 获取行驶区域列表
     * @returns {Promise<array>} 行驶区域列表
     */
    async getDrivingRegions() {
        // 本地模式下返回本地数据
        if (window.localDataProvider && window.localDataProvider.isLocalModeEnabled()) {
            logger.info('API', '本地模式：使用本地行驶区域数据');
            const regions = window.localDataProvider.data.drivingRegions;
            this.appState.cacheSystemData('drivingRegions', regions);
            return regions;
        }

        const url = this.buildUrl('driving_regions');
        logger.info('API', '发起获取行驶区域请求', { url });
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取行驶区域失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            const regions = response.data.data || [];

            // 记录详细的API响应日志
            logger.logApiResponse('GET', url, response.status, {
                total_count: regions.length,
                regions: regions
            });

            this.appState.cacheSystemData('drivingRegions', regions);

            logger.success('API', '获取行驶区域成功', {
                regionCount: regions.length,
                regionNames: regions.map(r => r.name || r.title || r.id).slice(0, 5)
            });
            return regions;
        } catch (error) {
            logger.error('API', '获取行驶区域失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取行驶区域失败');
        }
    }

    /**
     * @function getLanguages - 获取语言列表
     * @returns {Promise<array>} 语言列表
     */
    async getLanguages() {
        // 本地模式下返回本地数据
        if (window.localDataProvider && window.localDataProvider.isLocalModeEnabled()) {
            logger.info('API', '本地模式：使用本地语言数据');
            const languages = window.localDataProvider.data.languages;
            this.appState.cacheSystemData('languages', languages);
            return languages;
        }

        const url = this.buildUrl('languages');
        logger.info('API', '发起获取语言请求', { url });
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取语言失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            const languages = response.data.data || [];

            // 记录详细的API响应日志
            logger.logApiResponse('GET', url, response.status, {
                total_count: languages.length,
                languages: languages
            });

            this.appState.cacheSystemData('languages', languages);

            logger.success('API', '获取语言成功', {
                languageCount: languages.length,
                languageNames: languages.map(l => l.name || l.title || l.id).slice(0, 5)
            });
            return languages;
        } catch (error) {
            logger.error('API', '获取语言失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取语言失败');
        }
    }

    /**
     * @function createOrder - 创建订单
     * @param {object} orderData - 订单数据
     * @returns {Promise<object>} 创建结果
     */
    async createOrder(orderData) {
        // 本地模式下模拟订单创建成功
        if (window.localDataProvider && window.localDataProvider.isLocalModeEnabled()) {
            logger.info('API', '本地模式：模拟创建订单', {
                hasData: !!orderData,
                dataKeys: Object.keys(orderData || {})
            });
            
            // 模拟API响应延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 生成模拟订单ID
            const mockOrderId = 'LOCAL_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            const mockResponse = {
                status: true,
                message: '订单创建成功（本地模式模拟）',
                data: {
                    order_id: mockOrderId,
                    ota_reference_number: orderData.ota_reference_number,
                    status: 'pending',
                    created_at: new Date().toISOString()
                }
            };
            
            logger.success('API', '本地模式：订单创建模拟成功', {
                orderId: mockOrderId,
                otaReference: orderData.ota_reference_number
            });
            
            return mockResponse;
        }

        const requestStartTime = Date.now();
        const method = 'POST';
        const url = `${this.baseUrl}/create_order`;

        logger.info('API', '发起创建订单请求', {
            hasData: !!orderData,
            dataKeys: Object.keys(orderData || {})
        });

        // DEBUG: Log the actual orderData to see if ota_reference_number is present


        // 记录API请求日志（隐藏敏感信息）
        const sanitizedOrderData = {
            ...orderData,
            customer_contact: orderData.customer_contact ? '***' + orderData.customer_contact.slice(-4) : undefined,
            customer_email: orderData.customer_email ? orderData.customer_email.replace(/(.{2}).*(@.*)/, '$1***$2') : undefined
        };

        logger.logApiRequest(method, url, sanitizedOrderData, {
            'Content-Type': 'application/json'
        });

        try {
            // 获取认证token
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '创建订单失败：未找到认证token');
                throw new Error('未找到认证token，请重新登录');
            }

            // 验证和格式化订单数据
            const validatedOrderData = this.validateAndFormatOrderData(orderData);

            logger.debug('API', '发送订单数据', {
                hasToken: !!token,
                tokenPrefix: token.substring(0, 10) + '...',
                orderDataKeys: Object.keys(validatedOrderData),
                dateFormat: validatedOrderData.date || validatedOrderData.service_date
            });

            const response = await axios.post(url, validatedOrderData, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            const responseTime = Date.now() - requestStartTime;

            // 记录API响应日志
            logger.logApiResponse(method, url, response.status, response.data, responseTime);

            logger.success('API', '创建订单成功', {
                responseTime: `${responseTime}ms`,
                orderId: response.data?.id || 'unknown'
            });

            // 显示API响应通知
            if (window.notificationManager) {
                window.notificationManager.showApiResponse(response.data, '创建订单');
            }

            return response.data;
        } catch (error) {
            const responseTime = Date.now() - requestStartTime;
            const status = error.response?.status || 0;

            // 记录API错误响应日志
            logger.logApiResponse(method, url, status, error.response?.data, responseTime);

            logger.error('API', '创建订单失败', {
                message: error.message,
                response: error.response?.data,
                status: status,
                responseTime: `${responseTime}ms`,
                orderData: sanitizedOrderData
            });

            // 显示API错误响应通知
            if (window.notificationManager && error.response?.data) {
                window.notificationManager.showApiResponse(error.response.data, '创建订单');
            }

            throw new Error(error.response?.data?.message || '创建订单失败');
        }
    }

    /**
     * @function validateAndFormatOrderData - 验证和格式化订单数据
     * @description 验证订单数据的完整性并格式化为API期望的格式
     * @param {object} orderData - 原始订单数据
     * @returns {object} 验证和格式化后的订单数据
     */
    validateAndFormatOrderData(orderData) {
        try {
            const formattedData = { ...orderData };

            // 1. 验证必填字段
            const requiredFields = [
                'sub_category_id',
                'car_type_id',
                'incharge_by_backend_user_id',
                'customer_name',
                'customer_contact',
                'customer_email',
                'pickup',
                'destination',
                'date',
                'time',
                'passenger_number',
                'driving_region_id'
            ];

            const missingFields = [];
            requiredFields.forEach(field => {
                if (!formattedData[field] && formattedData[field] !== 0) {
                    missingFields.push(field);
                }
            });

            if (missingFields.length > 0) {
                logger.warn('API', '订单数据缺少必填字段', { missingFields, orderData });

                // 尝试设置默认值
                this.setDefaultValues(formattedData, missingFields);
            }

            // 2. 格式化数字字段
            const numericFields = ['sub_category_id', 'car_type_id', 'incharge_by_backend_user_id', 'passenger_number', 'driving_region_id'];
            numericFields.forEach(field => {
                if (formattedData[field] !== undefined) {
                    const numValue = parseInt(formattedData[field]);
                    if (!isNaN(numValue)) {
                        formattedData[field] = numValue;
                    }
                }
            });

            // 3. 格式化日期为DD-MM-YYYY
            if (formattedData.date) {
                formattedData.date = this.formatDateToDDMMYYYY(formattedData.date);
            }
            if (formattedData.service_date) {
                formattedData.service_date = this.formatDateToDDMMYYYY(formattedData.service_date);
                // 统一使用date字段
                if (!formattedData.date) {
                    formattedData.date = formattedData.service_date;
                }
                delete formattedData.service_date;
            }

            // 4. 格式化时间
            if (formattedData.time) {
                formattedData.time = this.formatTime(formattedData.time);
            }
            if (formattedData.service_time && !formattedData.time) {
                formattedData.time = this.formatTime(formattedData.service_time);
                delete formattedData.service_time;
            }

            // 5. 统一字段名称
            const fieldMappings = {
                'pickup_location': 'pickup',
                'drop_location': 'destination',
                'passenger_count': 'passenger_number',
                'backend_user_id': 'incharge_by_backend_user_id'
            };

            Object.entries(fieldMappings).forEach(([oldField, newField]) => {
                if (formattedData[oldField] && !formattedData[newField]) {
                    formattedData[newField] = formattedData[oldField];
                    delete formattedData[oldField];
                }
            });

            // 6. 清理空值和无效字段
            Object.keys(formattedData).forEach(key => {
                if (formattedData[key] === null || formattedData[key] === undefined || formattedData[key] === '') {
                    delete formattedData[key];
                }
            });

            // 7. 确保OTA参考号
            if (!formattedData.ota_reference_number) {
                formattedData.ota_reference_number = this.generateOTAReference();
            }

            logger.debug('API', '订单数据验证和格式化完成', {
                originalKeys: Object.keys(orderData),
                formattedKeys: Object.keys(formattedData),
                dateFormat: formattedData.date,
                timeFormat: formattedData.time
            });

            return formattedData;

        } catch (error) {
            logger.error('API', '订单数据验证失败', { error: error.message, orderData });
            throw new Error('订单数据格式错误: ' + error.message);
        }
    }

    /**
     * @function setDefaultValues - 设置默认值
     * @description 为缺失的必填字段设置默认值
     * @param {object} orderData - 订单数据
     * @param {Array} missingFields - 缺失的字段
     */
    setDefaultValues(orderData, missingFields) {
        const defaults = {
            'sub_category_id': 2, // 默认接机服务
            'car_type_id': 5,     // 默认5座车
            'incharge_by_backend_user_id': 1, // 默认管理员
            'customer_name': 'Test Customer',
            'customer_contact': '+60123456789',
            'customer_email': '<EMAIL>',
            'pickup': 'KLIA Airport',
            'destination': 'KLCC',
            'date': this.formatDateToDDMMYYYY(new Date()),
            'time': '10:00',
            'passenger_number': 1,
            'driving_region_id': 1 // 默认KL/Selangor
        };

        missingFields.forEach(field => {
            if (defaults[field]) {
                orderData[field] = defaults[field];
                logger.info('API', `设置默认值: ${field} = ${defaults[field]}`);
            }
        });
    }

    /**
     * @function formatDateToDDMMYYYY - 格式化日期为DD-MM-YYYY
     * @description 将各种日期格式转换为DD-MM-YYYY格式
     * @param {string|Date} date - 日期
     * @returns {string} DD-MM-YYYY格式的日期
     */
    formatDateToDDMMYYYY(date) {
        try {
            let dateObj;

            if (date instanceof Date) {
                dateObj = date;
            } else if (typeof date === 'string') {
                // 检查是否已经是DD-MM-YYYY格式
                if (/^\d{2}-\d{2}-\d{4}$/.test(date)) {
                    return date;
                }
                dateObj = new Date(date);
            } else {
                dateObj = new Date();
            }

            if (isNaN(dateObj.getTime())) {
                logger.warn('API', '无效日期，使用当前日期', { originalDate: date });
                dateObj = new Date();
            }

            const day = dateObj.getDate().toString().padStart(2, '0');
            const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
            const year = dateObj.getFullYear();

            return `${day}-${month}-${year}`;
        } catch (error) {
            logger.error('API', '日期格式化失败', { date, error: error.message });
            return this.formatDateToDDMMYYYY(new Date());
        }
    }

    /**
     * @function formatTime - 格式化时间
     * @description 格式化时间为HH:MM格式
     * @param {string} time - 时间
     * @returns {string} HH:MM格式的时间
     */
    formatTime(time) {
        try {
            if (!time) return '10:00';

            // 如果已经是HH:MM格式
            if (/^\d{2}:\d{2}$/.test(time)) {
                return time;
            }

            // 尝试解析其他格式
            const timeMatch = time.match(/(\d{1,2}):?(\d{2})/);
            if (timeMatch) {
                const hours = timeMatch[1].padStart(2, '0');
                const minutes = timeMatch[2] || '00';
                return `${hours}:${minutes}`;
            }

            return '10:00'; // 默认时间
        } catch (error) {
            logger.warn('API', '时间格式化失败，使用默认时间', { time, error: error.message });
            return '10:00';
        }
    }

    /**
     * @function generateOTAReference - 生成OTA参考号
     * @description 生成唯一的OTA参考号
     * @returns {string} OTA参考号
     */
    generateOTAReference() {
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
        const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `API${dateStr}${timeStr}${randomNum}`;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiService;
} else if (typeof window !== 'undefined') {
    window.ApiService = ApiService;
}
