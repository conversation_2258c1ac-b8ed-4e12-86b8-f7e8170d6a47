/**
 * @file resilience-manager.js - 统一弹性管理器
 * @description 合并数据一致性管理和错误恢复功能的统一管理器
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-08
 * @version v4.2.0
 */

/**
 * @class ResilienceManager - 统一弹性管理器
 * @description 提供数据一致性验证、错误分析、自动恢复、重试机制的统一管理
 */
class ResilienceManager {
    /**
     * @function constructor - 构造函数
     * @description 初始化弹性管理器
     * @param {AppState} appState - 应用状态实例
     * @param {ApiService} apiService - API服务实例
     */
    constructor(appState, apiService) {
        this.appState = appState;
        this.apiService = apiService;
        
        // 数据一致性配置
        this.cacheExpiry = 24 * 60 * 60 * 1000; // 24小时缓存有效期
        this.lastValidationTime = 0;
        this.validationInterval = 5 * 60 * 1000; // 5分钟验证间隔
        
        // 错误恢复配置
        this.retryConfig = SYSTEM_CONFIG.ERROR_HANDLING.RETRY_CONFIG;
        
        // 新增：五维数据加载配置
        this.dataLoadingConfig = {
            coreDataTypes: ['backendUsers', 'subCategories', 'carTypes'], // 核心数据（阻塞加载）
            enhancedDataTypes: ['drivingRegions', 'languages'], // 增强数据（后台加载）
            loadingTimeout: 30000, // 30秒超时
            fallbackMode: false // 降级模式标志
        };
        
        // 新增：数据加载统计
        this.loadingStats = {
            coreDataLoadTime: 0,
            enhancedDataLoadTime: 0,
            totalLoadTime: 0,
            failedLoads: [],
            lastLoadTimestamp: 0
        };
        
        logger.info('Resilience', '统一弹性管理器初始化完成', { 
            version: 'v4.2.0',
            features: ['数据一致性验证', '错误分析', '自动恢复', '重试机制', '五维数据支持', '分层加载策略']
        });
    }

    // ==================== 数据一致性管理功能 ====================

    /**
     * @function validateUserData - 验证用户数据
     * @description 验证当前用户的系统数据是否有效和一致（支持五维数据）
     * @returns {Promise<boolean>} 验证结果
     */
    async validateUserData() {
        logger.info('Resilience', '开始验证用户数据（五维数据支持）');

        try {
            // 检查验证间隔
            const now = Date.now();
            if (now - this.lastValidationTime < this.validationInterval) {
                logger.debug('Resilience', '跳过验证，间隔时间未到');
                return true;
            }

            // 1. 检查数据完整性（包含新数据类型）
            const integrityResult = this.checkDataIntegrity();
            if (!integrityResult.isValid) {
                logger.warn('Resilience', '数据完整性检查失败', integrityResult);
                return false;
            }

            // 2. 检查缓存时效性
            const cacheResult = this.validateCacheExpiry();
            if (!cacheResult.isValid) {
                logger.warn('Resilience', '缓存时效性检查失败', cacheResult);
                return false;
            }

            // 3. 检查数据一致性（包含新API数据）
            const consistencyResult = await this.checkDataConsistency();
            if (!consistencyResult.isValid) {
                logger.warn('Resilience', '数据一致性检查失败', consistencyResult);
                return false;
            }

            this.lastValidationTime = now;
            logger.success('Resilience', '用户数据验证通过（五维数据）');
            return true;

        } catch (error) {
            logger.error('Resilience', '用户数据验证失败', error);
            return false;
        }
    }

    /**
     * @function checkDataIntegrity - 检查数据完整性
     * @description 检查必需的系统数据是否存在且有效（支持五维数据）
     * @returns {object} 完整性检查结果
     */
    checkDataIntegrity() {
        // 核心数据（必需）
        const coreDataKeys = this.dataLoadingConfig.coreDataTypes;
        // 增强数据（可选，但影响功能完整性）
        const enhancedDataKeys = this.dataLoadingConfig.enhancedDataTypes;
        
        const result = {
            isValid: true,
            missingCoreData: [],
            missingEnhancedData: [],
            emptyData: [],
            details: {},
            fallbackMode: this.dataLoadingConfig.fallbackMode
        };

        // 检查核心数据
        coreDataKeys.forEach(key => {
            const data = this.appState[key];
            result.details[key] = {
                exists: !!data,
                isArray: Array.isArray(data),
                length: data ? data.length : 0,
                type: 'core'
            };

            if (!data) {
                result.missingCoreData.push(key);
                result.isValid = false;
            } else if (Array.isArray(data) && data.length === 0) {
                result.emptyData.push(key);
                result.isValid = false;
            }
        });

        // 检查增强数据（不影响核心功能）
        enhancedDataKeys.forEach(key => {
            const data = this.appState[key];
            result.details[key] = {
                exists: !!data,
                isArray: Array.isArray(data),
                length: data ? data.length : 0,
                type: 'enhanced'
            };

            if (!data || (Array.isArray(data) && data.length === 0)) {
                result.missingEnhancedData.push(key);
                // 增强数据缺失不影响核心验证，但记录警告
                logger.warn('Resilience', `增强数据缺失: ${key}，将使用降级模式`);
            }
        });

        // 如果有增强数据缺失，启用降级模式
        if (result.missingEnhancedData.length > 0) {
            this.dataLoadingConfig.fallbackMode = true;
            result.fallbackMode = true;
        }

        return result;
    }

    /**
     * @function validateCacheExpiry - 验证缓存时效性
     * @description 检查缓存数据是否在有效期内（支持新数据类型）
     * @returns {object} 时效性检查结果
     */
    validateCacheExpiry() {
        const result = {
            isValid: true,
            expiredData: [],
            cacheInfo: {},
            enhancedDataStatus: {}
        };

        if (!this.appState.currentUserHash) {
            // 无用户标识时，跳过缓存时效检查
            result.isValid = true;
            return result;
        }

        const userCache = this.appState.userDataCache.get(this.appState.currentUserHash);
        if (!userCache) {
            result.isValid = false;
            result.expiredData.push('userCache');
            return result;
        }

        const now = Date.now();
        const cacheAge = now - userCache.cacheTime;
        
        result.cacheInfo = {
            cacheTime: userCache.cacheTime,
            lastUpdate: userCache.lastUpdate,
            age: cacheAge,
            maxAge: this.cacheExpiry
        };

        // 检查核心数据缓存
        if (cacheAge > this.cacheExpiry) {
            result.isValid = false;
            result.expiredData.push('userCache');
        }

        // 检查增强数据缓存状态
        this.dataLoadingConfig.enhancedDataTypes.forEach(dataType => {
            const enhancedCacheKey = `${this.appState.currentUserHash}_${dataType}`;
            const enhancedCache = localStorage.getItem(enhancedCacheKey);
            
            result.enhancedDataStatus[dataType] = {
                cached: !!enhancedCache,
                timestamp: enhancedCache ? JSON.parse(enhancedCache).timestamp : null
            };
        });

        return result;
    }

    /**
     * @function checkDataConsistency - 检查数据一致性
     * @description 检查本地数据与服务器数据的一致性（支持五维数据）
     * @returns {Promise<object>} 一致性检查结果
     */
    async checkDataConsistency() {
        const result = {
            isValid: true,
            inconsistentData: [],
            details: {},
            enhancedDataConsistency: {}
        };

        try {
            // 检查核心数据的关键ID有效性
            const idValidation = await this.validateApiIds();
            result.details.idValidation = idValidation;
            
            if (!idValidation.isValid) {
                result.isValid = false;
                result.inconsistentData.push('apiIds');
            }

            // 检查增强数据一致性（非阻塞）
            const enhancedValidation = await this.validateEnhancedDataConsistency();
            result.enhancedDataConsistency = enhancedValidation;
            
            // 增强数据不一致不影响核心功能，但记录警告
            if (!enhancedValidation.isValid) {
                logger.warn('Resilience', '增强数据一致性检查失败', enhancedValidation);
            }

            return result;

        } catch (error) {
            logger.error('Resilience', '数据一致性检查异常', error);
            result.isValid = false;
            result.error = error.message;
            return result;
        }
    }

    /**
     * @function validateEnhancedDataConsistency - 验证增强数据一致性
     * @description 验证driving_regions和languages数据的一致性
     * @returns {Promise<object>} 增强数据一致性结果
     */
    async validateEnhancedDataConsistency() {
        const result = {
            isValid: true,
            inconsistentTypes: [],
            details: {}
        };

        try {
            // 验证driving_regions数据
            if (this.appState.drivingRegions && this.appState.drivingRegions.length > 0) {
                const regionsValidation = await this.validateDataTypeConsistency('drivingRegions');
                result.details.drivingRegions = regionsValidation;
                
                if (!regionsValidation.isValid) {
                    result.isValid = false;
                    result.inconsistentTypes.push('drivingRegions');
                }
            }

            // 验证languages数据
            if (this.appState.languages && this.appState.languages.length > 0) {
                const languagesValidation = await this.validateDataTypeConsistency('languages');
                result.details.languages = languagesValidation;
                
                if (!languagesValidation.isValid) {
                    result.isValid = false;
                    result.inconsistentTypes.push('languages');
                }
            }

            return result;

        } catch (error) {
            logger.error('Resilience', '增强数据一致性验证异常', error);
            result.isValid = false;
            result.error = error.message;
            return result;
        }
    }

    /**
     * @function validateDataTypeConsistency - 验证特定数据类型一致性
     * @description 验证特定数据类型与服务器的一致性
     * @param {string} dataType - 数据类型名称
     * @returns {Promise<object>} 数据类型一致性结果
     */
    async validateDataTypeConsistency(dataType) {
        const result = {
            isValid: true,
            invalidIds: [],
            checkedIds: [],
            sampleSize: 0
        };

        try {
            const localData = this.appState[dataType];
            if (!localData || localData.length === 0) {
                result.isValid = true; // 无数据时认为一致
                return result;
            }

            // 随机抽样验证（避免过多API调用）
            const sampleSize = Math.min(3, localData.length);
            const sampleData = localData.slice(0, sampleSize);
            result.sampleSize = sampleSize;

            // 获取最新数据进行对比
            let latestData = [];
            switch (dataType) {
                case 'drivingRegions':
                    const regionsResult = await this.apiService.getDrivingRegions();
                    latestData = regionsResult.success ? regionsResult.data : [];
                    break;
                case 'languages':
                    const languagesResult = await this.apiService.getLanguages();
                    latestData = languagesResult.success ? languagesResult.data : [];
                    break;
                default:
                    logger.warn('Resilience', `未知的数据类型: ${dataType}`);
                    return result;
            }

            const latestIds = new Set(latestData.map(item => item.id));

            sampleData.forEach(item => {
                result.checkedIds.push(item.id);
                if (!latestIds.has(item.id)) {
                    result.invalidIds.push(item.id);
                    result.isValid = false;
                }
            });

            return result;

        } catch (error) {
            logger.error('Resilience', `${dataType}数据一致性验证失败`, error);
            result.isValid = false;
            result.error = error.message;
            return result;
        }
    }

    /**
     * @function validateApiIds - 验证API ID有效性
     * @description 验证本地缓存的ID在服务器端是否仍然有效
     * @returns {Promise<object>} ID验证结果
     */
    async validateApiIds() {
        const result = {
            isValid: true,
            invalidIds: [],
            validationDetails: {}
        };

        try {
            // 随机抽样验证（避免过多API调用）
            const sampleSize = 3;
            
            // 验证后台用户ID
            if (this.appState.backendUsers && this.appState.backendUsers.length > 0) {
                const userSample = this.appState.backendUsers.slice(0, sampleSize);
                const userValidation = await this.validateUserIds(userSample);
                result.validationDetails.users = userValidation;
                
                if (!userValidation.isValid) {
                    result.isValid = false;
                    result.invalidIds.push(...userValidation.invalidIds);
                }
            }

            return result;

        } catch (error) {
            logger.error('Resilience', 'API ID验证异常', error);
            result.isValid = false;
            result.error = error.message;
            return result;
        }
    }

    /**
     * @function validateUserIds - 验证用户ID有效性
     * @description 验证用户ID是否在服务器端仍然有效
     * @param {Array} users - 用户列表样本
     * @returns {Promise<object>} 用户ID验证结果
     */
    async validateUserIds(users) {
        const result = {
            isValid: true,
            invalidIds: [],
            checkedIds: []
        };

        try {
            // 获取最新的用户列表进行对比
            const latestUsers = await this.apiService.getBackendUsers();
            const latestUserIds = new Set(latestUsers.map(user => user.id));

            users.forEach(user => {
                result.checkedIds.push(user.id);
                if (!latestUserIds.has(user.id)) {
                    result.invalidIds.push(user.id);
                    result.isValid = false;
                }
            });

            return result;

        } catch (error) {
            logger.error('Resilience', '用户ID验证失败', error);
            result.isValid = false;
            result.error = error.message;
            return result;
        }
    }

    /**
     * @function forceDataRefresh - 强制数据刷新
     * @description 强制刷新所有系统数据（支持五维数据分层加载）
     * @returns {Promise<boolean>} 刷新结果
     */
    async forceDataRefresh() {
        logger.info('Resilience', '开始强制数据刷新（五维数据分层加载）');

        try {
            // 清理缓存
            this.clearDataCache();
            
            // 重新加载系统数据（分层加载）
            await this.refreshSystemDataWithLayeredLoading();
            
            logger.success('Resilience', '强制数据刷新完成');
            return true;

        } catch (error) {
            logger.error('Resilience', '强制数据刷新失败', error);
            return false;
        }
    }

    /**
     * @function clearDataCache - 清理数据缓存
     * @description 清理所有缓存数据（包含新数据类型）
     */
    clearDataCache() {
        logger.info('Resilience', '清理数据缓存（包含五维数据）');

        try {
            // 清理用户数据缓存
            this.appState.userDataCache.clear();
            
            // 清理当前用户的核心系统数据
            this.appState.backendUsers = [];
            this.appState.subCategories = [];
            this.appState.carTypes = [];
            
            // 清理增强数据
            this.appState.drivingRegions = [];
            this.appState.languages = [];
            
            // 清理增强数据的本地存储
            if (this.appState.currentUserHash) {
                this.dataLoadingConfig.enhancedDataTypes.forEach(dataType => {
                    const cacheKey = `${this.appState.currentUserHash}_${dataType}`;
                    localStorage.removeItem(cacheKey);
                });
            }
            
            // 重置验证时间和降级模式
            this.lastValidationTime = 0;
            this.dataLoadingConfig.fallbackMode = false;
            
            // 重置加载统计
            this.loadingStats = {
                coreDataLoadTime: 0,
                enhancedDataLoadTime: 0,
                totalLoadTime: 0,
                failedLoads: [],
                lastLoadTimestamp: 0
            };
            
            logger.success('Resilience', '数据缓存清理完成（五维数据）');

        } catch (error) {
            logger.error('Resilience', '数据缓存清理失败', error);
        }
    }

    /**
     * @function refreshSystemData - 刷新系统数据
     * @description 重新加载所有系统数据（兼容性方法）
     * @returns {Promise<boolean>} 刷新结果
     */
    async refreshSystemData() {
        return await this.refreshSystemDataWithLayeredLoading();
    }

    /**
     * @function refreshSystemDataWithLayeredLoading - 分层加载系统数据
     * @description 使用分层策略重新加载系统数据，优化性能和用户体验
     * @returns {Promise<boolean>} 刷新结果
     */
    async refreshSystemDataWithLayeredLoading() {
        logger.info('Resilience', '开始分层加载系统数据');
        const startTime = Date.now();

        try {
            // 第一层：核心数据（阻塞加载）
            const coreLoadStart = Date.now();
            logger.info('Resilience', '加载核心数据（阻塞）');
            
            const coreDataPromises = [
                this.apiService.getBackendUsers(),
                this.apiService.getSubCategories(),
                this.apiService.getCarTypes()
            ];

            await Promise.all(coreDataPromises);
            this.loadingStats.coreDataLoadTime = Date.now() - coreLoadStart;
            
            logger.success('Resilience', '核心数据加载完成', {
                loadTime: this.loadingStats.coreDataLoadTime + 'ms'
            });

            // 第二层：增强数据（后台加载，不阻塞）
            this.loadEnhancedDataInBackground();

            this.loadingStats.lastLoadTimestamp = Date.now();
            logger.success('Resilience', '系统数据刷新完成（分层加载）');
            return true;

        } catch (error) {
            logger.error('Resilience', '系统数据刷新失败', error);
            this.loadingStats.failedLoads.push({
                timestamp: Date.now(),
                error: error.message,
                type: 'core_data'
            });
            return false;
        }
    }

    /**
     * @function loadEnhancedDataInBackground - 后台加载增强数据
     * @description 在后台异步加载增强数据，不阻塞核心功能
     */
    async loadEnhancedDataInBackground() {
        logger.info('Resilience', '开始后台加载增强数据');
        const enhancedLoadStart = Date.now();

        try {
            const enhancedDataPromises = [
                this.apiService.getDrivingRegions(),
                this.apiService.getLanguages()
            ];

            // 使用Promise.allSettled确保部分失败不影响其他数据
            const results = await Promise.allSettled(enhancedDataPromises);
            
            // 处理结果
            results.forEach((result, index) => {
                const dataType = this.dataLoadingConfig.enhancedDataTypes[index];
                
                if (result.status === 'fulfilled') {
                    logger.success('Resilience', `${dataType}数据加载成功`);
                } else {
                    logger.warn('Resilience', `${dataType}数据加载失败`, result.reason);
                    this.loadingStats.failedLoads.push({
                        timestamp: Date.now(),
                        error: result.reason?.message || '未知错误',
                        type: dataType
                    });
                }
            });

            this.loadingStats.enhancedDataLoadTime = Date.now() - enhancedLoadStart;
            this.loadingStats.totalLoadTime = Date.now() - this.loadingStats.lastLoadTimestamp;
            
            // 检查是否需要启用降级模式
            const failedEnhancedLoads = this.loadingStats.failedLoads.filter(
                fail => this.dataLoadingConfig.enhancedDataTypes.includes(fail.type)
            );
            
            if (failedEnhancedLoads.length > 0) {
                this.dataLoadingConfig.fallbackMode = true;
                logger.warn('Resilience', '部分增强数据加载失败，启用降级模式', {
                    failedTypes: failedEnhancedLoads.map(f => f.type)
                });
            }

            logger.success('Resilience', '增强数据后台加载完成', {
                loadTime: this.loadingStats.enhancedDataLoadTime + 'ms',
                fallbackMode: this.dataLoadingConfig.fallbackMode
            });

        } catch (error) {
            logger.error('Resilience', '增强数据后台加载异常', error);
            this.dataLoadingConfig.fallbackMode = true;
        }
    }

    /**
     * @function getDataLoadingStrategy - 获取数据加载策略
     * @description 根据当前状态返回最优的数据加载策略
     * @returns {object} 加载策略配置
     */
    getDataLoadingStrategy() {
        return {
            strategy: this.dataLoadingConfig.fallbackMode ? 'fallback' : 'full',
            coreDataTypes: this.dataLoadingConfig.coreDataTypes,
            enhancedDataTypes: this.dataLoadingConfig.enhancedDataTypes,
            fallbackMode: this.dataLoadingConfig.fallbackMode,
            loadingTimeout: this.dataLoadingConfig.loadingTimeout,
            stats: this.loadingStats
        };
    }

    /**
     * @function optimizeLoadingPerformance - 优化加载性能
     * @description 根据历史数据优化加载性能配置，集成性能优化器功能
     */
    optimizeLoadingPerformance() {
        const avgCoreLoadTime = this.loadingStats.coreDataLoadTime;
        const avgEnhancedLoadTime = this.loadingStats.enhancedDataLoadTime;
        
        // 根据历史加载时间调整超时配置
        if (avgCoreLoadTime > 15000) { // 核心数据加载超过15秒
            this.dataLoadingConfig.loadingTimeout = Math.max(45000, avgCoreLoadTime * 2);
            logger.info('Resilience', '调整加载超时配置', {
                newTimeout: this.dataLoadingConfig.loadingTimeout
            });
        }
        
        // 如果增强数据经常失败，建议预加载
        const recentFailures = this.loadingStats.failedLoads.filter(
            fail => Date.now() - fail.timestamp < 60000 // 最近1分钟的失败
        );
        
        if (recentFailures.length > 2) {
            logger.warn('Resilience', '检测到频繁的增强数据加载失败，建议检查网络连接');
        }
        
        // 如果有性能优化器，集成增量更新策略
        this.integratePerformanceOptimizer();
    }

    /**
     * @function integratePerformanceOptimizer - 集成性能优化器
     * @description 与性能优化器协作，使用智能增量更新策略
     */
    integratePerformanceOptimizer() {
        try {
            if (window.app && window.app.performanceOptimizer) {
                const optimizer = window.app.performanceOptimizer;
                
                // 检查是否需要执行增量更新
                optimizer.checkDataCacheStatus().then(needsUpdate => {
                    if (needsUpdate) {
                        logger.info('Resilience', '性能优化器建议执行增量更新');
                        
                        // 使用性能优化器的增量更新策略
                        optimizer.performIncrementalUpdate().then(updateResult => {
                            if (updateResult) {
                                logger.success('Resilience', '增量更新成功');
                                this.resetFailureStats();
                            }
                        }).catch(error => {
                            logger.warn('Resilience', '增量更新失败，使用标准刷新策略', error);
                        });
                    }
                }).catch(error => {
                    logger.debug('Resilience', '性能优化器检查失败，跳过集成', error);
                });
            }
            
        } catch (error) {
            logger.debug('Resilience', '性能优化器集成跳过', error);
        }
    }

    /**
     * @function resetFailureStats - 重置失败统计
     * @description 重置失败统计计数器
     */
    resetFailureStats() {
        this.loadingStats.failedLoads = [];
        logger.debug('Resilience', '失败统计已重置');
    }

    /**
     * @function scheduleValidation - 启动定时验证
     * @description 启动定时数据验证任务（支持五维数据）
     */
    scheduleValidation() {
        logger.info('Resilience', '启动定时数据验证（五维数据支持）', {
            interval: this.validationInterval / 1000 + '秒'
        });

        // 设置定时验证
        setInterval(async () => {
            if (this.appState.token && this.appState.userInfo) {
                await this.validateUserData();
                
                // 定期优化加载性能
                this.optimizeLoadingPerformance();
            }
        }, this.validationInterval);
    }

    // ==================== 错误恢复管理功能 ====================

    /**
     * @function analyzeError - 错误分析
     * @description 分析错误并确定是否可重试
     * @param {Error} error - 错误对象
     * @returns {object} 错误分析结果
     */
    analyzeError(error) {
        const message = error.message?.toLowerCase() || '';
        const status = error.response?.status;
        
        // 简化的错误分类：只区分可重试和不可重试
        const isRetryable = this.isRetryableError(error);
        
        const analysis = {
            originalError: error,
            isRetryable,
            errorType: isRetryable ? 'RETRYABLE' : 'NON_RETRYABLE',
            status,
            message: error.message,
            timestamp: new Date().toISOString()
        };

        logger.debug('Resilience', '错误分析完成', {
            isRetryable,
            status,
            message: message.substring(0, 100)
        });
        
        return analysis;
    }

    /**
     * @function isRetryableError - 判断错误是否可重试
     * @description 根据错误状态码和消息判断是否值得重试
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否可重试
     */
    isRetryableError(error) {
        const status = error.response?.status;
        const message = error.message?.toLowerCase() || '';
        
        // 不可重试的错误类型
        if (status === 403 || status === 400 || status === 422) {
            return false; // 权限错误、请求错误、验证错误
        }
        
        if (message.includes('permission') || message.includes('forbidden')) {
            return false; // 权限相关错误
        }
        
        // 可重试的错误类型
        if (status === 401 || status === 404 || status >= 500 || !status) {
            return true; // 认证错误、未找到、服务器错误、网络错误
        }
        
        if (message.includes('timeout') || message.includes('network') || 
            message.includes('connection') || message.includes('not found')) {
            return true; // 网络相关错误
        }
        
        // 默认可重试
        return true;
    }

    /**
     * @function attemptRecovery - 错误恢复
     * @description 对可重试错误进行统一的重试处理
     * @param {object} errorAnalysis - 错误分析结果
     * @param {object} orderData - 原始订单数据
     * @returns {Promise<object>} 恢复结果
     */
    async attemptRecovery(errorAnalysis, orderData) {
        if (!errorAnalysis.isRetryable) {
            return {
                success: false,
                reason: '错误不可重试',
                errorType: errorAnalysis.errorType,
                finalError: errorAnalysis.originalError
            };
        }

        logger.info('Resilience', '开始重试操作', {
            maxAttempts: this.retryConfig.maxAttempts
        });

        try {
            // 统一的重试机制
            const result = await this.retryWithBackoff(orderData);
            
            if (result.success) {
                logger.success('Resilience', '重试成功');
                return {
                    success: true,
                    result: result.data,
                    attempts: result.attempts
                };
            } else {
                logger.warn('Resilience', '重试失败', {
                    attempts: result.attempts,
                    finalError: result.error?.message
                });
                return {
                    success: false,
                    reason: '重试次数耗尽',
                    attempts: result.attempts,
                    finalError: result.error
                };
            }
        } catch (error) {
            logger.error('Resilience', '重试过程异常', error);
            return {
                success: false,
                reason: '重试过程异常: ' + error.message,
                finalError: error
            };
        }
    }

    /**
     * @function retryWithBackoff - 带退避策略的重试
     * @description 使用指数退避策略进行重试
     * @param {object} orderData - 订单数据
     * @returns {Promise<object>} 重试结果
     */
    async retryWithBackoff(orderData) {
        let lastError = null;
        
        for (let attempt = 1; attempt <= this.retryConfig.maxAttempts; attempt++) {
            try {
                // 在重试前尝试刷新数据（针对认证和数据过期问题）
                if (attempt > 1) {
                    await this.refreshDataIfNeeded(lastError);
                }
                
                // 执行API调用
                const result = await this.apiService.createOrder(orderData);
                
                return {
                    success: true,
                    data: result,
                    attempts: attempt
                };
                
            } catch (error) {
                lastError = error;
                
                logger.warn('Resilience', `重试第${attempt}次失败`, {
                    error: error.message,
                    status: error.response?.status
                });
                
                // 如果不是最后一次尝试，等待后继续
                if (attempt < this.retryConfig.maxAttempts) {
                    const delay = this.calculateBackoffDelay(attempt);
                    logger.debug('Resilience', `等待${delay}ms后进行下次重试`);
                    await this.delay(delay);
                }
            }
        }
        
        return {
            success: false,
            error: lastError,
            attempts: this.retryConfig.maxAttempts
        };
    }

    /**
     * @function refreshDataIfNeeded - 根据需要刷新数据
     * @description 根据错误类型决定是否需要刷新数据
     * @param {Error} error - 错误对象
     */
    async refreshDataIfNeeded(error) {
        const status = error.response?.status;
        const message = error.message?.toLowerCase() || '';
        
        try {
            // 认证错误：尝试验证token
            if (status === 401 || message.includes('unauthorized')) {
                logger.info('Resilience', '检测到认证错误，验证token');
                const isValid = await this.apiService.validateToken();
                if (!isValid) {
                    logger.warn('Resilience', 'Token无效，需要重新登录');
                    // 这里可以触发重新登录流程
                }
            }
            
            // 数据不一致错误：刷新系统数据
            if (status === 404 || message.includes('not found') || 
                message.includes('invalid id')) {
                logger.info('Resilience', '检测到数据不一致，刷新系统数据');
                await this.refreshSystemData();
            }
            
        } catch (refreshError) {
            logger.warn('Resilience', '数据刷新失败', refreshError);
        }
    }

    /**
     * @function calculateBackoffDelay - 计算退避延迟
     * @description 计算指数退避延迟时间
     * @param {number} attempt - 当前尝试次数
     * @returns {number} 延迟时间（毫秒）
     */
    calculateBackoffDelay(attempt) {
        const baseDelay = this.retryConfig.baseDelay || 1000;
        const maxDelay = this.retryConfig.maxDelay || 10000;
        
        // 指数退避：baseDelay * 2^(attempt-1)
        const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
        
        // 添加随机抖动（±25%）
        const jitter = delay * 0.25 * (Math.random() - 0.5);
        
        return Math.round(delay + jitter);
    }

    /**
     * @function delay - 延迟函数
     * @description 异步延迟指定时间
     * @param {number} ms - 延迟时间（毫秒）
     * @returns {Promise} 延迟Promise
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // ==================== 统一接口方法 ====================

    /**
     * @function formatErrorMessage - 格式化错误消息
     * @description 格式化错误消息，包含恢复信息
     * @param {Error} error - 错误对象
     * @param {object} recoveryResult - 恢复结果
     * @returns {string} 格式化的错误消息
     */
    formatErrorMessage(error, recoveryResult = null) {
        const baseMessage = error.message || '未知错误';
        
        if (!recoveryResult) {
            return baseMessage;
        }
        
        if (recoveryResult.success) {
            return `${baseMessage}（已自动恢复，重试${recoveryResult.attempts}次后成功）`;
        } else {
            return `${baseMessage}（自动恢复失败：${recoveryResult.reason}）`;
        }
    }

    /**
     * @function getValidationStatus - 获取验证状态
     * @description 获取当前数据验证状态
     * @returns {object} 验证状态信息
     */
    getValidationStatus() {
        return {
            lastValidationTime: this.lastValidationTime,
            validationInterval: this.validationInterval,
            cacheExpiry: this.cacheExpiry,
            nextValidationTime: this.lastValidationTime + this.validationInterval,
            isValidationDue: Date.now() - this.lastValidationTime > this.validationInterval
        };
    }

    /**
     * @function getErrorMessage - 获取错误消息
     * @description 根据错误类型获取用户友好的错误消息
     * @param {string} errorType - 错误类型
     * @returns {string} 错误消息
     */
    getErrorMessage(errorType) {
        const errorMessages = SYSTEM_CONFIG.ERROR_HANDLING.ERROR_MESSAGES;
        return errorMessages[errorType] || errorMessages.UNKNOWN_ERROR;
    }

    /**
     * @function getResilienceStats - 获取弹性管理统计
     * @description 获取弹性管理的统计信息（包含五维数据统计）
     * @returns {object} 统计信息
     */
    getResilienceStats() {
        return {
            validation: this.getValidationStatus(),
            retry: {
                maxAttempts: this.retryConfig.maxAttempts,
                baseDelay: this.retryConfig.baseDelay,
                maxDelay: this.retryConfig.maxDelay
            },
            cache: {
                expiry: this.cacheExpiry,
                userCacheSize: this.appState.userDataCache.size
            },
            dataLoading: {
                strategy: this.getDataLoadingStrategy(),
                stats: this.loadingStats,
                fallbackMode: this.dataLoadingConfig.fallbackMode,
                coreDataTypes: this.dataLoadingConfig.coreDataTypes,
                enhancedDataTypes: this.dataLoadingConfig.enhancedDataTypes
            },
            dataIntegrity: {
                coreData: {
                    backendUsers: this.appState.backendUsers?.length || 0,
                    subCategories: this.appState.subCategories?.length || 0,
                    carTypes: this.appState.carTypes?.length || 0
                },
                enhancedData: {
                    drivingRegions: this.appState.drivingRegions?.length || 0,
                    languages: this.appState.languages?.length || 0
                }
            }
        };
    }
}

// 导出类
window.ResilienceManager = ResilienceManager;

logger.info('模块', 'ResilienceManager统一弹性管理器加载完成', { 
    version: 'v4.2.0',
    features: ['数据一致性验证', '错误分析', '自动恢复', '重试机制', '五维数据支持', '分层加载策略', '性能优化']
}); 