<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire API 测试工具 - 精简版</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1400px; margin: 0 auto; background: white;
            border-radius: 12px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 20px; text-align: center; border-radius: 12px 12px 0 0;
        }
        .header h1 { margin: 0; font-size: 2em; font-weight: 300; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .main-content { padding: 20px; }
        
        /* 核心组件样式 */
        .section {
            background: #f8f9fa; margin-bottom: 20px; border-radius: 8px;
            border: 1px solid #e9ecef; overflow: hidden;
        }
        .section-header {
            background: #007bff; color: white; padding: 12px 20px;
            font-weight: 600; font-size: 1.1em;
        }
        .section-body { padding: 20px; }
        
        /* 表单样式 */
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 500; }
        .form-group input, .form-group select { 
            width: 100%; padding: 10px; border: 1px solid #ddd; 
            border-radius: 4px; font-size: 14px;
        }
        .form-row { display: flex; gap: 15px; flex-wrap: wrap; }
        .form-row .form-group { flex: 1; min-width: 200px; }
        
        /* 按钮样式 */
        .btn {
            padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;
            font-size: 14px; font-weight: 500; transition: all 0.3s ease; margin: 4px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-info:hover { background: #138496; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
        
        /* 账号选择器 */
        .account-selector {
            background: linear-gradient(135deg, #e7f3ff 0%, #f0f8ff 100%);
            border: 2px solid #b3d9ff; border-radius: 8px; padding: 15px; margin-bottom: 20px;
        }
        .account-selector h4 { margin: 0 0 10px 0; color: #0056b3; }
        .quick-buttons { display: flex; gap: 8px; flex-wrap: wrap; margin-top: 10px; }
        .quick-buttons .btn { flex: 1; min-width: 120px; }
        
        /* 后台用户选择器 */
        .backend-user-selector {
            background: linear-gradient(135deg, #f0f8ff 0%, #e8f5e8 100%);
            border: 2px solid #b3d9ff; border-radius: 8px; padding: 15px; margin-bottom: 20px;
            display: none;
        }
        .backend-user-selector h4 { margin: 0 0 10px 0; color: #0056b3; }
        .user-info { 
            background: white; border: 1px solid #dee2e6; border-radius: 4px; 
            padding: 10px; margin-bottom: 10px; font-size: 0.9em;
        }
        
        /* 测试配置面板 */
        .test-config-panel {
            background: linear-gradient(135deg, #fff3cd 0%, #f8f9fa 100%);
            border: 2px solid #ffc107; border-radius: 8px; padding: 15px; margin-bottom: 20px;
        }
        .test-config-panel h4 { margin: 0 0 15px 0; color: #856404; }
        .config-row { display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 15px; }
        .config-row .form-group { flex: 1; min-width: 150px; }
        
        /* 测试卡片 */
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
        .test-card {
            background: white; border: 1px solid #e9ecef; border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;
        }
        .test-card-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #e9ecef; }
        .test-title { margin: 0; color: #495057; font-size: 1.1em; }
        .test-type { 
            display: inline-block; background: #007bff; color: white; 
            padding: 2px 8px; border-radius: 12px; font-size: 0.8em; margin-top: 5px;
        }
        .test-card-body { padding: 15px; }
        
        /* 测试结果 */
        .test-result { margin-top: 15px; padding: 10px; border-radius: 4px; border-left: 4px solid #6c757d; }
        .result-success { background: #d4edda; border-left-color: #28a745; }
        .result-error { background: #f8d7da; border-left-color: #dc3545; }
        .result-pending { background: #fff3cd; border-left-color: #ffc107; }
        .result-info { background: #d1ecf1; border-left-color: #17a2b8; }
        .result-warning { background: #fff3cd; border-left-color: #ffc107; }

        /* 诊断界面样式 */
        .diagnostic-section {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
        }

        .diagnostic-section h5 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .diagnostic-item {
            padding: 8px;
            border-radius: 3px;
            margin: 5px 0;
        }

        .diagnostic-item.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .diagnostic-item.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .diagnostic-item.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .diagnostic-item.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .diagnostic-progress {
            background: #e9ecef;
            border-radius: 4px;
            padding: 8px;
            margin: 10px 0;
            font-weight: bold;
            text-align: center;
        }

        .fix-suggestions {
            margin: 10px 0;
        }

        .fix-suggestion {
            margin: 8px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid;
        }

        .fix-suggestion.urgent {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        .fix-suggestion.medium {
            border-left-color: #ffc107;
            background: #fff3cd;
            color: #856404;
        }

        .fix-suggestion.low {
            border-left-color: #17a2b8;
            background: #d1ecf1;
            color: #0c5460;
        }

        .diagnostic-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        /* 状态指示器 */
        .auth-status {
            display: inline-block; padding: 4px 8px; border-radius: 4px;
            font-size: 0.9em; font-weight: 500;
        }
        .auth-success { background: #d4edda; color: #155724; }
        .auth-pending { background: #fff3cd; color: #856404; }
        .auth-failed { background: #f8d7da; color: #721c24; }
        .auth-warning { background: #ffeaa7; color: #d63031; }
        
        /* 统计信息 */
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 15px; border-radius: 8px; text-align: center;
        }
        .stat-number { font-size: 1.8em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 0.8em; opacity: 0.9; }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container { margin: 10px; }
            .main-content { padding: 15px; }
            .form-row, .config-row { flex-direction: column; }
            .test-grid { grid-template-columns: 1fr; }
            .quick-buttons { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 GoMyHire API 完整测试系统</h1>
            <p>集成版 - API数据获取与降级机制 + 订单测试 + 数据验证</p>
            <div style="background: #e8f5e8; padding: 8px; border-radius: 4px; margin-top: 10px; font-size: 0.9em;">
                📅 <strong>日期格式说明:</strong> 系统现在使用 <strong>DD-MM-YYYY</strong> 格式（日-月-年）提交订单数据，确保与 GoMyHire API 兼容
            </div>
        </div>
        
        <div class="main-content">
            <!-- 邮箱账号选择器 -->
            <div class="account-selector">
                <h4>📧 邮箱账号选择</h4>
                <select id="accountSelector" onchange="switchAccount()">
                    <option value="">选择邮箱账号...</option>
                </select>
                <div class="quick-buttons">
                    <button type="button" class="btn btn-primary" onclick="switchToAccount('general')">
                        📧 <EMAIL>
                    </button>
                    <button type="button" class="btn btn-success" onclick="switchToAccount('jcy')">
                        👤 <EMAIL>
                    </button>
                    <button type="button" class="btn btn-info" onclick="switchToAccount('skymirror')">
                        🌟 <EMAIL>
                    </button>
                    <button type="button" class="btn btn-warning" onclick="testAllAccounts()">
                        🧪 测试所有邮箱
                    </button>
                </div>
                <div class="user-info">
                    <span id="authStatus" class="auth-status auth-pending">正在初始化...</span>
                    <div id="currentAccountInfo" style="margin-top: 5px; font-size: 0.9em;">未选择账号</div>
                </div>
            </div>

            <!-- 后台用户选择器 -->
            <div id="backendUserSelector" class="backend-user-selector">
                <h4>👤 后台用户选择</h4>
                <div id="backendUserInfo" class="user-info">正在加载后台用户列表...</div>
                <div class="user-info">
                    <span id="backendUsersStatus" class="auth-status auth-pending">未初始化</span>
                </div>
                <select id="backendUserSelect" onchange="selectBackendUser()">
                    <option value="">选择后台用户...</option>
                </select>
                <div id="selectedBackendUserInfo" class="user-info">未选择后台用户</div>
            </div>

            <!-- 测试配置面板 -->
            <div class="test-config-panel">
                <h4>⚙️ 测试配置</h4>

                <!-- 数据状态指示器 -->
                <div class="config-row">
                    <div class="form-group">
                        <label>🚗 车型数据</label>
                        <span id="carTypesStatus" class="auth-status auth-pending">未初始化</span>
                    </div>
                    <div class="form-group">
                        <label>📋 子分类数据</label>
                        <span id="subCategoriesStatus" class="auth-status auth-pending">未初始化</span>
                    </div>
                    <div class="form-group">
                        <label>🌍 地区数据</label>
                        <span id="drivingRegionsStatus" class="auth-status auth-pending">未初始化</span>
                    </div>
                    <div class="form-group">
                        <label>🗣️ 语言数据</label>
                        <span id="languagesStatus" class="auth-status auth-pending">未初始化</span>
                    </div>
                </div>

                <div class="config-row">
                    <div class="form-group">
                        <label for="testCount">测试条数</label>
                        <select id="testCount">
                            <option value="5">5条测试</option>
                            <option value="10" selected>10条测试</option>
                            <option value="15">15条测试</option>
                            <option value="20">20条测试</option>
                            <option value="30">30条测试</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="orderType">订单类型</label>
                        <select id="orderType">
                            <option value="mixed" selected>混合类型</option>
                            <option value="pickup">仅接机</option>
                            <option value="dropoff">仅送机</option>
                            <option value="charter">仅包车</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="regionType">地区选择</label>
                        <select id="regionType">
                            <option value="mixed" selected>混合地区</option>
                            <option value="kl">吉隆坡</option>
                            <option value="penang">槟城</option>
                            <option value="johor">柔佛</option>
                            <option value="sabah">沙巴</option>
                        </select>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <button type="button" class="btn btn-success" onclick="generateAndRunTests()">🚀 生成并运行测试</button>
                    <button type="button" class="btn btn-warning" onclick="clearAllResults()">🧹 清除结果</button>
                    <button type="button" class="btn btn-primary" onclick="previewTestCases()">👁️ 预览测试用例</button>
                    <button type="button" class="btn btn-info" onclick="refreshAllData()">🔄 刷新数据</button>
                </div>

                <!-- 数据管理器测试控制 -->
                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                    <h5 style="margin-bottom: 10px; color: #495057;">🔧 数据管理器测试</h5>
                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <button type="button" class="btn btn-primary" onclick="testAuthentication()">🔐 测试认证</button>
                        <button type="button" class="btn btn-success" onclick="testDataLoading()">📊 测试数据加载</button>
                        <button type="button" class="btn btn-warning" onclick="testFallbackMechanism()">📦 测试降级机制</button>
                        <button type="button" class="btn btn-info" onclick="testConcurrentLoading()">⚡ 测试并发加载</button>
                        <button type="button" class="btn btn-danger" onclick="simulateAPIFailure()">❌ 模拟API失败</button>
                        <button type="button" class="btn btn-info" onclick="debugLastAPICall()">🔍 调试最后API调用</button>
                        <button type="button" class="btn btn-warning" onclick="diagnose500Error()">🚨 500错误专项诊断</button>
                    </div>
                </div>
            </div>

            <!-- 手动输入订单测试模块 -->
            <div class="section">
                <div class="section-header">
                    ✏️ 手动输入订单测试
                </div>
                <div class="section-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="manualOrderType">订单类型</label>
                            <select id="manualOrderType" onchange="updateManualSubCategory()">
                                <option value="pickup">接机服务</option>
                                <option value="dropoff">送机服务</option>
                                <option value="charter">包车服务</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="manualCarType">车型选择</label>
                            <select id="manualCarType">
                                <option value="1">Comfort 5 Seater</option>
                                <option value="5">Economy 5 Seater</option>
                                <option value="15">Economy 7 Seater</option>
                                <option value="20">Comfort 7 Seater</option>
                                <option value="25">Mini Bus 14 Seater</option>
                                <option value="26">Bus 20+ Seater</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="manualRegion">地区选择</label>
                            <select id="manualRegion">
                                <option value="1">吉隆坡/雪兰莪</option>
                                <option value="2">槟城</option>
                                <option value="3">柔佛</option>
                                <option value="4">沙巴</option>
                                <option value="12">马六甲</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="manualCustomerName">客户姓名</label>
                            <input type="text" id="manualCustomerName" placeholder="请输入客户姓名" value="测试客户">
                        </div>
                        <div class="form-group">
                            <label for="manualCustomerContact">客户电话</label>
                            <input type="text" id="manualCustomerContact" placeholder="+***********" value="+***********">
                        </div>
                        <div class="form-group">
                            <label for="manualCustomerEmail">客户邮箱</label>
                            <input type="email" id="manualCustomerEmail" placeholder="<EMAIL>" value="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="manualPickup">接机地址</label>
                            <input type="text" id="manualPickup" placeholder="请输入接机地址" value="Kuala Lumpur International Airport (KLIA)">
                        </div>
                        <div class="form-group">
                            <label for="manualDestination">目的地址</label>
                            <input type="text" id="manualDestination" placeholder="请输入目的地址" value="KLCC - Kuala Lumpur City Centre">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="manualDate">服务日期 <small style="color: #6c757d;">(提交时将转换为DD-MM-YYYY格式)</small></label>
                            <input type="date" id="manualDate">
                        </div>
                        <div class="form-group">
                            <label for="manualTime">服务时间</label>
                            <input type="time" id="manualTime" value="10:00">
                        </div>
                        <div class="form-group">
                            <label for="manualPassengers">乘客人数</label>
                            <input type="number" id="manualPassengers" min="1" max="50" value="2">
                        </div>
                        <div class="form-group">
                            <label for="manualLuggage">行李件数</label>
                            <input type="number" id="manualLuggage" min="0" max="100" value="2">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="manualReference">OTA参考号</label>
                        <input type="text" id="manualReference" placeholder="自动生成或手动输入" value="">
                    </div>

                    <div class="form-group">
                        <label for="manualRequirement">特殊要求</label>
                        <textarea id="manualRequirement" rows="3" placeholder="请输入特殊要求（可选）">TESTING - 手动输入测试订单，请勿处理</textarea>
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <button type="button" class="btn btn-info" onclick="previewManualOrder()">👁️ 预览订单数据</button>
                        <button type="button" class="btn btn-success" onclick="submitManualOrder()">🚀 提交测试订单</button>
                        <button type="button" class="btn btn-warning" onclick="resetManualForm()">🔄 重置表单</button>
                        <button type="button" class="btn btn-primary" onclick="forceReloadData()">🔄 重新加载数据</button>
                    </div>

                    <!-- 手动订单测试结果 -->
                    <div id="manualOrderResult" class="test-result" style="display: none; margin-top: 20px;"></div>
                </div>
            </div>

            <!-- 错误诊断面板 -->
            <div class="section">
                <div class="section-header">
                    🔍 错误诊断与测试分析
                </div>
                <div class="section-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="diagnosticLevel">诊断级别</label>
                            <select id="diagnosticLevel">
                                <option value="basic">基础诊断</option>
                                <option value="detailed" selected>详细诊断</option>
                                <option value="verbose">完整诊断</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="testComplexity">测试复杂度</label>
                            <select id="testComplexity">
                                <option value="simple" selected>简单测试</option>
                                <option value="progressive">渐进式测试</option>
                                <option value="comprehensive">全面测试</option>
                            </select>
                        </div>
                    </div>

                    <div style="margin-top: 15px; text-align: center;">
                        <button type="button" class="btn btn-info" onclick="runDiagnosticTest()">🔍 运行诊断测试</button>
                        <button type="button" class="btn btn-warning" onclick="validateAllData()">✅ 验证所有数据</button>
                        <button type="button" class="btn btn-primary" onclick="generateDiagnosticReport()">📊 生成诊断报告</button>
                        <button type="button" class="btn btn-danger" onclick="clearDiagnosticLogs()">🧹 清除日志</button>
                    </div>

                    <!-- 诊断结果显示 -->
                    <div id="diagnosticResults" class="test-result" style="display: none; margin-top: 20px;"></div>

                    <!-- API调用历史 -->
                    <div id="apiCallHistory" style="margin-top: 20px; display: none;">
                        <h4>📋 API调用历史</h4>
                        <div id="apiCallList" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px;"></div>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalTests">0</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successTests">0</div>
                    <div class="stat-label">成功数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failedTests">0</div>
                    <div class="stat-label">失败数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successRate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
            </div>

            <!-- 测试用例网格 -->
            <div id="orderTestGrid" class="test-grid">
                <!-- 动态生成测试卡片 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const API_BASE_URL = 'https://gomyhire.com.my/api';
        let authToken = null;
        let currentAccount = null;
        let availableBackendUsers = [];
        let selectedBackendUser = null;
        let availableCarTypes = []; // 存储从API获取的车型列表
        let availableSubCategories = []; // 存储从API获取的子分类列表
        let availableRegions = []; // 存储从API获取的地区列表
        let availableLanguages = []; // 存储从API获取的语言列表
        let orderTestStats = { total: 0, success: 0, failed: 0 };
        let generatedTestCases = [];
        let diagnosticLogs = []; // 存储诊断日志
        let apiCallHistory = []; // 存储API调用历史

        // 数据管理器 - 统一管理API数据获取与降级机制
        class DataManager {
            constructor() {
                this.dataCache = new Map();
                this.dataStatus = new Map();
                this.fallbackData = this.initializeFallbackData();
                this.loadingPromises = new Map();
            }

            /**
             * @function initializeFallbackData - 初始化降级数据
             * @description 基于memory-bank/api return id list.md的标准数据
             */
            initializeFallbackData() {
                return {
                    backendUsers: [
                        { id: 1, name: 'Super Admin', email: '' },
                        { id: 37, name: 'smw', email: '<EMAIL>' },
                        { id: 89, name: 'GMH Sabah', email: '<EMAIL>' },
                        { id: 310, name: 'Jcy', email: '<EMAIL>' },
                        { id: 311, name: 'opAnnie', email: '<EMAIL>' },
                        { id: 312, name: 'opVenus', email: '<EMAIL>' },
                        { id: 313, name: 'opEric', email: '' },
                        { id: 342, name: 'SMW Wendy', email: 'SMW <EMAIL>' },
                        { id: 343, name: 'SMW XiaoYu', email: 'SMW <EMAIL>' },
                        { id: 420, name: 'chongyoonlim', email: '<EMAIL>' },
                        { id: 421, name: 'josua', email: '<EMAIL>' },
                        { id: 428, name: 'Gomyhire Yong', email: '' },
                        { id: 533, name: 'xhs', email: '' },
                        { id: 622, name: 'CsBob', email: '' },
                        { id: 777, name: '空空', email: '空空@gomyhire.com' },
                        { id: 812, name: '淼淼', email: '' },
                        { id: 856, name: 'GMH Ashley', email: '' },
                        { id: 907, name: 'OP XINYIN', email: '' },
                        { id: 1043, name: 'Billy Yong close', email: '' },
                        { id: 1047, name: 'OP QiJun', email: '<EMAIL>' },
                        { id: 1181, name: 'Op Karen', email: '<EMAIL>' },
                        { id: 1201, name: 'KK Lucas', email: '⭕️<EMAIL>' },
                        { id: 1223, name: 'Chong admin', email: '<EMAIL>' },
                        { id: 1652, name: 'CSteam Swee Qing', email: 'Swee <EMAIL>' },
                        { id: 1832, name: 'GMH SG William', email: '' },
                        { id: 2050, name: 'agent victor', email: '' },
                        { id: 2085, name: 'CSteam Tze Ying', email: '' },
                        { id: 2141, name: 'SMW Nas', email: '' },
                        { id: 2142, name: 'SMW Wen', email: '' },
                        { id: 2248, name: 'GMH Shi Wei', email: '' },
                        { id: 2249, name: 'Skymirror jetty', email: 'Skymirror <EMAIL>' },
                        { id: 2340, name: 'GMH JingSoon', email: '' },
                        { id: 2358, name: 'GMH Zilok', email: '' },
                        { id: 2446, name: 'UCSI - Cheras', email: '<EMAIL>' },
                        { id: 2503, name: 'GMH Veron', email: '' }
                    ],
                    subCategories: [
                        { id: 2, name: 'Pickup', main_category: 'Airport Transfer', type: 'pickup' },
                        { id: 3, name: 'Dropoff', main_category: 'Airport Transfer', type: 'dropoff' },
                        { id: 4, name: 'Charter', main_category: 'Charter Service', type: 'charter' },
                        { id: 5, name: 'Paging', main_category: 'Special Service', type: 'paging' },
                        { id: 6, name: 'SIM Card Only', main_category: 'Special Service', type: 'sim' },
                        { id: 7, name: 'Paging + Sim', main_category: 'Special Service', type: 'paging_sim' },
                        { id: 25, name: '包车（商铺）', main_category: 'Charter Service', type: 'charter_business' }
                    ],
                    carTypes: [
                        { id: 38, name: '4 Seater Hatchback (3 passenger, 2 x L size luggage)', type: '4 Seater Hatchback', seat_number: 4, priority: 1 },
                        { id: 5, name: '5 Seater (3 passenger, 3 x L size luggage)', type: '5 Seater', seat_number: 5, priority: 2 },
                        { id: 33, name: 'Premium 5 Seater (Mercedes/BMW Only) (3 passenger, 3 x L size luggage)', type: 'Premium 5 Seater', seat_number: 5, priority: 3 },
                        { id: 37, name: 'Extended 5 (4 passenger, 4 x L size luggage)', type: 'Extended 5', seat_number: 5, priority: 4 },
                        { id: 35, name: '7 Seater SUV (4 passenger, 4 x L size luggage)', type: '7 Seater SUV', seat_number: 7, priority: 5 },
                        { id: 15, name: '7 Seater MPV (5 passenger, 4 x L size luggage)', type: '7 Seater MPV', seat_number: 7, priority: 6 },
                        { id: 16, name: 'Standard Size MPV (5 passenger, 4 x L size luggage)', type: 'Standard Size MPV', seat_number: 7, priority: 7 },
                        { id: 31, name: 'Luxury Mpv (Serena) (5 passenger, 4 x L size luggage)', type: 'Luxury Mpv', seat_number: 7, priority: 8 },
                        { id: 32, name: 'Velfire/ Alphard (6 passenger, 4 x L size luggage)', type: 'Velfire/ Alphard', seat_number: 8, priority: 9 },
                        { id: 36, name: 'Alphard (6 passenger, 4 x L size luggage)', type: 'Alphard', seat_number: 8, priority: 10 },
                        { id: 20, name: '10 Seater MPV / Van (7 passenger, 7 x L size luggage)', type: '10 Seater MPV', seat_number: 10, priority: 11 },
                        { id: 30, name: '12 seat Starex (7 passenger, 7 x L size luggage)', type: '12 seat Starex', seat_number: 12, priority: 12 },
                        { id: 23, name: '14 Seater Van (10 passenger, 10 x L size luggage)', type: '14 Seater Van', seat_number: 14, priority: 13 },
                        { id: 24, name: '18 Seater Van (12 passenger, 12 x L size luggage)', type: '18 Seater Van', seat_number: 18, priority: 14 },
                        { id: 25, name: '30 Seat Mni Bus (29 passenger, 29 x L size luggage)', type: '30 Seat Mini Bus', seat_number: 30, priority: 15 },
                        { id: 26, name: '44 Seater Bus (43 passenger, 43 x L size luggage)', type: '44 Seater Bus', seat_number: 44, priority: 16 },
                        { id: 34, name: 'Ticket (N/A passenger, N/A luggage)', type: 'Ticket', seat_number: 0, priority: 17 },
                        { id: 39, name: 'Ticket (Non-Malaysian) (N/A passenger, N/A luggage)', type: 'Ticket (Non-Malaysian)', seat_number: 0, priority: 18 }
                    ],
                    drivingRegions: [
                        { id: 1, name: 'Kl/selangor (KL)', code: 'KL', active: true },
                        { id: 2, name: 'Penang (PNG)', code: 'PNG', active: true },
                        { id: 3, name: 'Johor (JB)', code: 'JB', active: true },
                        { id: 4, name: 'Sabah (SBH)', code: 'SBH', active: true },
                        { id: 5, name: 'Singapore (SG)', code: 'SG', active: true },
                        { id: 6, name: '携程专车 (CTRIP)', code: 'CTRIP', active: true },
                        { id: 8, name: 'Complete (COMPLETE)', code: 'COMPLETE', active: true },
                        { id: 9, name: 'Paging (PG)', code: 'PG', active: true },
                        { id: 10, name: 'Charter (CHRT)', code: 'CHRT', active: true },
                        { id: 12, name: 'Malacca (MLK)', code: 'MLK', active: true },
                        { id: 13, name: 'SMW (SMW)', code: 'SMW', active: true }
                    ],
                    languages: [
                        { id: 2, name: 'English (EN)', code: 'EN', active: true },
                        { id: 3, name: 'Malay (MY)', code: 'MY', active: true },
                        { id: 4, name: 'Chinese (CN)', code: 'CN', active: true },
                        { id: 5, name: 'Paging (PG)', code: 'PG', active: true },
                        { id: 6, name: 'Charter (CHARTER)', code: 'CHARTER', active: true },
                        { id: 8, name: '携程司导 (IM)', code: 'IM', active: true },
                        { id: 9, name: 'PSV (PSV)', code: 'PSV', active: true },
                        { id: 10, name: 'EVP (EVP)', code: 'EVP', active: true },
                        { id: 11, name: 'Car Type Reverify (CAR)', code: 'CAR', active: true },
                        { id: 12, name: 'Jetty (JETTY)', code: 'JETTY', active: true },
                        { id: 13, name: 'PhotoSkill Proof (PHOTO)', code: 'PHOTO', active: true }
                    ]
                };
            }

            /**
             * @function setDataStatus - 设置数据状态
             * @param {string} dataType - 数据类型
             * @param {string} status - 状态 (loading/api_success/api_failed/fallback)
             * @param {string} source - 数据来源 (api/fallback)
             * @param {string} message - 状态消息
             */
            setDataStatus(dataType, status, source = 'unknown', message = '') {
                this.dataStatus.set(dataType, {
                    status,
                    source,
                    message,
                    timestamp: new Date().toISOString()
                });
                this.updateDataStatusUI(dataType);
            }

            /**
             * @function getDataStatus - 获取数据状态
             * @param {string} dataType - 数据类型
             * @returns {Object} 数据状态信息
             */
            getDataStatus(dataType) {
                return this.dataStatus.get(dataType) || {
                    status: 'unknown',
                    source: 'unknown',
                    message: '未初始化',
                    timestamp: null
                };
            }

            /**
             * @function updateDataStatusUI - 更新数据状态UI显示
             * @param {string} dataType - 数据类型
             */
            updateDataStatusUI(dataType) {
                const status = this.getDataStatus(dataType);
                const statusElement = document.getElementById(`${dataType}Status`);

                if (statusElement) {
                    let statusText = '';
                    let statusClass = '';

                    switch (status.status) {
                        case 'loading':
                            statusText = '🔄 加载中...';
                            statusClass = 'auth-pending';
                            break;
                        case 'api_success':
                            statusText = `✅ API数据 (${status.source})`;
                            statusClass = 'auth-success';
                            break;
                        case 'api_failed':
                            statusText = `⚠️ API失败，使用降级数据`;
                            statusClass = 'auth-failed';
                            break;
                        case 'fallback':
                            statusText = `📦 降级数据 (${status.source})`;
                            statusClass = 'auth-warning';
                            break;
                        default:
                            statusText = '❓ 未知状态';
                            statusClass = 'auth-pending';
                    }

                    statusElement.textContent = statusText;
                    statusElement.className = `auth-status ${statusClass}`;
                    statusElement.title = `${status.message} (${status.timestamp})`;
                }
            }

            /**
             * @function loadDataWithFallback - 统一数据加载方法（带降级机制）
             * @param {string} dataType - 数据类型 (backendUsers/carTypes/subCategories/drivingRegions/languages)
             * @param {string} apiEndpoint - API端点
             * @param {Function} validator - 数据验证函数
             * @returns {Promise<Array>} 数据数组
             */
            async loadDataWithFallback(dataType, apiEndpoint, validator = null) {
                // 检查是否已有加载中的Promise，避免重复请求
                if (this.loadingPromises.has(dataType)) {
                    console.log(`📋 ${dataType} 正在加载中，等待现有请求完成...`);
                    return await this.loadingPromises.get(dataType);
                }

                // 设置加载状态
                this.setDataStatus(dataType, 'loading', 'api', '正在从API获取数据...');

                const loadingPromise = this._performDataLoad(dataType, apiEndpoint, validator);
                this.loadingPromises.set(dataType, loadingPromise);

                try {
                    const result = await loadingPromise;
                    return result;
                } finally {
                    this.loadingPromises.delete(dataType);
                }
            }

            /**
             * @function _performDataLoad - 执行实际的数据加载
             * @private
             */
            async _performDataLoad(dataType, apiEndpoint, validator) {
                try {
                    // 尝试从API获取数据
                    if (authToken) {
                        const apiData = await this._fetchFromAPI(apiEndpoint);

                        if (apiData && Array.isArray(apiData) && apiData.length > 0) {
                            // 验证数据格式
                            const validatedData = validator ? validator(apiData) : apiData;

                            // 缓存API数据
                            this.dataCache.set(dataType, validatedData);
                            this.setDataStatus(dataType, 'api_success', 'api', `成功获取 ${validatedData.length} 条记录`);

                            console.log(`✅ ${dataType}: 成功从API获取 ${validatedData.length} 条数据`);
                            return validatedData;
                        }
                    }

                    // API获取失败，使用降级数据
                    return this._useFallbackData(dataType, 'API数据获取失败或为空');

                } catch (error) {
                    console.warn(`⚠️ ${dataType} API调用失败:`, error.message);
                    return this._useFallbackData(dataType, `API错误: ${error.message}`);
                }
            }

            /**
             * @function _fetchFromAPI - 从API获取数据
             * @private
             */
            async _fetchFromAPI(endpoint) {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // 处理不同的API响应格式
                if (data.data && Array.isArray(data.data)) {
                    return data.data;
                } else if (Array.isArray(data)) {
                    return data;
                } else {
                    throw new Error('API响应格式不正确');
                }
            }

            /**
             * @function _useFallbackData - 使用降级数据
             * @private
             */
            _useFallbackData(dataType, reason) {
                const fallbackData = this.fallbackData[dataType] || [];
                this.dataCache.set(dataType, fallbackData);
                this.setDataStatus(dataType, 'fallback', 'memory-bank', `${reason}，使用降级数据 (${fallbackData.length} 条)`);

                console.log(`📦 ${dataType}: 使用降级数据，共 ${fallbackData.length} 条记录`);
                return fallbackData;
            }

            /**
             * @function loadAllSystemData - 并发加载所有系统数据
             * @returns {Promise<Object>} 所有数据的加载结果
             */
            async loadAllSystemData() {
                console.log('🚀 开始并发加载所有系统数据...');

                const loadTasks = [
                    { type: 'backendUsers', endpoint: '/backend_users', validator: this._validateBackendUsers },
                    { type: 'carTypes', endpoint: '/car_types', validator: this._validateCarTypes },
                    { type: 'subCategories', endpoint: '/sub_categories', validator: this._validateSubCategories },
                    { type: 'drivingRegions', endpoint: '/driving_regions', validator: this._validateDrivingRegions },
                    { type: 'languages', endpoint: '/languages', validator: this._validateLanguages }
                ];

                const results = {};

                // 并发执行所有加载任务
                const promises = loadTasks.map(async (task) => {
                    try {
                        const data = await this.loadDataWithFallback(task.type, task.endpoint, task.validator);
                        results[task.type] = { success: true, data, count: data.length };
                        return { type: task.type, success: true, data };
                    } catch (error) {
                        console.error(`❌ ${task.type} 加载失败:`, error);
                        results[task.type] = { success: false, error: error.message };
                        return { type: task.type, success: false, error };
                    }
                });

                await Promise.allSettled(promises);

                // 更新全局变量
                availableBackendUsers = this.dataCache.get('backendUsers') || [];
                availableCarTypes = this.dataCache.get('carTypes') || [];
                availableSubCategories = this.dataCache.get('subCategories') || [];
                availableRegions = this.dataCache.get('drivingRegions') || [];
                availableLanguages = this.dataCache.get('languages') || [];

                console.log('✅ 所有系统数据加载完成:', results);
                return results;
            }

            /**
             * @function _validateBackendUsers - 验证后台用户数据格式
             * @private
             */
            _validateBackendUsers(data) {
                return data.map(user => ({
                    id: parseInt(user.id),
                    name: user.name || user.username || `User ${user.id}`,
                    email: user.email || '',
                    role: user.role || 'user'
                }));
            }

            /**
             * @function _validateCarTypes - 验证车型数据格式
             * @private
             */
            _validateCarTypes(data) {
                return data.map(carType => ({
                    id: parseInt(carType.id),
                    name: carType.name || carType.type || `Car Type ${carType.id}`,
                    type: carType.type || carType.name || 'Unknown',
                    seat_number: parseInt(carType.seat_number) || 0,
                    priority: parseInt(carType.priority) || 999
                }));
            }

            /**
             * @function _validateSubCategories - 验证子分类数据格式
             * @private
             */
            _validateSubCategories(data) {
                return data.map(category => ({
                    id: parseInt(category.id),
                    name: category.name || `Category ${category.id}`,
                    main_category: category.main_category || 'General',
                    type: category.type || 'general'
                }));
            }

            /**
             * @function _validateDrivingRegions - 验证行驶区域数据格式
             * @private
             */
            _validateDrivingRegions(data) {
                return data.map(region => ({
                    id: parseInt(region.id),
                    name: region.name || `Region ${region.id}`,
                    code: region.code || region.name || `R${region.id}`,
                    active: region.active !== false
                }));
            }

            /**
             * @function _validateLanguages - 验证语言数据格式
             * @private
             */
            _validateLanguages(data) {
                return data.map(language => ({
                    id: parseInt(language.id),
                    name: language.name || `Language ${language.id}`,
                    code: language.code || language.name || `L${language.id}`,
                    active: language.active !== false
                }));
            }
        }

        // 创建全局数据管理器实例
        const dataManager = new DataManager();

        /**
         * @function refreshAllData - 刷新所有数据
         * @description 强制重新加载所有系统数据，清除缓存
         */
        async function refreshAllData() {
            console.log('🔄 开始刷新所有数据...');

            // 清除缓存
            dataManager.dataCache.clear();
            dataManager.loadingPromises.clear();

            // 重置状态显示
            ['backendUsers', 'carTypes', 'subCategories', 'drivingRegions', 'languages'].forEach(dataType => {
                dataManager.setDataStatus(dataType, 'loading', 'api', '正在刷新数据...');
            });

            try {
                if (authToken) {
                    // 使用数据管理器重新加载所有数据
                    const loadResults = await dataManager.loadAllSystemData();

                    // 重新加载后台用户
                    await backendUserManager.loadBackendUsersWithFallback();

                    // 更新UI组件
                    updateCarTypeSelectors();
                    updateRegionSelector();

                    console.log('✅ 数据刷新完成:', loadResults);
                    alert('✅ 所有数据已刷新完成！');
                } else {
                    // 无认证时使用降级数据
                    availableBackendUsers = dataManager.fallbackData.backendUsers;
                    availableCarTypes = dataManager.fallbackData.carTypes;
                    availableSubCategories = dataManager.fallbackData.subCategories;
                    availableRegions = dataManager.fallbackData.drivingRegions;
                    availableLanguages = dataManager.fallbackData.languages;

                    // 设置降级状态
                    ['backendUsers', 'carTypes', 'subCategories', 'drivingRegions', 'languages'].forEach(dataType => {
                        dataManager.setDataStatus(dataType, 'fallback', 'memory-bank', '无认证，使用降级数据');
                    });

                    // 更新UI
                    backendUserManager.updateBackendUserSelector();
                    backendUserManager.selectDefaultUser();
                    updateCarTypeSelectors();
                    updateRegionSelector();

                    console.log('📦 降级数据刷新完成');
                    alert('📦 已使用降级数据刷新！');
                }
            } catch (error) {
                console.error('❌ 数据刷新失败:', error);
                alert('❌ 数据刷新失败: ' + error.message);
            }
        }

        /**
         * @function validateAllData - 验证所有数据完整性
         * @description 检查所有数据的完整性和一致性
         */
        function validateAllData() {
            console.log('🔍 开始验证所有数据...');

            const validationResults = {
                backendUsers: validateDataIntegrity(availableBackendUsers, 'backendUsers'),
                carTypes: validateDataIntegrity(availableCarTypes, 'carTypes'),
                subCategories: validateDataIntegrity(availableSubCategories, 'subCategories'),
                drivingRegions: validateDataIntegrity(availableRegions, 'drivingRegions'),
                languages: validateDataIntegrity(availableLanguages, 'languages')
            };

            const report = generateValidationReport(validationResults);

            // 显示验证报告
            const diagnosticResults = document.getElementById('diagnosticResults');
            if (diagnosticResults) {
                diagnosticResults.style.display = 'block';
                diagnosticResults.className = 'test-result result-success';
                diagnosticResults.innerHTML = report;
            }

            console.log('✅ 数据验证完成:', validationResults);
        }

        /**
         * @function validateDataIntegrity - 验证单个数据类型的完整性
         * @private
         */
        function validateDataIntegrity(data, dataType) {
            const result = {
                isValid: true,
                count: data.length,
                issues: [],
                source: dataManager.getDataStatus(dataType).source
            };

            if (!Array.isArray(data)) {
                result.isValid = false;
                result.issues.push('数据不是数组格式');
                return result;
            }

            if (data.length === 0) {
                result.isValid = false;
                result.issues.push('数据为空');
                return result;
            }

            // 检查必需字段
            data.forEach((item, index) => {
                if (!item.id) {
                    result.issues.push(`第${index + 1}项缺少ID字段`);
                }
                if (!item.name) {
                    result.issues.push(`第${index + 1}项缺少name字段`);
                }
            });

            // 检查ID重复
            const ids = data.map(item => item.id).filter(id => id);
            const uniqueIds = [...new Set(ids)];
            if (ids.length !== uniqueIds.length) {
                result.issues.push('存在重复的ID');
            }

            if (result.issues.length > 0) {
                result.isValid = false;
            }

            return result;
        }

        /**
         * @function generateValidationReport - 生成验证报告
         * @private
         */
        function generateValidationReport(validationResults) {
            let report = '<h4>📊 数据验证报告</h4>';

            Object.entries(validationResults).forEach(([dataType, result]) => {
                const statusIcon = result.isValid ? '✅' : '❌';
                const statusText = result.isValid ? '正常' : '异常';

                report += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        <strong>${statusIcon} ${dataType}</strong> (${statusText})<br>
                        数据来源: ${result.source}<br>
                        记录数量: ${result.count}<br>
                        ${result.issues.length > 0 ? `问题: ${result.issues.join(', ')}` : '无问题'}
                    </div>
                `;
            });

            const totalIssues = Object.values(validationResults).reduce((sum, result) => sum + result.issues.length, 0);
            report += `<p><strong>总结:</strong> 发现 ${totalIssues} 个问题</p>`;

            return report;
        }

        // 邮箱账号配置
        const realLoginAccounts = [
            {
                id: 'general',
                email: '<EMAIL>',
                password: 'Gomyhire@123456',
                isDefault: true
            },
            {
                id: 'jcy',
                email: '<EMAIL>',
                password: 'Yap123',
                isDefault: false
            },
            {
                id: 'skymirror',
                email: '<EMAIL>',
                password: 'Sky@114788',
                isDefault: false
            }
        ];

        // 地址模板库 - 马来西亚真实地址
        const addressTemplates = [
            {
                id: 'airport_kl',
                name: '机场 ↔ 吉隆坡市中心',
                pickup: 'Kuala Lumpur International Airport (KLIA)',
                destination: 'KLCC - Kuala Lumpur City Centre',
                category: 'airport',
                region: 'kl'
            },
            {
                id: 'airport_klia2',
                name: 'KLIA2 ↔ 双子塔',
                pickup: 'KLIA2 Terminal',
                destination: 'Petronas Twin Towers KLCC',
                category: 'airport',
                region: 'kl'
            },
            {
                id: 'hotel_airport',
                name: '酒店 ↔ 机场',
                pickup: 'Hotel Sentral Kuala Lumpur',
                destination: 'KLIA Terminal 1',
                category: 'airport',
                region: 'kl'
            },
            {
                id: 'city_tour',
                name: '吉隆坡市区游',
                pickup: 'Merdeka Square (Independence Square)',
                destination: 'Batu Caves Temple',
                category: 'tour',
                region: 'kl'
            },
            {
                id: 'genting_trip',
                name: '云顶高原一日游',
                pickup: 'Kuala Lumpur City Center',
                destination: 'Genting Highlands Resort',
                category: 'tour',
                region: 'kl'
            },
            {
                id: 'penang_tour',
                name: '槟城历史游',
                pickup: 'Penang International Airport',
                destination: 'Georgetown Heritage Area',
                category: 'tour',
                region: 'penang'
            },
            {
                id: 'johor_sg',
                name: '柔佛新加坡跨境',
                pickup: 'Johor Bahru CIQ',
                destination: 'Changi Airport Singapore',
                category: 'airport',
                region: 'johor'
            },
            {
                id: 'sabah_tour',
                name: '沙巴当地游',
                pickup: 'Kota Kinabalu Airport',
                destination: 'Kota Kinabalu City Tour',
                category: 'tour',
                region: 'sabah'
            }
        ];

        // 客户信息模板
        const customerTemplates = {
            chinese: [
                { name: '张三', email: '<EMAIL>' },
                { name: '李四', email: '<EMAIL>' },
                { name: '王五', email: '<EMAIL>' },
                { name: '赵六', email: '<EMAIL>' },
                { name: '陈七', email: '<EMAIL>' }
            ],
            english: [
                { name: 'John Smith', email: '<EMAIL>' },
                { name: 'Mary Johnson', email: '<EMAIL>' },
                { name: 'David Brown', email: '<EMAIL>' },
                { name: 'Sarah Wilson', email: '<EMAIL>' },
                { name: 'Michael Davis', email: '<EMAIL>' }
            ],
            malay: [
                { name: 'Ahmad Abdullah', email: '<EMAIL>' },
                { name: 'Siti Nurhaliza', email: '<EMAIL>' },
                { name: 'Muhammad Ali', email: '<EMAIL>' },
                { name: 'Fatimah Zahra', email: '<EMAIL>' },
                { name: 'Hassan Ibrahim', email: '<EMAIL>' }
            ]
        };

        // 车型配置
        const carTypeConfig = {
            economy: [5, 15, 38], // 经济型
            comfort: [1, 20, 33], // 舒适型
            luxury: [32, 33, 36], // 豪华型
            minibus: [25, 26], // 小巴
            bus: [26] // 大巴
        };

        // 地区配置
        const regionConfig = {
            kl: { id: 1, name: '吉隆坡/雪兰莪' },
            penang: { id: 2, name: '槟城' },
            johor: { id: 3, name: '柔佛' },
            sabah: { id: 4, name: '沙巴' },
            melaka: { id: 12, name: '马六甲' }
        };

        // 工具函数
        function getRandomElement(array) {
            return array[Math.floor(Math.random() * array.length)];
        }

        /**
         * @function formatDateToDDMMYYYY - 将Date对象格式化为DD-MM-YYYY格式
         * @param {Date} date - 要格式化的日期对象
         * @returns {string} DD-MM-YYYY格式的日期字符串
         */
        function formatDateToDDMMYYYY(date) {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}-${month}-${year}`;
        }

        /**
         * @function formatDateToYYYYMMDD - 将Date对象格式化为YYYY-MM-DD格式（用于HTML date input）
         * @param {Date} date - 要格式化的日期对象
         * @returns {string} YYYY-MM-DD格式的日期字符串
         */
        function formatDateToYYYYMMDD(date) {
            return date.toISOString().split('T')[0];
        }

        /**
         * @function convertDDMMYYYYToYYYYMMDD - 将DD-MM-YYYY格式转换为YYYY-MM-DD格式
         * @param {string} ddmmyyyy - DD-MM-YYYY格式的日期字符串
         * @returns {string} YYYY-MM-DD格式的日期字符串
         */
        function convertDDMMYYYYToYYYYMMDD(ddmmyyyy) {
            if (!ddmmyyyy || typeof ddmmyyyy !== 'string') return '';
            const parts = ddmmyyyy.split('-');
            if (parts.length !== 3) return ddmmyyyy; // 如果格式不对，返回原值
            const [day, month, year] = parts;
            return `${year}-${month}-${day}`;
        }

        /**
         * @function convertYYYYMMDDToDDMMYYYY - 将YYYY-MM-DD格式转换为DD-MM-YYYY格式
         * @param {string} yyyymmdd - YYYY-MM-DD格式的日期字符串
         * @returns {string} DD-MM-YYYY格式的日期字符串
         */
        function convertYYYYMMDDToDDMMYYYY(yyyymmdd) {
            if (!yyyymmdd || typeof yyyymmdd !== 'string') return '';
            const parts = yyyymmdd.split('-');
            if (parts.length !== 3) return yyyymmdd; // 如果格式不对，返回原值
            const [year, month, day] = parts;
            return `${day}-${month}-${year}`;
        }

        function getRandomDate() {
            const start = new Date(2025, 4, 1); // 2025年5月1日
            const end = new Date(2025, 4, 31); // 2025年5月31日
            const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
            return formatDateToDDMMYYYY(date); // 改为DD-MM-YYYY格式
        }

        function getRandomTime() {
            const hours = Math.floor(Math.random() * 24);
            const minutes = Math.floor(Math.random() * 4) * 15; // 15分钟间隔
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }

        function getRandomPhone() {
            const prefixes = ['+60123', '+60198', '+60176', '+60187', '+60165'];
            const prefix = getRandomElement(prefixes);
            const number = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
            return prefix + number;
        }

        function getRandomPassengerCount() {
            const weights = [
                { count: 1, weight: 15 },
                { count: 2, weight: 25 },
                { count: 3, weight: 20 },
                { count: 4, weight: 15 },
                { count: 5, weight: 10 },
                { count: 6, weight: 8 },
                { count: 8, weight: 4 },
                { count: 10, weight: 2 },
                { count: 15, weight: 1 }
            ];

            const totalWeight = weights.reduce((sum, item) => sum + item.weight, 0);
            let random = Math.random() * totalWeight;

            for (const item of weights) {
                random -= item.weight;
                if (random <= 0) return item.count;
            }
            return 2; // 默认值
        }

        /**
         * @function getCarTypeForPassengers - 根据乘客人数智能选择车型
         * @param {number} passengerCount - 乘客人数
         * @returns {number} 车型ID
         * @description 根据乘客人数从可用车型中智能选择合适的车型
         */
        function getCarTypeForPassengers(passengerCount) {
            // 如果没有可用车型，使用备用逻辑
            if (availableCarTypes.length === 0) {
                if (passengerCount <= 4) return getRandomElement(carTypeConfig.economy);
                if (passengerCount <= 6) return getRandomElement(carTypeConfig.comfort);
                if (passengerCount <= 8) return getRandomElement(carTypeConfig.luxury);
                if (passengerCount <= 25) return getRandomElement(carTypeConfig.minibus);
                return getRandomElement(carTypeConfig.bus);
            }

            // 使用动态车型数据
            const suitableCarTypes = availableCarTypes.filter(carType => {
                const seatNumber = carType.seat_number || 5; // 默认5座
                return seatNumber >= passengerCount;
            });

            if (suitableCarTypes.length === 0) {
                // 如果没有合适的车型，选择最大的
                const maxSeatCarType = availableCarTypes.reduce((max, carType) => {
                    const maxSeats = max.seat_number || 0;
                    const currentSeats = carType.seat_number || 0;
                    return currentSeats > maxSeats ? carType : max;
                });
                return maxSeatCarType.id;
            }

            // 选择最小但足够的车型（更经济）
            const optimalCarType = suitableCarTypes.reduce((optimal, carType) => {
                const optimalSeats = optimal.seat_number || 999;
                const currentSeats = carType.seat_number || 999;
                return currentSeats < optimalSeats ? carType : optimal;
            });

            return optimalCarType.id;
        }

        // 动态测试用例生成器
        function generateTestCase(index, orderType, regionType) {
            const orderTypes = ['pickup', 'dropoff', 'charter'];
            const actualOrderType = orderType === 'mixed' ? getRandomElement(orderTypes) : orderType;

            // 选择地址模板
            let availableTemplates = addressTemplates;
            if (regionType !== 'mixed') {
                availableTemplates = addressTemplates.filter(t => t.region === regionType);
            }
            const template = getRandomElement(availableTemplates);

            // 选择客户信息
            const customerType = getRandomElement(['chinese', 'english', 'malay']);
            const customer = getRandomElement(customerTemplates[customerType]);

            // 生成订单数据
            const passengerCount = getRandomPassengerCount();
            const carTypeId = getCarTypeForPassengers(passengerCount);

            // 使用动态地区数据
            let regionId = regionConfig[template.region]?.id || 1;
            if (availableRegions.length > 0) {
                const region = availableRegions.find(r => r.name?.includes(regionConfig[template.region]?.name)) ||
                              availableRegions[0];
                regionId = region.id;
            }

            // 使用动态子分类数据
            let subCategoryId = getSubCategoryByType(actualOrderType);
            if (!subCategoryId) {
                // 回退到硬编码值
                subCategoryId = actualOrderType === 'pickup' ? 2 : actualOrderType === 'dropoff' ? 3 : 4;
            }

            // 使用动态语言数据
            const languageIds = getLanguagesByCustomerType(customerType);

            // 确定接送地址
            let pickup, destination;
            if (actualOrderType === 'pickup') {
                pickup = template.pickup;
                destination = template.destination;
            } else if (actualOrderType === 'dropoff') {
                pickup = template.destination;
                destination = template.pickup;
            } else {
                pickup = template.pickup;
                destination = template.destination;
            }

            const testCase = {
                name: `${actualOrderType === 'pickup' ? '接机' : actualOrderType === 'dropoff' ? '送机' : '包车'}服务 - ${template.name}`,
                type: actualOrderType,
                description: `${template.name} - ${passengerCount}人${customerType === 'chinese' ? '中文' : customerType === 'english' ? '英文' : '马来'}客户`,
                data: {
                    sub_category_id: subCategoryId,
                    car_type_id: carTypeId,
                    incharge_by_backend_user_id: selectedBackendUser?.id || 1,
                    ota_reference_number: `${actualOrderType.toUpperCase()}_${Date.now()}_${index}`,
                    customer_name: customer.name,
                    customer_contact: getRandomPhone(),
                    customer_email: customer.email,
                    pickup: pickup,
                    destination: destination,
                    date: getRandomDate(),
                    time: getRandomTime(),
                    passenger_number: passengerCount,
                    luggage_number: Math.min(passengerCount + Math.floor(Math.random() * 3), passengerCount * 2),
                    driving_region_id: regionId,
                    languages_id_array: languageIds,
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 动态生成测试用例'
                }
            };

            return testCase;
        }

        // 生成并运行测试
        async function generateAndRunTests() {
            const testCount = parseInt(document.getElementById('testCount').value);
            const orderType = document.getElementById('orderType').value;
            const regionType = document.getElementById('regionType').value;

            // 验证认证状态
            if (!authToken) {
                alert('请先选择邮箱账号进行认证');
                return;
            }

            // 生成测试用例
            generatedTestCases = [];
            for (let i = 0; i < testCount; i++) {
                generatedTestCases.push(generateTestCase(i + 1, orderType, regionType));
            }

            // 渲染测试卡片
            renderTestCards();

            // 重置统计
            orderTestStats = { total: 0, success: 0, failed: 0 };
            updateStats();

            // 运行测试
            for (let i = 0; i < generatedTestCases.length; i++) {
                await runSingleTest(i);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 1秒间隔
            }

            console.log('所有测试完成', orderTestStats);
        }

        // 预览测试用例
        function previewTestCases() {
            const testCount = parseInt(document.getElementById('testCount').value);
            const orderType = document.getElementById('orderType').value;
            const regionType = document.getElementById('regionType').value;

            // 生成测试用例
            generatedTestCases = [];
            for (let i = 0; i < testCount; i++) {
                generatedTestCases.push(generateTestCase(i + 1, orderType, regionType));
            }

            // 渲染测试卡片
            renderTestCards();

            alert(`已生成 ${testCount} 个测试用例，请查看下方卡片。点击"生成并运行测试"开始执行。`);
        }

        // 渲染测试卡片
        function renderTestCards() {
            const container = document.getElementById('orderTestGrid');
            container.innerHTML = '';

            generatedTestCases.forEach((testCase, index) => {
                const card = document.createElement('div');
                card.className = 'test-card';

                const typeColor = testCase.type === 'pickup' ? '#28a745' :
                                testCase.type === 'dropoff' ? '#007bff' : '#ffc107';

                card.innerHTML = `
                    <div class="test-card-header">
                        <h4 class="test-title">${testCase.name}</h4>
                        <span class="test-type" style="background: ${typeColor};">${testCase.type}</span>
                    </div>
                    <div class="test-card-body">
                        <p style="margin: 5px 0; color: #6c757d; font-size: 0.9em;">${testCase.description}</p>
                        <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin: 8px 0; font-size: 0.85em;">
                            <div style="color: #28a745;">📍 接机: ${testCase.data.pickup}</div>
                            <div style="color: #dc3545;">🎯 送达: ${testCase.data.destination}</div>
                            <div style="color: #007bff;">👥 乘客: ${testCase.data.passenger_number}人 | 🧳 行李: ${testCase.data.luggage_number}件</div>
                            <div style="color: #6c757d;">📅 ${testCase.data.date} ${testCase.data.time} (DD-MM-YYYY格式)</div>
                        </div>
                        <button class="btn btn-primary" onclick="runSingleTest(${index})">
                            测试此订单
                        </button>
                        <div id="testResult${index}" class="test-result" style="display: none;"></div>
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // 运行单个测试（增强版错误诊断）
        async function runSingleTest(index) {
            if (index < 0 || index >= generatedTestCases.length) {
                console.error('无效的测试索引:', index);
                return;
            }

            const testCase = generatedTestCases[index];
            const resultContainer = document.getElementById(`testResult${index}`);

            if (!resultContainer) {
                console.error('找不到结果容器:', `testResult${index}`);
                return;
            }

            resultContainer.style.display = 'block';
            resultContainer.className = 'test-result result-pending';
            resultContainer.innerHTML = '🔄 测试进行中...';

            let response = null;
            let responseText = '';
            let orderData = null;

            try {
                // 验证认证状态
                if (!authToken) {
                    throw new Error('未找到认证token，请先登录邮箱账号');
                }

                // 准备订单数据
                orderData = prepareOrderData(testCase.data);

                // 预验证订单数据
                const preValidation = analyzeRequestData(orderData);
                if (preValidation.issues.length > 0) {
                    console.warn('⚠️ 订单数据预验证发现问题:', preValidation.issues);
                }

                const startTime = Date.now();

                // 发送API请求
                response = await fetch(`${API_BASE_URL}/create_order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(orderData)
                });

                const responseTime = Date.now() - startTime;
                responseText = await response.text();

                // 记录API调用
                logApiCall('/create_order', orderData, response, responseText, responseTime);

                if (!response.ok) {
                    // 分析错误
                    const errorAnalysis = analyzeApiError(response, responseText, orderData);

                    // 记录诊断日志
                    const diagnosticEntry = {
                        timestamp: new Date().toISOString(),
                        testCase: testCase.name,
                        error: `HTTP ${response.status}: ${response.statusText}`,
                        analysis: errorAnalysis,
                        requestData: orderData,
                        responseText: responseText
                    };
                    diagnosticLogs.unshift(diagnosticEntry);

                    throw new Error(`HTTP ${response.status}: ${response.statusText}\n分析: ${errorAnalysis.possibleCauses.join(', ')}`);
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`无效的JSON响应: ${parseError.message}\n响应内容: ${responseText.substring(0, 200)}...`);
                }

                if (result.status === true || result.status === 'true') {
                    orderTestStats.success++;
                    orderTestStats.total++;

                    resultContainer.className = 'test-result result-success';
                    resultContainer.innerHTML = `
                        ✅ <strong>测试成功</strong><br>
                        订单ID: ${result.order_id || result.data?.order_id || 'N/A'}<br>
                        消息: ${result.message || '订单创建成功'}<br>
                        <small>响应时间: ${responseTime}ms</small>
                    `;
                } else {
                    // 业务逻辑错误
                    const errorAnalysis = {
                        errorType: 'business_logic_error',
                        possibleCauses: ['业务规则验证失败', '数据不符合业务要求'],
                        recommendations: ['检查业务规则', '验证数据完整性'],
                        serverMessage: result.message || result.error
                    };

                    const diagnosticEntry = {
                        timestamp: new Date().toISOString(),
                        testCase: testCase.name,
                        error: result.message || result.error || '订单创建失败',
                        analysis: errorAnalysis,
                        requestData: orderData,
                        responseData: result
                    };
                    diagnosticLogs.unshift(diagnosticEntry);

                    throw new Error(result.message || result.error || '订单创建失败');
                }

            } catch (error) {
                orderTestStats.failed++;
                orderTestStats.total++;

                // 增强的错误显示
                let errorDetails = error.message;
                if (response && orderData) {
                    const errorAnalysis = analyzeApiError(response, responseText, orderData);
                    errorDetails += `\n\n🔍 错误分析:\n• ${errorAnalysis.possibleCauses.join('\n• ')}`;
                    errorDetails += `\n\n💡 建议:\n• ${errorAnalysis.recommendations.join('\n• ')}`;
                }

                resultContainer.className = 'test-result result-error';
                resultContainer.innerHTML = `
                    ❌ <strong>测试失败</strong><br>
                    错误: ${error.message}<br>
                    <small>测试用例: ${testCase.name}</small><br>
                    <button onclick="showDetailedError(${index})" class="btn btn-sm" style="margin-top: 5px;">📋 详细错误</button>
                `;

                console.error(`订单测试失败: ${testCase.name}`, {
                    error: error.message,
                    orderData: orderData,
                    response: response ? {
                        status: response.status,
                        statusText: response.statusText,
                        headers: Object.fromEntries(response.headers.entries())
                    } : null,
                    responseText: responseText
                });
            }

            updateStats();
        }

        /**
         * @function prepareOrderData - 准备订单数据，智能匹配后台用户
         * @param {Object} originalData - 原始订单数据
         * @returns {Object} 处理后的订单数据
         * @description 根据当前登录邮箱智能选择后台用户ID，确保订单分配给正确的负责人
         */
        function prepareOrderData(originalData) {
            const orderData = JSON.parse(JSON.stringify(originalData));

            // 优先使用智能匹配的后台用户ID
            const smartUserId = getSmartBackendUserId();
            if (smartUserId) {
                // 验证智能匹配的用户是否在可用列表中
                const smartUser = availableBackendUsers.find(u => u.id === smartUserId);
                if (smartUser) {
                    orderData.incharge_by_backend_user_id = smartUserId;
                    console.log(`🎯 智能匹配后台用户: ${smartUser.name} (ID: ${smartUserId})`);
                } else {
                    // 智能匹配的用户不在可用列表中，使用当前选中的用户
                    orderData.incharge_by_backend_user_id = selectedBackendUser?.id || 1;
                    console.log(`📋 智能匹配用户不在可用列表中，使用当前选中用户: ID ${orderData.incharge_by_backend_user_id}`);
                }
            } else {
                // 使用当前选中的后台用户或默认值
                orderData.incharge_by_backend_user_id = selectedBackendUser?.id || 1;
                console.log(`📋 使用当前选中后台用户: ID ${orderData.incharge_by_backend_user_id}`);
            }

            return orderData;
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalTests').textContent = orderTestStats.total;
            document.getElementById('successTests').textContent = orderTestStats.success;
            document.getElementById('failedTests').textContent = orderTestStats.failed;

            const successRate = orderTestStats.total > 0 ?
                Math.round((orderTestStats.success / orderTestStats.total) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        // 清除所有测试结果
        function clearAllResults() {
            orderTestStats = { total: 0, success: 0, failed: 0 };
            generatedTestCases = [];
            document.getElementById('orderTestGrid').innerHTML = '';
            updateStats();
        }

        // 切换到指定账号
        async function switchToAccount(accountId) {
            const account = realLoginAccounts.find(acc => acc.id === accountId);
            if (!account) {
                console.error('账号不存在:', accountId);
                return;
            }

            // 更新选择器
            const selector = document.getElementById('accountSelector');
            if (selector) selector.value = accountId;

            // 清除之前的数据
            authToken = null;
            currentAccount = null;
            availableBackendUsers = [];
            selectedBackendUser = null;

            // 隐藏后台用户选择器
            const backendUserSelector = document.getElementById('backendUserSelector');
            if (backendUserSelector) backendUserSelector.style.display = 'none';

            // 开始认证
            updateAuthStatus(false, `正在切换到 ${account.email}...`);
            updateCurrentAccountInfo('切换中...');

            const success = await authenticateAccount(account);
            if (success) {
                currentAccount = account;
                updateCurrentAccountInfo(`当前邮箱: ${account.email}`);

                // 使用新的数据管理器并发加载所有数据
                try {
                    const loadResults = await dataManager.loadAllSystemData();

                    // 使用后台用户管理器处理用户选择
                    await backendUserManager.loadBackendUsersWithFallback();

                    // 更新UI组件
                    updateCarTypeSelectors();
                    updateRegionSelector();

                    console.log('✅ 所有数据加载完成:', loadResults);
                    updateCurrentAccountInfo(`✅ ${account.email} - 数据加载完成`);

                } catch (error) {
                    console.error('❌ 数据加载失败:', error);
                    updateCurrentAccountInfo(`⚠️ ${account.email} - 数据加载部分失败`);
                }
            } else {
                updateCurrentAccountInfo('认证失败 - 使用降级数据');

                // 认证失败时使用数据管理器的降级机制
                console.warn('⚠️ 认证失败，使用降级数据确保基本功能可用');

                try {
                    // 强制使用降级数据
                    availableBackendUsers = dataManager.fallbackData.backendUsers;
                    availableCarTypes = dataManager.fallbackData.carTypes;
                    availableSubCategories = dataManager.fallbackData.subCategories;
                    availableRegions = dataManager.fallbackData.drivingRegions;
                    availableLanguages = dataManager.fallbackData.languages;

                    // 设置降级状态
                    dataManager.setDataStatus('backendUsers', 'fallback', 'memory-bank', '认证失败，使用降级数据');
                    dataManager.setDataStatus('carTypes', 'fallback', 'memory-bank', '认证失败，使用降级数据');
                    dataManager.setDataStatus('subCategories', 'fallback', 'memory-bank', '认证失败，使用降级数据');
                    dataManager.setDataStatus('drivingRegions', 'fallback', 'memory-bank', '认证失败，使用降级数据');
                    dataManager.setDataStatus('languages', 'fallback', 'memory-bank', '认证失败，使用降级数据');

                    // 更新UI
                    backendUserManager.updateBackendUserSelector();
                    backendUserManager.selectDefaultUser();
                    updateCarTypeSelectors();
                    updateRegionSelector();

                    console.log('📦 降级数据加载完成');
                } catch (fallbackError) {
                    console.error('❌ 降级数据加载也失败:', fallbackError);
                    updateCurrentAccountInfo('❌ 数据加载完全失败');
                }
            }
        }

        // 从选择器切换账号
        async function switchAccount() {
            const selector = document.getElementById('accountSelector');
            if (!selector || !selector.value) return;
            await switchToAccount(selector.value);
        }

        // 认证指定账号
        async function authenticateAccount(account) {
            try {
                const loginResponse = await fetch(`${API_BASE_URL}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        'email': account.email,
                        'password': account.password
                    })
                });

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    if (loginData.status && loginData.token) {
                        const fullToken = loginData.token;
                        authToken = fullToken.includes('|') ? fullToken.split('|')[1] : fullToken;
                        updateAuthStatus(true, `✅ ${account.email} 认证成功`);
                        return true;
                    }
                }

                updateAuthStatus(false, `❌ ${account.email} 认证失败`);
                return false;

            } catch (error) {
                updateAuthStatus(false, `❌ ${account.email} 认证失败：网络错误`);
                return false;
            }
        }

        // 增强的智能后台用户匹配规则
        const backendUserMapping = {
            '<EMAIL>': { id: 37, name: 'smw', role: 'admin' },
            '<EMAIL>': { id: 310, name: 'Jcy', role: 'manager' },
            '<EMAIL>': 'use_first_available' // 使用API返回的第一个用户
        };

        /**
         * @class BackendUserManager - 后台用户管理器
         * @description 管理后台用户的获取、匹配和降级机制
         */
        class BackendUserManager {
            constructor() {
                this.selectedUser = null;
                this.matchingStrategy = 'smart'; // smart/manual/default
            }

            /**
             * @function loadBackendUsersWithFallback - 加载后台用户（带完整降级机制）
             * @returns {Promise<Array>} 后台用户列表
             */
            async loadBackendUsersWithFallback() {
                try {
                    // 使用数据管理器加载数据
                    const users = await dataManager.loadDataWithFallback(
                        'backendUsers',
                        '/backend_users',
                        dataManager._validateBackendUsers.bind(dataManager)
                    );

                    availableBackendUsers = users;
                    this.updateBackendUserSelector();

                    // 自动智能选择用户
                    await this.performSmartUserSelection();

                    return users;
                } catch (error) {
                    console.error('后台用户加载失败:', error);
                    // 使用最基础的降级数据
                    availableBackendUsers = this.getEmergencyBackendUsers();
                    this.updateBackendUserSelector();
                    return availableBackendUsers;
                }
            }

            /**
             * @function performSmartUserSelection - 执行智能用户选择
             * @description 根据当前登录邮箱智能匹配后台用户
             */
            async performSmartUserSelection() {
                if (!currentAccount || !currentAccount.email) {
                    this.selectDefaultUser();
                    return;
                }

                const email = currentAccount.email;
                const mapping = backendUserMapping[email];

                if (mapping && mapping !== 'use_first_available') {
                    // 尝试精确匹配
                    const matchedUser = availableBackendUsers.find(u => u.id === mapping.id);
                    if (matchedUser) {
                        this.selectUser(matchedUser);
                        this.updateBackendUserInfo(`🎯 智能匹配成功: ${matchedUser.name} (ID: ${matchedUser.id})`);
                        console.log(`✅ 智能匹配成功: ${email} → ${matchedUser.name} (${matchedUser.id})`);
                        return;
                    } else {
                        this.updateBackendUserInfo(`⚠️ 智能匹配失败: 预设用户ID ${mapping.id} 不存在`);
                        console.warn(`⚠️ 智能匹配失败: ${email} 的预设用户ID ${mapping.id} 在API响应中不存在`);
                    }
                }

                // 降级到默认选择
                this.selectDefaultUser();
            }

            /**
             * @function selectDefaultUser - 选择默认用户
             */
            selectDefaultUser() {
                if (availableBackendUsers.length > 0) {
                    const defaultUser = availableBackendUsers[0];
                    this.selectUser(defaultUser);
                    this.updateBackendUserInfo(`📋 默认选择: ${defaultUser.name} (ID: ${defaultUser.id})`);
                    console.log(`📋 使用默认用户: ${defaultUser.name} (${defaultUser.id})`);
                } else {
                    this.updateBackendUserInfo('❌ 无可用后台用户');
                    console.error('❌ 无可用后台用户');
                }
            }

            /**
             * @function selectUser - 选择指定用户
             * @param {Object} user - 用户对象
             */
            selectUser(user) {
                this.selectedUser = user;
                selectedBackendUser = user;

                // 更新UI选择器
                const selector = document.getElementById('backendUserSelect');
                if (selector) {
                    selector.value = user.id;
                }

                // 显示后台用户选择器
                const backendUserSelector = document.getElementById('backendUserSelector');
                if (backendUserSelector) {
                    backendUserSelector.style.display = 'block';
                }

                this.updateSelectedUserInfo();
            }

            /**
             * @function updateBackendUserSelector - 更新后台用户选择器
             */
            updateBackendUserSelector() {
                const selector = document.getElementById('backendUserSelect');
                if (!selector) return;

                const currentValue = selector.value;
                selector.innerHTML = '<option value="">选择后台用户...</option>';

                if (availableBackendUsers.length === 0) {
                    selector.innerHTML = '<option value="">暂无可用用户</option>';
                    return;
                }

                // 按ID排序显示用户
                const sortedUsers = [...availableBackendUsers].sort((a, b) => a.id - b.id);

                sortedUsers.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = `${user.name} (ID: ${user.id})${user.email ? ` - ${user.email}` : ''}`;
                    selector.appendChild(option);
                });

                // 恢复之前选中的值
                if (currentValue && availableBackendUsers.find(u => u.id == currentValue)) {
                    selector.value = currentValue;
                }
            }

            /**
             * @function updateBackendUserInfo - 更新后台用户信息显示
             * @param {string} message - 显示消息
             */
            updateBackendUserInfo(message) {
                const infoElement = document.getElementById('backendUserInfo');
                if (infoElement) {
                    infoElement.textContent = message;
                }
            }

            /**
             * @function updateSelectedUserInfo - 更新选中用户信息显示
             */
            updateSelectedUserInfo() {
                const infoElement = document.getElementById('selectedBackendUserInfo');
                if (infoElement && this.selectedUser) {
                    const user = this.selectedUser;
                    const statusInfo = dataManager.getDataStatus('backendUsers');
                    infoElement.innerHTML = `
                        <strong>当前选择:</strong> ${user.name} (ID: ${user.id})<br>
                        <strong>邮箱:</strong> ${user.email || '未设置'}<br>
                        <strong>数据来源:</strong> ${statusInfo.source} (${statusInfo.status})
                    `;
                }
            }

            /**
             * @function getEmergencyBackendUsers - 获取紧急降级用户数据
             * @returns {Array} 最基础的用户列表
             */
            getEmergencyBackendUsers() {
                return [
                    { id: 1, name: 'Super Admin', email: '', role: 'admin' },
                    { id: 37, name: 'smw', email: '<EMAIL>', role: 'admin' },
                    { id: 310, name: 'Jcy', email: '<EMAIL>', role: 'manager' }
                ];
            }

            /**
             * @function validateSelectedUser - 验证选中的用户
             * @returns {Object} 验证结果
             */
            validateSelectedUser() {
                if (!this.selectedUser) {
                    return { isValid: false, error: '请选择后台用户' };
                }

                const user = availableBackendUsers.find(u => u.id === this.selectedUser.id);
                if (!user) {
                    return { isValid: false, error: `后台用户ID ${this.selectedUser.id} 不存在` };
                }

                return { isValid: true, user: user };
            }
        }

        // 创建全局后台用户管理器实例
        const backendUserManager = new BackendUserManager();

        /**
         * @function getSmartBackendUserId - 根据当前登录邮箱智能匹配后台用户ID
         * @returns {number|null} 匹配的后台用户ID，null表示使用默认逻辑
         */
        function getSmartBackendUserId() {
            if (!currentAccount || !currentAccount.email) return null;
            return backendUserMapping[currentAccount.email] || null;
        }

        /**
         * @function smartSelectBackendUser - 智能选择后台用户
         * @description 根据当前登录邮箱自动选择对应的后台用户，如果匹配失败则使用默认逻辑
         */
        function smartSelectBackendUser() {
            const smartUserId = getSmartBackendUserId();

            if (smartUserId) {
                // 尝试智能匹配
                const matchedUser = availableBackendUsers.find(u => u.id === smartUserId);
                if (matchedUser) {
                    selectBackendUser(smartUserId);
                    updateBackendUserInfo(`🎯 智能匹配: ${matchedUser.name} (ID: ${smartUserId})`);
                    return true;
                } else {
                    updateBackendUserInfo(`⚠️ 智能匹配失败: 未找到用户ID ${smartUserId}，使用默认选择`);
                }
            }

            // 回退到默认逻辑
            if (availableBackendUsers.length > 0) {
                selectBackendUser(availableBackendUsers[0].id);
                updateBackendUserInfo(`📋 默认选择: ${availableBackendUsers[0].name} (ID: ${availableBackendUsers[0].id})`);
                return true;
            }

            return false;
        }

        // ========== 车型管理功能 ==========

        /**
         * @function loadCarTypes - 从API加载可用车型列表（使用新的数据管理器）
         * @returns {Array} 车型列表
         * @description 获取GoMyHire API支持的所有车型，用于动态更新选择器
         */
        async function loadCarTypes() {
            const carTypes = await dataManager.loadDataWithFallback(
                'carTypes',
                '/car_types',
                dataManager._validateCarTypes.bind(dataManager)
            );

            availableCarTypes = carTypes;
            updateCarTypeSelectors();
            return carTypes;
        }

        /**
         * @function getBackupCarTypes - 获取备用车型列表
         * @returns {Array} 备用车型配置
         * @description 当API不可用时使用的备用车型列表
         */
        function getBackupCarTypes() {
            return [
                { id: 1, type: 'Comfort 5 Seater', seat_number: 5, priority: 1 },
                { id: 5, type: 'Economy 5 Seater', seat_number: 5, priority: 2 },
                { id: 15, type: 'Economy 7 Seater', seat_number: 7, priority: 3 },
                { id: 20, type: 'Comfort 7 Seater', seat_number: 7, priority: 4 },
                { id: 25, type: 'Mini Bus 14 Seater', seat_number: 14, priority: 5 },
                { id: 26, type: 'Bus 20+ Seater', seat_number: 20, priority: 6 },
                { id: 32, type: 'Luxury 5 Seater', seat_number: 5, priority: 7 },
                { id: 33, type: 'Luxury 7 Seater', seat_number: 7, priority: 8 },
                { id: 36, type: 'Premium Luxury', seat_number: 5, priority: 9 },
                { id: 38, type: 'Economy Plus', seat_number: 5, priority: 10 }
            ];
        }

        /**
         * @function updateCarTypeSelectors - 更新所有车型选择器
         * @description 使用最新的车型数据更新手动输入表单的车型选择器
         */
        function updateCarTypeSelectors() {
            const manualSelector = document.getElementById('manualCarType');
            if (!manualSelector) return;

            // 保存当前选中的值
            const currentValue = manualSelector.value;

            // 清空并重新填充选项
            manualSelector.innerHTML = '';

            if (availableCarTypes.length === 0) {
                manualSelector.innerHTML = '<option value="">暂无可用车型</option>';
                return;
            }

            // 按优先级或座位数排序
            const sortedCarTypes = [...availableCarTypes].sort((a, b) => {
                return (a.priority || a.seat_number || 0) - (b.priority || b.seat_number || 0);
            });

            sortedCarTypes.forEach(carType => {
                const option = document.createElement('option');
                option.value = carType.id;
                option.textContent = `${carType.type || carType.name || 'Unknown'} (${carType.seat_number || 'N/A'} 座)`;
                manualSelector.appendChild(option);
            });

            // 恢复之前选中的值，如果仍然有效
            if (currentValue && availableCarTypes.find(ct => ct.id == currentValue)) {
                manualSelector.value = currentValue;
            } else {
                // 选择第一个可用选项
                manualSelector.value = sortedCarTypes[0]?.id || '';
            }

            console.log(`🚗 车型选择器已更新，共 ${availableCarTypes.length} 个选项`);
        }

        /**
         * @function validateCarType - 验证车型ID是否有效
         * @param {number} carTypeId - 车型ID
         * @returns {Object} 验证结果 {isValid: boolean, carType: Object|null, error: string}
         */
        function validateCarType(carTypeId) {
            if (!carTypeId) {
                return { isValid: false, carType: null, error: '请选择车型' };
            }

            const carType = availableCarTypes.find(ct => ct.id == carTypeId);
            if (!carType) {
                return {
                    isValid: false,
                    carType: null,
                    error: `车型ID ${carTypeId} 不存在，请选择有效的车型`
                };
            }

            return { isValid: true, carType: carType, error: null };
        }

        /**
         * @function getCarTypeDisplayName - 获取车型显示名称
         * @param {number} carTypeId - 车型ID
         * @returns {string} 车型显示名称
         */
        function getCarTypeDisplayName(carTypeId) {
            const carType = availableCarTypes.find(ct => ct.id == carTypeId);
            if (carType) {
                return `${carType.type || carType.name || 'Unknown'} (ID: ${carTypeId})`;
            }
            return `车型ID: ${carTypeId} (未知车型)`;
        }

        /**
         * @function getRegionDisplayName - 获取地区显示名称
         * @param {number} regionId - 地区ID
         * @returns {string} 地区显示名称
         */
        function getRegionDisplayName(regionId) {
            const region = availableRegions.find(r => r.id == regionId);
            if (region) {
                return `${region.name} (ID: ${regionId})`;
            }
            return `地区ID: ${regionId} (未知地区)`;
        }

        /**
         * @function getLanguagesDisplayName - 获取语言显示名称
         * @param {Array} languageIds - 语言ID数组
         * @returns {string} 语言显示名称
         */
        function getLanguagesDisplayName(languageIds) {
            if (!Array.isArray(languageIds) || languageIds.length === 0) {
                return '未指定语言';
            }

            const languageNames = languageIds.map(id => {
                const language = availableLanguages.find(lang => lang.id == id);
                return language ? language.name : `语言ID: ${id}`;
            });

            return languageNames.join(', ');
        }

        /**
         * @function getSubCategoryDisplayName - 获取子分类显示名称
         * @param {number} subCategoryId - 子分类ID
         * @returns {string} 子分类显示名称
         */
        function getSubCategoryDisplayName(subCategoryId) {
            const subCategory = availableSubCategories.find(sc => sc.id == subCategoryId);
            if (subCategory) {
                return `${subCategory.name} (ID: ${subCategoryId})`;
            }
            return `子分类ID: ${subCategoryId} (未知类型)`;
        }

        // ========== 子分类管理功能 ==========

        /**
         * @function loadSubCategories - 从API加载可用子分类列表（使用新的数据管理器）
         * @returns {Array} 子分类列表
         * @description 获取GoMyHire API支持的所有订单子分类
         */
        async function loadSubCategories() {
            const subCategories = await dataManager.loadDataWithFallback(
                'subCategories',
                '/sub_categories',
                dataManager._validateSubCategories.bind(dataManager)
            );

            availableSubCategories = subCategories;
            return subCategories;
        }

        /**
         * @function getBackupSubCategories - 获取备用子分类列表
         * @returns {Array} 备用子分类配置
         */
        function getBackupSubCategories() {
            return [
                { id: 2, main_category: 'Airport Transfer', name: 'Pickup Service', type: 'pickup' },
                { id: 3, main_category: 'Airport Transfer', name: 'Drop-off Service', type: 'dropoff' },
                { id: 4, main_category: 'Charter Service', name: 'Charter Service', type: 'charter' }
            ];
        }

        /**
         * @function validateSubCategory - 验证子分类ID是否有效
         * @param {number} subCategoryId - 子分类ID
         * @returns {Object} 验证结果
         */
        function validateSubCategory(subCategoryId) {
            if (!subCategoryId) {
                return { isValid: false, subCategory: null, error: '请选择订单类型' };
            }

            const subCategory = availableSubCategories.find(sc => sc.id == subCategoryId);
            if (!subCategory) {
                return {
                    isValid: false,
                    subCategory: null,
                    error: `子分类ID ${subCategoryId} 不存在，请选择有效的订单类型`
                };
            }

            return { isValid: true, subCategory: subCategory, error: null };
        }

        /**
         * @function getSubCategoryByType - 根据订单类型获取子分类ID
         * @param {string} orderType - 订单类型 (pickup/dropoff/charter)
         * @returns {number|null} 子分类ID
         */
        function getSubCategoryByType(orderType) {
            const subCategory = availableSubCategories.find(sc =>
                sc.type === orderType ||
                (orderType === 'pickup' && sc.name?.toLowerCase().includes('pickup')) ||
                (orderType === 'dropoff' && sc.name?.toLowerCase().includes('drop')) ||
                (orderType === 'charter' && sc.name?.toLowerCase().includes('charter'))
            );
            return subCategory ? subCategory.id : null;
        }

        // ========== 地区管理功能 ==========

        /**
         * @function loadRegions - 从API加载可用地区列表（使用新的数据管理器）
         * @returns {Array} 地区列表
         */
        async function loadRegions() {
            const regions = await dataManager.loadDataWithFallback(
                'drivingRegions',
                '/driving_regions',
                dataManager._validateDrivingRegions.bind(dataManager)
            );

            availableRegions = regions;
            updateRegionSelector();
            return regions;
        }

        /**
         * @function getBackupRegions - 获取备用地区列表
         * @returns {Array} 备用地区配置
         */
        function getBackupRegions() {
            return [
                { id: 1, name: '吉隆坡/雪兰莪', code: 'KL', active: true },
                { id: 2, name: '槟城', code: 'PG', active: true },
                { id: 3, name: '柔佛', code: 'JH', active: true },
                { id: 4, name: '沙巴', code: 'SB', active: true },
                { id: 12, name: '马六甲', code: 'ML', active: true }
            ];
        }

        /**
         * @function updateRegionSelector - 更新地区选择器
         */
        function updateRegionSelector() {
            const regionSelector = document.getElementById('manualRegion');
            if (!regionSelector) return;

            const currentValue = regionSelector.value;
            regionSelector.innerHTML = '';

            if (availableRegions.length === 0) {
                regionSelector.innerHTML = '<option value="">暂无可用地区</option>';
                return;
            }

            availableRegions.forEach(region => {
                const option = document.createElement('option');
                option.value = region.id;
                option.textContent = region.name || `地区 ${region.id}`;
                regionSelector.appendChild(option);
            });

            // 恢复之前选中的值
            if (currentValue && availableRegions.find(r => r.id == currentValue)) {
                regionSelector.value = currentValue;
            } else {
                regionSelector.value = availableRegions[0]?.id || '';
            }

            console.log(`🌍 地区选择器已更新，共 ${availableRegions.length} 个选项`);
        }

        /**
         * @function validateRegion - 验证地区ID是否有效
         * @param {number} regionId - 地区ID
         * @returns {Object} 验证结果
         */
        function validateRegion(regionId) {
            if (!regionId) {
                return { isValid: false, region: null, error: '请选择服务地区' };
            }

            const region = availableRegions.find(r => r.id == regionId);
            if (!region) {
                return {
                    isValid: false,
                    region: null,
                    error: `地区ID ${regionId} 不存在，请选择有效的服务地区`
                };
            }

            return { isValid: true, region: region, error: null };
        }

        // ========== 语言管理功能 ==========

        /**
         * @function loadLanguages - 从API加载可用语言列表（使用新的数据管理器）
         * @returns {Array} 语言列表
         */
        async function loadLanguages() {
            const languages = await dataManager.loadDataWithFallback(
                'languages',
                '/languages',
                dataManager._validateLanguages.bind(dataManager)
            );

            availableLanguages = languages;
            return languages;
        }

        /**
         * @function getBackupLanguages - 获取备用语言列表
         * @returns {Array} 备用语言配置
         */
        function getBackupLanguages() {
            return [
                { id: 2, name: 'English', code: 'en', active: true },
                { id: 3, name: 'Bahasa Malaysia', code: 'ms', active: true },
                { id: 4, name: '中文', code: 'zh', active: true }
            ];
        }

        /**
         * @function getLanguagesByCustomerType - 根据客户类型获取语言ID数组
         * @param {string} customerType - 客户类型 (chinese/english/malay)
         * @returns {Array} 语言ID数组
         */
        function getLanguagesByCustomerType(customerType) {
            if (availableLanguages.length === 0) {
                // 使用备用配置
                switch (customerType) {
                    case 'chinese': return [2, 4]; // 英文 + 中文
                    case 'english': return [2]; // 英文
                    case 'malay': return [3]; // 马来文
                    default: return [2]; // 默认英文
                }
            }

            // 使用动态语言数据
            const languageMap = {
                'chinese': ['en', 'zh'],
                'english': ['en'],
                'malay': ['ms']
            };

            const targetCodes = languageMap[customerType] || ['en'];
            const languageIds = [];

            targetCodes.forEach(code => {
                const language = availableLanguages.find(lang =>
                    lang.code === code ||
                    lang.name?.toLowerCase().includes(code) ||
                    (code === 'zh' && lang.name?.includes('中文'))
                );
                if (language) {
                    languageIds.push(language.id);
                }
            });

            return languageIds.length > 0 ? languageIds : [2]; // 默认返回英文
        }

        /**
         * @function validateLanguages - 验证语言ID数组是否有效
         * @param {Array} languageIds - 语言ID数组
         * @returns {Object} 验证结果
         */
        function validateLanguages(languageIds) {
            if (!Array.isArray(languageIds) || languageIds.length === 0) {
                return { isValid: false, languages: [], error: '请至少选择一种语言' };
            }

            const validLanguages = [];
            const invalidIds = [];

            languageIds.forEach(id => {
                const language = availableLanguages.find(lang => lang.id == id);
                if (language) {
                    validLanguages.push(language);
                } else {
                    invalidIds.push(id);
                }
            });

            if (invalidIds.length > 0) {
                return {
                    isValid: false,
                    languages: validLanguages,
                    error: `语言ID ${invalidIds.join(', ')} 不存在`
                };
            }

            return { isValid: true, languages: validLanguages, error: null };
        }

        // ========== 错误诊断和分析功能 ==========

        /**
         * @function logApiCall - 记录API调用详情
         * @param {string} endpoint - API端点
         * @param {Object} requestData - 请求数据
         * @param {Object} response - 响应对象
         * @param {string} responseText - 响应文本
         * @param {number} responseTime - 响应时间
         */
        function logApiCall(endpoint, requestData, response, responseText, responseTime) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                endpoint: endpoint,
                method: 'POST',
                requestData: JSON.parse(JSON.stringify(requestData)),
                responseStatus: response.status,
                responseStatusText: response.statusText,
                responseHeaders: Object.fromEntries(response.headers.entries()),
                responseText: responseText,
                responseTime: responseTime,
                success: response.ok
            };

            apiCallHistory.unshift(logEntry);

            // 保持最近100条记录
            if (apiCallHistory.length > 100) {
                apiCallHistory = apiCallHistory.slice(0, 100);
            }

            console.log('📡 API调用记录:', logEntry);
        }

        /**
         * @function analyzeApiError - 分析API错误
         * @param {Object} response - 响应对象
         * @param {string} responseText - 响应文本
         * @param {Object} requestData - 请求数据
         * @returns {Object} 错误分析结果
         */
        function analyzeApiError(response, responseText, requestData) {
            const analysis = {
                errorType: 'unknown',
                possibleCauses: [],
                recommendations: [],
                severity: 'medium'
            };

            // 分析HTTP状态码
            switch (response.status) {
                case 400:
                    analysis.errorType = 'bad_request';
                    analysis.possibleCauses.push('请求数据格式错误', '必填字段缺失', '字段值无效');
                    analysis.recommendations.push('检查所有字段格式', '验证必填字段', '确认字段值范围');
                    analysis.severity = 'high';
                    break;
                case 401:
                    analysis.errorType = 'unauthorized';
                    analysis.possibleCauses.push('认证token无效', '权限不足');
                    analysis.recommendations.push('重新登录获取token', '检查账号权限');
                    analysis.severity = 'high';
                    break;
                case 403:
                    analysis.errorType = 'forbidden';
                    analysis.possibleCauses.push('账号权限不足', 'API访问限制');
                    analysis.recommendations.push('联系管理员检查权限', '确认API访问策略');
                    analysis.severity = 'high';
                    break;
                case 404:
                    analysis.errorType = 'not_found';
                    analysis.possibleCauses.push('API端点不存在', '资源ID无效');
                    analysis.recommendations.push('检查API端点URL', '验证资源ID有效性');
                    analysis.severity = 'medium';
                    break;
                case 422:
                    analysis.errorType = 'validation_error';
                    analysis.possibleCauses.push('数据验证失败', '字段格式不符合要求');
                    analysis.recommendations.push('检查字段格式', '验证数据类型', '确认字段值范围');
                    analysis.severity = 'high';
                    break;
                case 500:
                    analysis.errorType = 'server_error';
                    analysis.possibleCauses.push('服务器内部错误', '数据库错误', '业务逻辑错误', '日期格式不兼容', '用户权限问题');
                    analysis.recommendations.push(
                        '检查请求数据完整性',
                        '验证所有ID字段有效性',
                        '确认日期格式为DD-MM-YYYY',
                        '验证后台用户权限',
                        '检查子分类ID是否存在',
                        '简化请求数据测试'
                    );
                    analysis.severity = 'critical';
                    break;
                default:
                    analysis.errorType = 'unknown';
                    analysis.possibleCauses.push('未知错误类型');
                    analysis.recommendations.push('查看详细错误信息', '联系技术支持');
                    analysis.severity = 'medium';
            }

            // 分析响应内容
            try {
                const errorData = JSON.parse(responseText);
                if (errorData.message) {
                    analysis.serverMessage = errorData.message;
                }
                if (errorData.errors) {
                    analysis.fieldErrors = errorData.errors;
                    analysis.possibleCauses.push('特定字段验证失败');
                    analysis.recommendations.push('检查错误字段: ' + Object.keys(errorData.errors).join(', '));
                }
            } catch (e) {
                // 响应不是JSON格式
                if (responseText.includes('500')) {
                    analysis.possibleCauses.push('服务器内部错误');
                }
            }

            // 分析请求数据
            analysis.requestAnalysis = analyzeRequestData(requestData);

            return analysis;
        }

        /**
         * @function analyzeRequestData - 分析请求数据
         * @param {Object} requestData - 请求数据
         * @returns {Object} 数据分析结果
         */
        function analyzeRequestData(requestData) {
            const analysis = {
                issues: [],
                warnings: [],
                suggestions: []
            };

            // 检查必填字段
            const requiredFields = [
                'sub_category_id', 'car_type_id', 'incharge_by_backend_user_id',
                'customer_name', 'customer_contact', 'customer_email',
                'pickup', 'destination', 'date', 'time',
                'passenger_number', 'driving_region_id'
            ];

            requiredFields.forEach(field => {
                if (!requestData[field]) {
                    analysis.issues.push(`缺失必填字段: ${field}`);
                }
            });

            // 检查数据类型
            if (requestData.sub_category_id && typeof requestData.sub_category_id !== 'number') {
                analysis.warnings.push('sub_category_id应为数字类型');
            }
            if (requestData.car_type_id && typeof requestData.car_type_id !== 'number') {
                analysis.warnings.push('car_type_id应为数字类型');
            }
            if (requestData.driving_region_id && typeof requestData.driving_region_id !== 'number') {
                analysis.warnings.push('driving_region_id应为数字类型');
            }

            // 检查数据范围
            if (requestData.passenger_number && (requestData.passenger_number < 1 || requestData.passenger_number > 50)) {
                analysis.warnings.push('passenger_number超出合理范围(1-50)');
            }

            // 检查日期格式 - 现在使用DD-MM-YYYY格式
            if (requestData.date) {
                const ddmmyyyyRegex = /^\d{2}-\d{2}-\d{4}$/;
                const yyyymmddRegex = /^\d{4}-\d{2}-\d{2}$/;
                if (!ddmmyyyyRegex.test(requestData.date) && !yyyymmddRegex.test(requestData.date)) {
                    analysis.warnings.push('date格式可能不正确，建议使用DD-MM-YYYY格式');
                } else if (yyyymmddRegex.test(requestData.date)) {
                    analysis.warnings.push('检测到YYYY-MM-DD格式，系统现在使用DD-MM-YYYY格式');
                }
            }

            // 检查邮箱格式
            if (requestData.customer_email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(requestData.customer_email)) {
                    analysis.warnings.push('customer_email格式不正确');
                }
            }

            // 检查ID字段有效性
            if (requestData.car_type_id && !availableCarTypes.find(ct => ct.id == requestData.car_type_id)) {
                analysis.issues.push(`car_type_id ${requestData.car_type_id} 在可用车型列表中不存在`);
            }
            if (requestData.sub_category_id && !availableSubCategories.find(sc => sc.id == requestData.sub_category_id)) {
                analysis.issues.push(`sub_category_id ${requestData.sub_category_id} 在可用子分类列表中不存在`);
            }
            if (requestData.driving_region_id && !availableRegions.find(r => r.id == requestData.driving_region_id)) {
                analysis.issues.push(`driving_region_id ${requestData.driving_region_id} 在可用地区列表中不存在`);
            }

            return analysis;
        }

        // ========== 诊断工具核心功能 ==========

        /**
         * @function runDiagnosticTest - 运行诊断测试
         * @description 执行系统性的诊断测试，从简单到复杂逐步验证
         */
        async function runDiagnosticTest() {
            const diagnosticLevel = document.getElementById('diagnosticLevel').value;
            const testComplexity = document.getElementById('testComplexity').value;
            const resultContainer = document.getElementById('diagnosticResults');

            resultContainer.style.display = 'block';
            resultContainer.className = 'test-result result-pending';
            resultContainer.innerHTML = '🔍 正在运行诊断测试...';

            try {
                // 清除之前的诊断日志
                diagnosticLogs = [];

                // 验证基础配置
                const basicValidation = await validateBasicConfiguration();

                // 生成诊断测试用例
                const diagnosticTestCases = generateDiagnosticTestCases(testComplexity);

                // 执行测试
                const testResults = await executeDiagnosticTests(diagnosticTestCases, diagnosticLevel);

                // 生成报告
                const report = generateDiagnosticSummary(basicValidation, testResults);

                resultContainer.className = 'test-result result-success';
                resultContainer.innerHTML = report;

                // 显示API调用历史
                updateApiCallHistory();

            } catch (error) {
                resultContainer.className = 'test-result result-error';
                resultContainer.innerHTML = `
                    ❌ <strong>诊断测试失败</strong><br>
                    错误: ${error.message}<br>
                    <small>请检查网络连接和认证状态</small>
                `;
                console.error('诊断测试失败:', error);
            }
        }

        /**
         * @function validateBasicConfiguration - 验证基础配置
         * @returns {Object} 验证结果
         */
        async function validateBasicConfiguration() {
            const validation = {
                auth: false,
                carTypes: false,
                subCategories: false,
                regions: false,
                languages: false,
                backendUsers: false,
                issues: []
            };

            // 检查认证状态
            if (!authToken) {
                validation.issues.push('未找到认证token');
            } else {
                validation.auth = true;
            }

            // 检查数据加载状态
            if (availableCarTypes.length === 0) {
                validation.issues.push('车型列表为空');
            } else {
                validation.carTypes = true;
            }

            if (availableSubCategories.length === 0) {
                validation.issues.push('子分类列表为空');
            } else {
                validation.subCategories = true;
            }

            if (availableRegions.length === 0) {
                validation.issues.push('地区列表为空');
            } else {
                validation.regions = true;
            }

            if (availableLanguages.length === 0) {
                validation.issues.push('语言列表为空');
            } else {
                validation.languages = true;
            }

            if (availableBackendUsers.length === 0) {
                validation.issues.push('后台用户列表为空');
            } else {
                validation.backendUsers = true;
            }

            return validation;
        }

        /**
         * @function generateDiagnosticTestCases - 生成诊断测试用例
         * @param {string} complexity - 测试复杂度
         * @returns {Array} 测试用例列表
         */
        function generateDiagnosticTestCases(complexity) {
            const testCases = [];

            // 基础测试用例（最简单配置）
            testCases.push({
                name: '基础接机测试',
                description: '最简单的接机订单配置',
                data: {
                    sub_category_id: availableSubCategories.find(sc => sc.type === 'pickup')?.id || 2,
                    car_type_id: availableCarTypes[0]?.id || 1,
                    incharge_by_backend_user_id: selectedBackendUser?.id || 1,
                    ota_reference_number: `DIAG_BASIC_${Date.now()}`,
                    customer_name: 'Test Customer',
                    customer_contact: '+***********',
                    customer_email: '<EMAIL>',
                    pickup: 'KLIA Airport',
                    destination: 'KLCC',
                    date: getNextDate(),
                    time: '10:00',
                    passenger_number: 2,
                    luggage_number: 2,
                    driving_region_id: availableRegions[0]?.id || 1,
                    languages_id_array: [availableLanguages[0]?.id || 2],
                    extra_requirement: 'DIAGNOSTIC TEST - 基础测试'
                }
            });

            if (complexity === 'progressive' || complexity === 'comprehensive') {
                // 渐进式测试 - 不同订单类型
                ['pickup', 'dropoff', 'charter'].forEach(type => {
                    const subCategory = availableSubCategories.find(sc => sc.type === type);
                    if (subCategory) {
                        testCases.push({
                            name: `${type}服务测试`,
                            description: `${type}订单类型测试`,
                            data: {
                                sub_category_id: subCategory.id,
                                car_type_id: availableCarTypes[0]?.id || 1,
                                incharge_by_backend_user_id: selectedBackendUser?.id || 1,
                                ota_reference_number: `DIAG_${type.toUpperCase()}_${Date.now()}`,
                                customer_name: 'Test Customer',
                                customer_contact: '+***********',
                                customer_email: '<EMAIL>',
                                pickup: type === 'dropoff' ? 'KLCC' : 'KLIA Airport',
                                destination: type === 'dropoff' ? 'KLIA Airport' : 'KLCC',
                                date: getNextDate(),
                                time: '10:00',
                                passenger_number: 2,
                                luggage_number: 2,
                                driving_region_id: availableRegions[0]?.id || 1,
                                languages_id_array: [availableLanguages[0]?.id || 2],
                                extra_requirement: `DIAGNOSTIC TEST - ${type}测试`
                            }
                        });
                    }
                });

                // 不同车型测试
                availableCarTypes.slice(0, 3).forEach((carType, index) => {
                    testCases.push({
                        name: `车型测试 - ${carType.type}`,
                        description: `测试车型ID ${carType.id}`,
                        data: {
                            sub_category_id: availableSubCategories[0]?.id || 2,
                            car_type_id: carType.id,
                            incharge_by_backend_user_id: selectedBackendUser?.id || 1,
                            ota_reference_number: `DIAG_CAR_${carType.id}_${Date.now()}`,
                            customer_name: 'Test Customer',
                            customer_contact: '+***********',
                            customer_email: '<EMAIL>',
                            pickup: 'KLIA Airport',
                            destination: 'KLCC',
                            date: getNextDate(),
                            time: '10:00',
                            passenger_number: Math.min(carType.seat_number || 2, 4),
                            luggage_number: 2,
                            driving_region_id: availableRegions[0]?.id || 1,
                            languages_id_array: [availableLanguages[0]?.id || 2],
                            extra_requirement: `DIAGNOSTIC TEST - 车型${carType.id}测试`
                        }
                    });
                });
            }

            if (complexity === 'comprehensive') {
                // 全面测试 - 边界值测试
                testCases.push({
                    name: '边界值测试 - 最小配置',
                    description: '最小乘客数和行李数',
                    data: {
                        sub_category_id: availableSubCategories[0]?.id || 2,
                        car_type_id: availableCarTypes[0]?.id || 1,
                        incharge_by_backend_user_id: selectedBackendUser?.id || 1,
                        ota_reference_number: `DIAG_MIN_${Date.now()}`,
                        customer_name: 'Min Test',
                        customer_contact: '+***********',
                        customer_email: '<EMAIL>',
                        pickup: 'A',
                        destination: 'B',
                        date: getNextDate(),
                        time: '00:01',
                        passenger_number: 1,
                        luggage_number: 0,
                        driving_region_id: availableRegions[0]?.id || 1,
                        languages_id_array: [availableLanguages[0]?.id || 2],
                        extra_requirement: 'DIAGNOSTIC TEST - 边界值最小'
                    }
                });

                testCases.push({
                    name: '边界值测试 - 最大配置',
                    description: '最大乘客数和行李数',
                    data: {
                        sub_category_id: availableSubCategories[0]?.id || 2,
                        car_type_id: availableCarTypes.find(ct => ct.seat_number >= 20)?.id || availableCarTypes[0]?.id || 1,
                        incharge_by_backend_user_id: selectedBackendUser?.id || 1,
                        ota_reference_number: `DIAG_MAX_${Date.now()}`,
                        customer_name: 'Max Test Customer With Very Long Name',
                        customer_contact: '+***********',
                        customer_email: '<EMAIL>',
                        pickup: 'Very Long Pickup Address With Many Details',
                        destination: 'Very Long Destination Address With Many Details',
                        date: getNextDate(),
                        time: '23:59',
                        passenger_number: 20,
                        luggage_number: 40,
                        driving_region_id: availableRegions[0]?.id || 1,
                        languages_id_array: availableLanguages.map(lang => lang.id),
                        extra_requirement: 'DIAGNOSTIC TEST - 边界值最大配置，包含特殊字符：@#$%^&*()_+-=[]{}|;:,.<>?'
                    }
                });
            }

            return testCases;
        }

        /**
         * @function getNextDate - 获取明天的日期
         * @returns {string} DD-MM-YYYY格式的日期
         */
        function getNextDate() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            return formatDateToDDMMYYYY(tomorrow); // 改为DD-MM-YYYY格式
        }

        /**
         * @function executeDiagnosticTests - 执行诊断测试
         * @param {Array} testCases - 测试用例列表
         * @param {string} diagnosticLevel - 诊断级别
         * @returns {Object} 测试结果
         */
        async function executeDiagnosticTests(testCases, diagnosticLevel) {
            const results = {
                total: testCases.length,
                success: 0,
                failed: 0,
                details: []
            };

            for (let i = 0; i < testCases.length; i++) {
                const testCase = testCases[i];
                console.log(`🔍 执行诊断测试 ${i + 1}/${testCases.length}: ${testCase.name}`);

                try {
                    const orderData = prepareOrderData(testCase.data);
                    const startTime = Date.now();

                    const response = await fetch(`${API_BASE_URL}/create_order`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'Authorization': `Bearer ${authToken}`
                        },
                        body: JSON.stringify(orderData)
                    });

                    const responseTime = Date.now() - startTime;
                    const responseText = await response.text();

                    // 记录API调用
                    logApiCall('/create_order', orderData, response, responseText, responseTime);

                    if (response.ok) {
                        const result = JSON.parse(responseText);
                        if (result.status === true || result.status === 'true') {
                            results.success++;
                            results.details.push({
                                testCase: testCase.name,
                                status: 'success',
                                responseTime: responseTime,
                                orderId: result.order_id || result.data?.order_id
                            });
                        } else {
                            results.failed++;
                            results.details.push({
                                testCase: testCase.name,
                                status: 'business_error',
                                error: result.message || result.error,
                                responseTime: responseTime
                            });
                        }
                    } else {
                        results.failed++;
                        const errorAnalysis = analyzeApiError(response, responseText, orderData);
                        results.details.push({
                            testCase: testCase.name,
                            status: 'api_error',
                            httpStatus: response.status,
                            error: response.statusText,
                            analysis: errorAnalysis,
                            responseTime: responseTime
                        });
                    }

                } catch (error) {
                    results.failed++;
                    results.details.push({
                        testCase: testCase.name,
                        status: 'exception',
                        error: error.message
                    });
                }

                // 在测试之间添加短暂延迟
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            return results;
        }

        /**
         * @function generateDiagnosticSummary - 生成诊断摘要报告
         * @param {Object} basicValidation - 基础验证结果
         * @param {Object} testResults - 测试结果
         * @returns {string} HTML格式的报告
         */
        function generateDiagnosticSummary(basicValidation, testResults) {
            const successRate = testResults.total > 0 ?
                Math.round((testResults.success / testResults.total) * 100) : 0;

            let report = `
                <h4>🔍 诊断测试报告</h4>
                <div style="margin: 10px 0;">
                    <strong>基础配置验证:</strong><br>
                    • 认证状态: ${basicValidation.auth ? '✅' : '❌'}<br>
                    • 车型数据: ${basicValidation.carTypes ? '✅' : '❌'} (${availableCarTypes.length}个)<br>
                    • 子分类数据: ${basicValidation.subCategories ? '✅' : '❌'} (${availableSubCategories.length}个)<br>
                    • 地区数据: ${basicValidation.regions ? '✅' : '❌'} (${availableRegions.length}个)<br>
                    • 语言数据: ${basicValidation.languages ? '✅' : '❌'} (${availableLanguages.length}个)<br>
                    • 后台用户: ${basicValidation.backendUsers ? '✅' : '❌'} (${availableBackendUsers.length}个)<br>
                </div>

                <div style="margin: 10px 0;">
                    <strong>测试结果统计:</strong><br>
                    • 总测试数: ${testResults.total}<br>
                    • 成功: ${testResults.success} (${successRate}%)<br>
                    • 失败: ${testResults.failed}<br>
                </div>
            `;

            if (basicValidation.issues.length > 0) {
                report += `
                    <div style="margin: 10px 0; color: #dc3545;">
                        <strong>⚠️ 发现的问题:</strong><br>
                        ${basicValidation.issues.map(issue => `• ${issue}`).join('<br>')}
                    </div>
                `;
            }

            // 分析失败的测试
            const failedTests = testResults.details.filter(d => d.status !== 'success');
            if (failedTests.length > 0) {
                report += `<div style="margin: 10px 0;"><strong>❌ 失败的测试:</strong><br>`;

                const errorGroups = {};
                failedTests.forEach(test => {
                    const errorKey = test.httpStatus || test.status;
                    if (!errorGroups[errorKey]) {
                        errorGroups[errorKey] = [];
                    }
                    errorGroups[errorKey].push(test);
                });

                Object.keys(errorGroups).forEach(errorKey => {
                    const tests = errorGroups[errorKey];
                    report += `<br><strong>${errorKey}错误 (${tests.length}个):</strong><br>`;
                    tests.forEach(test => {
                        report += `• ${test.testCase}: ${test.error}<br>`;
                        if (test.analysis && test.analysis.recommendations) {
                            report += `  💡 建议: ${test.analysis.recommendations.slice(0, 2).join(', ')}<br>`;
                        }
                    });
                });

                report += `</div>`;
            }

            // 成功的测试
            const successTests = testResults.details.filter(d => d.status === 'success');
            if (successTests.length > 0) {
                report += `
                    <div style="margin: 10px 0; color: #28a745;">
                        <strong>✅ 成功的测试 (${successTests.length}个):</strong><br>
                        ${successTests.map(test => `• ${test.testCase} (${test.responseTime}ms)`).join('<br>')}
                    </div>
                `;
            }

            // 总结和建议
            report += `<div style="margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">`;
            if (successRate >= 80) {
                report += `<strong>🎉 系统状态良好</strong><br>成功率${successRate}%，大部分功能正常工作。`;
            } else if (successRate >= 50) {
                report += `<strong>⚠️ 系统部分功能异常</strong><br>成功率${successRate}%，需要检查失败的测试用例。`;
            } else {
                report += `<strong>🚨 系统存在严重问题</strong><br>成功率仅${successRate}%，需要立即修复。`;
            }
            report += `</div>`;

            return report;
        }

        /**
         * @function validateAllData - 验证所有数据
         */
        async function validateAllData() {
            const resultContainer = document.getElementById('diagnosticResults');
            resultContainer.style.display = 'block';
            resultContainer.className = 'test-result result-pending';
            resultContainer.innerHTML = '✅ 正在验证所有数据...';

            try {
                const validation = {
                    carTypes: [],
                    subCategories: [],
                    regions: [],
                    languages: [],
                    backendUsers: []
                };

                // 验证车型数据
                availableCarTypes.forEach(carType => {
                    const issues = [];
                    if (!carType.id) issues.push('缺少ID');
                    if (!carType.type && !carType.name) issues.push('缺少名称');
                    if (!carType.seat_number) issues.push('缺少座位数');

                    validation.carTypes.push({
                        id: carType.id,
                        name: carType.type || carType.name,
                        issues: issues
                    });
                });

                // 验证子分类数据
                availableSubCategories.forEach(subCategory => {
                    const issues = [];
                    if (!subCategory.id) issues.push('缺少ID');
                    if (!subCategory.name) issues.push('缺少名称');

                    validation.subCategories.push({
                        id: subCategory.id,
                        name: subCategory.name,
                        issues: issues
                    });
                });

                // 验证地区数据
                availableRegions.forEach(region => {
                    const issues = [];
                    if (!region.id) issues.push('缺少ID');
                    if (!region.name) issues.push('缺少名称');

                    validation.regions.push({
                        id: region.id,
                        name: region.name,
                        issues: issues
                    });
                });

                // 验证语言数据
                availableLanguages.forEach(language => {
                    const issues = [];
                    if (!language.id) issues.push('缺少ID');
                    if (!language.name) issues.push('缺少名称');

                    validation.languages.push({
                        id: language.id,
                        name: language.name,
                        issues: issues
                    });
                });

                // 验证后台用户数据
                availableBackendUsers.forEach(user => {
                    const issues = [];
                    if (!user.id) issues.push('缺少ID');
                    if (!user.name) issues.push('缺少名称');

                    validation.backendUsers.push({
                        id: user.id,
                        name: user.name,
                        issues: issues
                    });
                });

                // 生成验证报告
                let report = '<h4>📊 数据验证报告</h4>';

                Object.keys(validation).forEach(dataType => {
                    const data = validation[dataType];
                    const totalItems = data.length;
                    const itemsWithIssues = data.filter(item => item.issues.length > 0).length;

                    report += `
                        <div style="margin: 10px 0;">
                            <strong>${dataType} (${totalItems}个):</strong>
                            ${itemsWithIssues === 0 ? '✅ 全部正常' : `⚠️ ${itemsWithIssues}个有问题`}<br>
                    `;

                    if (itemsWithIssues > 0) {
                        data.filter(item => item.issues.length > 0).forEach(item => {
                            report += `• ID ${item.id} (${item.name}): ${item.issues.join(', ')}<br>`;
                        });
                    }

                    report += '</div>';
                });

                resultContainer.className = 'test-result result-success';
                resultContainer.innerHTML = report;

            } catch (error) {
                resultContainer.className = 'test-result result-error';
                resultContainer.innerHTML = `
                    ❌ <strong>数据验证失败</strong><br>
                    错误: ${error.message}
                `;
            }
        }

        /**
         * @function generateDiagnosticReport - 生成完整的诊断报告
         */
        function generateDiagnosticReport() {
            const resultContainer = document.getElementById('diagnosticResults');
            resultContainer.style.display = 'block';
            resultContainer.className = 'test-result result-success';

            let report = `
                <h4>📋 完整诊断报告</h4>
                <div style="margin: 10px 0;">
                    <strong>系统状态:</strong><br>
                    • 认证状态: ${authToken ? '✅ 已认证' : '❌ 未认证'}<br>
                    • 当前账号: ${currentAccount?.email || '未选择'}<br>
                    • 智能匹配: ${getSmartBackendUserId() ? '✅ 启用' : '📋 使用默认'}<br>
                </div>

                <div style="margin: 10px 0;">
                    <strong>数据加载状态:</strong><br>
                    • 车型: ${availableCarTypes.length}个<br>
                    • 子分类: ${availableSubCategories.length}个<br>
                    • 地区: ${availableRegions.length}个<br>
                    • 语言: ${availableLanguages.length}个<br>
                    • 后台用户: ${availableBackendUsers.length}个<br>
                </div>

                <div style="margin: 10px 0;">
                    <strong>API调用历史:</strong><br>
                    • 总调用次数: ${apiCallHistory.length}<br>
                    • 成功调用: ${apiCallHistory.filter(call => call.success).length}<br>
                    • 失败调用: ${apiCallHistory.filter(call => !call.success).length}<br>
                </div>

                <div style="margin: 10px 0;">
                    <strong>诊断日志:</strong><br>
                    • 错误记录: ${diagnosticLogs.length}个<br>
            `;

            if (diagnosticLogs.length > 0) {
                report += `<br><strong>最近的错误:</strong><br>`;
                diagnosticLogs.slice(0, 5).forEach(log => {
                    report += `• ${log.timestamp.split('T')[1].split('.')[0]} - ${log.testCase}: ${log.error}<br>`;
                });
            }

            report += `</div>`;

            resultContainer.innerHTML = report;
            updateApiCallHistory();
        }

        /**
         * @function updateApiCallHistory - 更新API调用历史显示
         */
        function updateApiCallHistory() {
            const historyContainer = document.getElementById('apiCallHistory');
            const listContainer = document.getElementById('apiCallList');

            if (apiCallHistory.length === 0) {
                historyContainer.style.display = 'none';
                return;
            }

            historyContainer.style.display = 'block';

            let historyHtml = '';
            apiCallHistory.slice(0, 10).forEach((call, index) => {
                const statusIcon = call.success ? '✅' : '❌';
                const timestamp = call.timestamp.split('T')[1].split('.')[0];

                historyHtml += `
                    <div style="margin: 5px 0; padding: 5px; border-left: 3px solid ${call.success ? '#28a745' : '#dc3545'};">
                        <strong>${statusIcon} ${call.endpoint}</strong> (${call.responseTime}ms)<br>
                        <small>${timestamp} - HTTP ${call.responseStatus}</small><br>
                        ${!call.success ? `<small style="color: #dc3545;">错误: ${call.responseStatusText}</small>` : ''}
                    </div>
                `;
            });

            listContainer.innerHTML = historyHtml;
        }

        /**
         * @function clearDiagnosticLogs - 清除诊断日志
         */
        function clearDiagnosticLogs() {
            diagnosticLogs = [];
            apiCallHistory = [];

            const resultContainer = document.getElementById('diagnosticResults');
            const historyContainer = document.getElementById('apiCallHistory');

            resultContainer.style.display = 'none';
            historyContainer.style.display = 'none';

            console.log('🧹 诊断日志已清除');
        }

        /**
         * @function showDetailedError - 显示详细错误信息
         * @param {number} testIndex - 测试索引
         */
        function showDetailedError(testIndex) {
            if (diagnosticLogs.length > testIndex) {
                const log = diagnosticLogs[testIndex];
                const details = `
测试用例: ${log.testCase}
时间: ${log.timestamp}
错误: ${log.error}

请求数据:
${JSON.stringify(log.requestData, null, 2)}

响应内容:
${log.responseText || '无响应内容'}

错误分析:
${log.analysis ? JSON.stringify(log.analysis, null, 2) : '无分析数据'}
                `;
                alert(details);
            }
        }

        // 加载后台用户列表（使用新的管理器）
        async function loadBackendUsers() {
            return await backendUserManager.loadBackendUsersWithFallback();
        }

        // 更新后台用户选择器（使用新的管理器）
        function updateBackendUserSelector() {
            return backendUserManager.updateBackendUserSelector();
        }

        // 选择后台用户（使用新的管理器）
        function selectBackendUser(userId = null) {
            if (!userId) {
                const selector = document.getElementById('backendUserSelect');
                if (!selector || !selector.value) return;
                userId = parseInt(selector.value);
            }

            const user = availableBackendUsers.find(u => u.id === userId);
            if (user) {
                backendUserManager.selectUser(user);
            }
        }

        // 更新状态显示
        function updateAuthStatus(isAuthenticated, customMessage = null) {
            const indicator = document.getElementById('authStatus');
            if (indicator) {
                if (isAuthenticated) {
                    indicator.className = 'auth-status auth-success';
                    indicator.textContent = customMessage || '✅ 认证已完成';
                } else {
                    indicator.className = 'auth-status auth-pending';
                    indicator.textContent = customMessage || '正在认证中...';
                }
            }
        }

        function updateCurrentAccountInfo(message) {
            const infoElement = document.getElementById('currentAccountInfo');
            if (infoElement) infoElement.textContent = message;
        }

        function updateBackendUserInfo(message) {
            const infoElement = document.getElementById('backendUserInfo');
            if (infoElement) infoElement.textContent = message;
        }

        function updateSelectedBackendUserInfo(user) {
            const infoElement = document.getElementById('selectedBackendUserInfo');
            if (infoElement && user) {
                infoElement.innerHTML = `
                    <strong>当前选中:</strong> ${user.name}<br>
                    <strong>用户ID:</strong> ${user.id}<br>
                    <strong>角色:</strong> ${user.role || '未指定'}
                `;
            } else if (infoElement) {
                infoElement.textContent = '未选择后台用户';
            }
        }

        // 初始化账号选择器
        function initializeAccountSelector() {
            const selector = document.getElementById('accountSelector');
            if (!selector) return;

            selector.innerHTML = '<option value="">选择邮箱账号...</option>';
            realLoginAccounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.id;
                option.textContent = account.email;
                if (account.isDefault) option.selected = true;
                selector.appendChild(option);
            });
        }

        // 测试所有邮箱账号
        async function testAllAccounts() {
            console.log('开始测试所有邮箱账号...');
            for (const account of realLoginAccounts) {
                console.log(`测试邮箱: ${account.email}`);
                const success = await authenticateAccount(account);
                if (success) {
                    currentAccount = account;
                    // 并行加载所有必要数据
                    await Promise.all([
                        loadBackendUsers(),
                        loadCarTypes(),
                        loadSubCategories(),
                        loadRegions(),
                        loadLanguages()
                    ]);
                }
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            console.log('所有邮箱账号测试完成');
        }

        // ========== 手动输入订单测试功能 ==========

        /**
         * @function updateManualSubCategory - 根据订单类型更新子分类
         * @description 当用户选择订单类型时，自动设置对应的子分类ID
         */
        function updateManualSubCategory() {
            // 这个函数可以在未来扩展，目前订单类型和子分类的映射在提交时处理
            console.log('订单类型已更新');
        }

        /**
         * @function initializeManualForm - 初始化手动输入表单
         * @description 设置默认值和当前日期，使用DD-MM-YYYY格式
         */
        function initializeManualForm() {
            // 设置默认日期为明天
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            // HTML date input需要YYYY-MM-DD格式，但我们在提交时会转换为DD-MM-YYYY
            const dateString = formatDateToYYYYMMDD(tomorrow);
            document.getElementById('manualDate').value = dateString;

            // 生成默认OTA参考号
            const timestamp = Date.now();
            document.getElementById('manualReference').value = `MANUAL_TEST_${timestamp}`;
        }

        /**
         * @function collectManualOrderData - 收集手动输入的订单数据
         * @returns {Object} 订单数据对象
         * @description 从表单收集所有输入数据并格式化为API所需格式
         */
        function collectManualOrderData() {
            const orderType = document.getElementById('manualOrderType').value;
            const carTypeId = parseInt(document.getElementById('manualCarType').value);
            const regionId = parseInt(document.getElementById('manualRegion').value);

            // 使用动态子分类数据
            let subCategoryId = getSubCategoryByType(orderType);
            if (!subCategoryId) {
                // 回退到硬编码映射
                const subCategoryMap = {
                    'pickup': 2,    // 接机
                    'dropoff': 3,   // 送机
                    'charter': 4    // 包车
                };
                subCategoryId = subCategoryMap[orderType];
            }

            // 使用智能语言选择（默认英文，可以根据客户信息智能选择）
            let languageIds = [2]; // 默认英文
            if (availableLanguages.length > 0) {
                const englishLang = availableLanguages.find(lang =>
                    lang.code === 'en' || lang.name?.toLowerCase().includes('english')
                );
                if (englishLang) {
                    languageIds = [englishLang.id];
                }
            }

            // 获取日期并转换为DD-MM-YYYY格式
            const htmlDateValue = document.getElementById('manualDate').value; // YYYY-MM-DD格式
            const ddmmyyyyDate = convertYYYYMMDDToDDMMYYYY(htmlDateValue); // 转换为DD-MM-YYYY格式
            console.log(`📅 日期格式转换: ${htmlDateValue} (HTML格式) -> ${ddmmyyyyDate} (API格式DD-MM-YYYY)`);

            const orderData = {
                sub_category_id: subCategoryId,
                car_type_id: carTypeId,
                incharge_by_backend_user_id: selectedBackendUser?.id || 1, // 将在prepareOrderData中智能匹配
                ota_reference_number: document.getElementById('manualReference').value || `MANUAL_${Date.now()}`,
                customer_name: document.getElementById('manualCustomerName').value,
                customer_contact: document.getElementById('manualCustomerContact').value,
                customer_email: document.getElementById('manualCustomerEmail').value,
                pickup: document.getElementById('manualPickup').value,
                destination: document.getElementById('manualDestination').value,
                date: ddmmyyyyDate, // 使用DD-MM-YYYY格式
                time: document.getElementById('manualTime').value,
                passenger_number: parseInt(document.getElementById('manualPassengers').value),
                luggage_number: parseInt(document.getElementById('manualLuggage').value),
                driving_region_id: regionId,
                languages_id_array: languageIds,
                extra_requirement: document.getElementById('manualRequirement').value
            };

            return orderData;
        }

        /**
         * @function validateManualOrderData - 验证手动输入的订单数据
         * @param {Object} orderData - 订单数据
         * @returns {Object} 验证结果 {isValid: boolean, errors: string[]}
         */
        function validateManualOrderData(orderData) {
            const errors = [];

            // 必填字段验证
            if (!orderData.customer_name?.trim()) errors.push('客户姓名不能为空');
            if (!orderData.customer_contact?.trim()) errors.push('客户电话不能为空');
            if (!orderData.customer_email?.trim()) errors.push('客户邮箱不能为空');
            if (!orderData.pickup?.trim()) errors.push('接机地址不能为空');
            if (!orderData.destination?.trim()) errors.push('目的地址不能为空');
            if (!orderData.date) errors.push('服务日期不能为空');
            if (!orderData.time) errors.push('服务时间不能为空');

            // 子分类验证
            const subCategoryValidation = validateSubCategory(orderData.sub_category_id);
            if (!subCategoryValidation.isValid) {
                errors.push(subCategoryValidation.error);
            }

            // 车型验证
            const carTypeValidation = validateCarType(orderData.car_type_id);
            if (!carTypeValidation.isValid) {
                errors.push(carTypeValidation.error);
            }

            // 地区验证
            const regionValidation = validateRegion(orderData.driving_region_id);
            if (!regionValidation.isValid) {
                errors.push(regionValidation.error);
            }

            // 语言验证
            const languageValidation = validateLanguages(orderData.languages_id_array);
            if (!languageValidation.isValid) {
                errors.push(languageValidation.error);
            }

            // 数值验证
            if (orderData.passenger_number < 1) errors.push('乘客人数必须大于0');
            if (orderData.luggage_number < 0) errors.push('行李件数不能为负数');

            // 邮箱格式验证
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (orderData.customer_email && !emailRegex.test(orderData.customer_email)) {
                errors.push('邮箱格式不正确');
            }

            // 日期验证（不能是过去的日期）- 支持DD-MM-YYYY格式
            if (orderData.date) {
                // 将DD-MM-YYYY格式转换为YYYY-MM-DD格式以便Date对象解析
                const yyyymmddDate = convertDDMMYYYYToYYYYMMDD(orderData.date);
                const selectedDate = new Date(yyyymmddDate);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (isNaN(selectedDate.getTime())) {
                    errors.push('日期格式不正确，请使用DD-MM-YYYY格式');
                } else if (selectedDate < today) {
                    errors.push('服务日期不能是过去的日期');
                }
            }

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        /**
         * @function previewManualOrder - 预览手动输入的订单数据
         * @description 显示格式化的订单数据供用户确认
         */
        function previewManualOrder() {
            try {
                const orderData = collectManualOrderData();
                const validation = validateManualOrderData(orderData);

                if (!validation.isValid) {
                    alert('数据验证失败：\n' + validation.errors.join('\n'));
                    return;
                }

                // 使用智能匹配处理订单数据
                const processedData = prepareOrderData(orderData);

                // 格式化显示
                const preview = `
📋 订单数据预览：

🏷️ 基本信息：
• 订单类型: ${orderData.sub_category_id === 2 ? '接机服务' : orderData.sub_category_id === 3 ? '送机服务' : '包车服务'}
• OTA参考号: ${processedData.ota_reference_number}
• 负责人ID: ${processedData.incharge_by_backend_user_id} ${getSmartBackendUserId() ? '(智能匹配)' : '(默认选择)'}

👤 客户信息：
• 姓名: ${processedData.customer_name}
• 电话: ${processedData.customer_contact}
• 邮箱: ${processedData.customer_email}

🚗 服务信息：
• 接机地址: ${processedData.pickup}
• 目的地址: ${processedData.destination}
• 服务日期: ${processedData.date} (DD-MM-YYYY格式)
• 服务时间: ${processedData.time}
• 乘客人数: ${processedData.passenger_number}人
• 行李件数: ${processedData.luggage_number}件
• 车型: ${getCarTypeDisplayName(processedData.car_type_id)}
• 服务地区: ${getRegionDisplayName(processedData.driving_region_id)}
• 语言要求: ${getLanguagesDisplayName(processedData.languages_id_array)}

📝 特殊要求：
${processedData.extra_requirement || '无'}

确认数据无误后，点击"提交测试订单"按钮进行测试。
                `;

                alert(preview);

            } catch (error) {
                console.error('预览订单数据时出错:', error);
                alert('预览失败: ' + error.message);
            }
        }

        /**
         * @function submitManualOrder - 提交手动输入的测试订单
         * @description 验证数据并提交到GoMyHire API
         */
        async function submitManualOrder() {
            const resultContainer = document.getElementById('manualOrderResult');

            try {
                // 验证认证状态
                if (!authToken) {
                    throw new Error('请先选择邮箱账号进行认证');
                }

                // 收集和验证数据
                const orderData = collectManualOrderData();
                const validation = validateManualOrderData(orderData);

                if (!validation.isValid) {
                    throw new Error('数据验证失败：\n' + validation.errors.join('\n'));
                }

                // 显示提交状态
                resultContainer.style.display = 'block';
                resultContainer.className = 'test-result result-pending';
                resultContainer.innerHTML = '🔄 正在提交手动测试订单...';

                // 准备订单数据（应用智能匹配）
                const processedOrderData = prepareOrderData(orderData);
                const startTime = Date.now();

                // 发送API请求
                const response = await fetch(`${API_BASE_URL}/create_order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(processedOrderData)
                });

                const responseTime = Date.now() - startTime;
                const responseText = await response.text();

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`无效的JSON响应: ${parseError.message}`);
                }

                if (result.status === true || result.status === 'true') {
                    // 更新统计
                    orderTestStats.success++;
                    orderTestStats.total++;
                    updateStats();

                    resultContainer.className = 'test-result result-success';
                    resultContainer.innerHTML = `
                        ✅ <strong>手动订单提交成功</strong><br>
                        订单ID: ${result.order_id || result.data?.order_id || 'N/A'}<br>
                        消息: ${result.message || '订单创建成功'}<br>
                        负责人: ${processedOrderData.incharge_by_backend_user_id} ${getSmartBackendUserId() ? '(智能匹配)' : '(默认选择)'}<br>
                        <small>响应时间: ${responseTime}ms</small>
                    `;
                } else {
                    throw new Error(result.message || result.error || '订单创建失败');
                }

            } catch (error) {
                // 更新统计
                orderTestStats.failed++;
                orderTestStats.total++;
                updateStats();

                resultContainer.style.display = 'block';
                resultContainer.className = 'test-result result-error';
                resultContainer.innerHTML = `
                    ❌ <strong>手动订单提交失败</strong><br>
                    错误: ${error.message}<br>
                    <small>请检查输入数据并重试</small>
                `;

                console.error('手动订单提交失败:', error);
            }
        }

        /**
         * @function resetManualForm - 重置手动输入表单
         * @description 清空所有输入字段并恢复默认值
         */
        function resetManualForm() {
            // 重置选择器
            document.getElementById('manualOrderType').value = 'pickup';

            // 重置车型选择器为第一个可用选项
            const carTypeSelector = document.getElementById('manualCarType');
            if (carTypeSelector && carTypeSelector.options.length > 0) {
                carTypeSelector.value = carTypeSelector.options[0].value;
            }

            document.getElementById('manualRegion').value = '1';

            // 重置客户信息
            document.getElementById('manualCustomerName').value = '测试客户';
            document.getElementById('manualCustomerContact').value = '+***********';
            document.getElementById('manualCustomerEmail').value = '<EMAIL>';

            // 重置地址信息
            document.getElementById('manualPickup').value = 'Kuala Lumpur International Airport (KLIA)';
            document.getElementById('manualDestination').value = 'KLCC - Kuala Lumpur City Centre';

            // 重置时间信息
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            // HTML date input需要YYYY-MM-DD格式
            document.getElementById('manualDate').value = formatDateToYYYYMMDD(tomorrow);
            document.getElementById('manualTime').value = '10:00';

            // 重置数量信息
            document.getElementById('manualPassengers').value = '2';
            document.getElementById('manualLuggage').value = '2';

            // 重置其他字段
            document.getElementById('manualReference').value = `MANUAL_TEST_${Date.now()}`;
            document.getElementById('manualRequirement').value = 'TESTING - 手动输入测试订单，请勿处理';

            // 隐藏结果
            const resultContainer = document.getElementById('manualOrderResult');
            if (resultContainer) {
                resultContainer.style.display = 'none';
            }

            console.log('手动输入表单已重置');
        }

        /**
         * @function forceReloadData - 强制重新加载所有数据
         * @description 无论认证状态如何，都重新加载所有必要数据
         */
        async function forceReloadData() {
            console.log('🔄 强制重新加载所有数据...');

            try {
                if (authToken) {
                    // 有认证token，尝试从API加载
                    console.log('🌐 尝试从API加载数据...');
                    await Promise.all([
                        loadBackendUsers(),
                        loadCarTypes(),
                        loadSubCategories(),
                        loadRegions(),
                        loadLanguages()
                    ]);
                } else {
                    // 没有认证token，直接使用备用数据
                    console.log('📦 无认证token，使用备用数据...');
                    availableCarTypes = getBackupCarTypes();
                    availableSubCategories = getBackupSubCategories();
                    availableRegions = getBackupRegions();
                    availableLanguages = getBackupLanguages();
                    updateCarTypeSelectors();
                    updateRegionSelector();
                }

                console.log('✅ 数据重新加载完成');
                console.log(`🚗 车型: ${availableCarTypes.length}个`);
                console.log(`📋 子分类: ${availableSubCategories.length}个`);
                console.log(`🌍 地区: ${availableRegions.length}个`);
                console.log(`🗣️ 语言: ${availableLanguages.length}个`);

                // 显示成功提示
                alert(`数据重新加载完成！\n车型: ${availableCarTypes.length}个\n地区: ${availableRegions.length}个\n子分类: ${availableSubCategories.length}个\n语言: ${availableLanguages.length}个`);

            } catch (error) {
                console.error('数据重新加载失败:', error);
                alert('数据重新加载失败: ' + error.message);
            }
        }

        // ==================== 数据管理器测试功能 ====================

        /**
         * @function testAuthentication - 测试认证功能
         * @description 测试不同账号的认证流程
         */
        async function testAuthentication() {
            console.log('🔐 开始测试认证功能...');

            const testResults = [];

            for (const account of realLoginAccounts) {
                try {
                    console.log(`🔍 测试账号: ${account.email}`);
                    const success = await authenticateAccount(account);

                    testResults.push({
                        email: account.email,
                        success: success,
                        message: success ? '认证成功' : '认证失败'
                    });

                    console.log(`${success ? '✅' : '❌'} ${account.email}: ${success ? '认证成功' : '认证失败'}`);

                } catch (error) {
                    testResults.push({
                        email: account.email,
                        success: false,
                        message: `认证错误: ${error.message}`
                    });
                    console.error(`❌ ${account.email}: 认证错误`, error);
                }
            }

            // 显示测试结果
            const resultDiv = document.getElementById('diagnosticResults');
            if (resultDiv) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>🔐 认证测试结果</h4>
                    ${testResults.map(result => `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <strong>${result.success ? '✅' : '❌'} ${result.email}</strong><br>
                            ${result.message}
                        </div>
                    `).join('')}
                    <p><strong>总结:</strong> ${testResults.filter(r => r.success).length}/${testResults.length} 个账号认证成功</p>
                `;
            }

            console.log('🔐 认证测试完成');
        }

        /**
         * @function testDataLoading - 测试数据加载功能
         * @description 测试所有数据类型的加载
         */
        async function testDataLoading() {
            console.log('📊 开始测试数据加载功能...');

            if (!authToken) {
                alert('请先进行认证再测试数据加载');
                return;
            }

            try {
                // 清除缓存，强制重新加载
                dataManager.dataCache.clear();
                dataManager.loadingPromises.clear();

                // 测试所有数据加载
                const loadResults = await dataManager.loadAllSystemData();

                // 显示测试结果
                const resultDiv = document.getElementById('diagnosticResults');
                if (resultDiv) {
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'test-result result-success';
                    resultDiv.innerHTML = `
                        <h4>📊 数据加载测试结果</h4>
                        ${Object.entries(loadResults).map(([dataType, result]) => `
                            <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                <strong>${result.success ? '✅' : '❌'} ${dataType}</strong><br>
                                ${result.success ? `成功加载 ${result.count} 条数据` : `加载失败: ${result.error}`}
                            </div>
                        `).join('')}
                    `;
                }

                console.log('📊 数据加载测试完成:', loadResults);

            } catch (error) {
                console.error('❌ 数据加载测试失败:', error);
                alert('数据加载测试失败: ' + error.message);
            }
        }

        /**
         * @function testFallbackMechanism - 测试降级机制
         * @description 模拟API失败，测试降级机制
         */
        async function testFallbackMechanism() {
            console.log('📦 开始测试降级机制...');

            try {
                // 临时保存原始token
                const originalToken = authToken;

                // 模拟API失败 - 清除token
                authToken = null;

                // 清除缓存
                dataManager.dataCache.clear();
                dataManager.loadingPromises.clear();

                // 测试降级数据加载
                const fallbackResults = {};
                const dataTypes = ['backendUsers', 'carTypes', 'subCategories', 'drivingRegions', 'languages'];

                for (const dataType of dataTypes) {
                    try {
                        const data = await dataManager.loadDataWithFallback(
                            dataType,
                            `/${dataType}`,
                            dataManager[`_validate${dataType.charAt(0).toUpperCase() + dataType.slice(1)}`]?.bind(dataManager)
                        );

                        fallbackResults[dataType] = {
                            success: true,
                            count: data.length,
                            source: dataManager.getDataStatus(dataType).source
                        };

                    } catch (error) {
                        fallbackResults[dataType] = {
                            success: false,
                            error: error.message
                        };
                    }
                }

                // 恢复原始token
                authToken = originalToken;

                // 显示测试结果
                const resultDiv = document.getElementById('diagnosticResults');
                if (resultDiv) {
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'test-result result-success';
                    resultDiv.innerHTML = `
                        <h4>📦 降级机制测试结果</h4>
                        <p><strong>测试方式:</strong> 模拟API失败（清除认证token）</p>
                        ${Object.entries(fallbackResults).map(([dataType, result]) => `
                            <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                <strong>${result.success ? '✅' : '❌'} ${dataType}</strong><br>
                                ${result.success ?
                                    `降级成功: ${result.count} 条数据 (来源: ${result.source})` :
                                    `降级失败: ${result.error}`
                                }
                            </div>
                        `).join('')}
                        <p><strong>总结:</strong> ${Object.values(fallbackResults).filter(r => r.success).length}/${dataTypes.length} 个数据类型降级成功</p>
                    `;
                }

                console.log('📦 降级机制测试完成:', fallbackResults);

            } catch (error) {
                console.error('❌ 降级机制测试失败:', error);
                alert('降级机制测试失败: ' + error.message);
            }
        }

        /**
         * @function testConcurrentLoading - 测试并发加载
         * @description 测试同时加载多个数据类型的并发处理
         */
        async function testConcurrentLoading() {
            console.log('⚡ 开始测试并发加载...');

            if (!authToken) {
                alert('请先进行认证再测试并发加载');
                return;
            }

            try {
                // 清除缓存
                dataManager.dataCache.clear();
                dataManager.loadingPromises.clear();

                const startTime = Date.now();

                // 同时发起多个相同的数据加载请求，测试去重机制
                const promises = [];
                for (let i = 0; i < 5; i++) {
                    promises.push(dataManager.loadDataWithFallback('carTypes', '/car_types'));
                    promises.push(dataManager.loadDataWithFallback('backendUsers', '/backend_users'));
                }

                const results = await Promise.allSettled(promises);
                const endTime = Date.now();

                const successCount = results.filter(r => r.status === 'fulfilled').length;
                const failureCount = results.filter(r => r.status === 'rejected').length;

                // 显示测试结果
                const resultDiv = document.getElementById('diagnosticResults');
                if (resultDiv) {
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'test-result result-success';
                    resultDiv.innerHTML = `
                        <h4>⚡ 并发加载测试结果</h4>
                        <p><strong>测试方式:</strong> 同时发起10个重复的数据加载请求</p>
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <strong>性能指标:</strong><br>
                            总耗时: ${endTime - startTime}ms<br>
                            成功请求: ${successCount}/${promises.length}<br>
                            失败请求: ${failureCount}/${promises.length}<br>
                            缓存命中: ${promises.length - dataManager.loadingPromises.size} 次
                        </div>
                        <p><strong>结论:</strong> ${successCount === promises.length ? '✅ 并发处理正常' : '❌ 存在并发问题'}</p>
                    `;
                }

                console.log('⚡ 并发加载测试完成');

            } catch (error) {
                console.error('❌ 并发加载测试失败:', error);
                alert('并发加载测试失败: ' + error.message);
            }
        }

        /**
         * @function debugLastAPICall - 调试最后一次API调用
         * @description 显示最后一次API调用的详细信息和分析
         */
        function debugLastAPICall() {
            console.log('🔍 开始调试最后一次API调用...');

            if (apiCallHistory.length === 0) {
                alert('没有API调用历史记录');
                return;
            }

            const lastCall = apiCallHistory[apiCallHistory.length - 1];

            // 显示详细的调试信息
            const resultDiv = document.getElementById('diagnosticResults');
            if (resultDiv) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result result-info';

                const analysis = analyzeAPIError(lastCall.response, lastCall.requestData, lastCall.responseText);

                resultDiv.innerHTML = `
                    <h4>🔍 API调用调试报告</h4>
                    <div style="margin: 15px 0;">
                        <h5>📋 基本信息</h5>
                        <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;">
                            <strong>时间:</strong> ${new Date(lastCall.timestamp).toLocaleString()}<br>
                            <strong>端点:</strong> ${lastCall.endpoint}<br>
                            <strong>方法:</strong> ${lastCall.method}<br>
                            <strong>状态码:</strong> ${lastCall.responseStatus}<br>
                            <strong>响应时间:</strong> ${lastCall.responseTime || 'N/A'}ms
                        </div>
                    </div>

                    <div style="margin: 15px 0;">
                        <h5>📤 请求数据</h5>
                        <div style="background: #e8f5e8; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 0.9em; max-height: 200px; overflow-y: auto;">
                            ${JSON.stringify(lastCall.requestData, null, 2)}
                        </div>
                    </div>

                    <div style="margin: 15px 0;">
                        <h5>📥 响应数据</h5>
                        <div style="background: #ffe8e8; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 0.9em; max-height: 200px; overflow-y: auto;">
                            ${lastCall.responseText || 'N/A'}
                        </div>
                    </div>

                    <div style="margin: 15px 0;">
                        <h5>🔍 错误分析</h5>
                        <div style="background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0;">
                            <strong>错误类型:</strong> ${analysis.errorType}<br>
                            <strong>严重程度:</strong> ${analysis.severity}<br>
                            <strong>可能原因:</strong><br>
                            ${analysis.possibleCauses.map(cause => `• ${cause}`).join('<br>')}<br><br>
                            <strong>建议解决方案:</strong><br>
                            ${analysis.recommendations.map(rec => `• ${rec}`).join('<br>')}
                        </div>
                    </div>

                    <div style="margin: 15px 0;">
                        <h5>📊 请求数据分析</h5>
                        <div style="background: #e7f3ff; padding: 10px; border-radius: 4px; margin: 10px 0;">
                            ${analysis.requestAnalysis ? `
                                <strong>数据完整性:</strong> ${analysis.requestAnalysis.completeness}%<br>
                                <strong>格式正确性:</strong> ${analysis.requestAnalysis.formatCorrectness}%<br>
                                ${analysis.requestAnalysis.issues.length > 0 ? `
                                    <strong>发现问题:</strong><br>
                                    ${analysis.requestAnalysis.issues.map(issue => `• ${issue}`).join('<br>')}
                                ` : '<strong>✅ 未发现数据问题</strong>'}
                            ` : '无法分析请求数据'}
                        </div>
                    </div>
                `;
            }

            console.log('🔍 API调试完成:', lastCall);
        }

        /**
         * @function diagnose500Error - 500错误专项诊断
         * @description 专门针对500错误进行全面诊断和修复建议
         */
        async function diagnose500Error() {
            console.log('🚨 开始500错误专项诊断...');

            const resultDiv = document.getElementById('diagnosticResults');
            if (resultDiv) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result result-warning';

                resultDiv.innerHTML = `
                    <h4>🚨 500错误专项诊断</h4>
                    <div style="margin: 15px 0;">
                        <div class="diagnostic-progress">
                            <div id="diagProgress">正在执行诊断...</div>
                        </div>
                    </div>
                    <div id="diagResults"></div>
                `;
            }

            const diagResults = document.getElementById('diagResults');
            const diagProgress = document.getElementById('diagProgress');

            try {
                // 1. 认证状态检查
                diagProgress.textContent = '1/7 检查认证状态...';
                const authResult = await diagnoseAuthentication();

                // 2. 用户权限检查
                diagProgress.textContent = '2/7 检查用户权限...';
                const userResult = await diagnoseUserPermissions();

                // 3. 数据格式检查
                diagProgress.textContent = '3/7 检查数据格式...';
                const dataResult = await diagnoseDataFormat();

                // 4. API端点检查
                diagProgress.textContent = '4/7 检查API端点...';
                const endpointResult = await diagnoseAPIEndpoint();

                // 5. 日期格式兼容性检查
                diagProgress.textContent = '5/7 检查日期格式兼容性...';
                const dateResult = await diagnoseDateFormat();

                // 6. 网络连接检查
                diagProgress.textContent = '6/7 检查网络连接...';
                const networkResult = await diagnoseNetworkConnection();

                // 7. 生成修复建议
                diagProgress.textContent = '7/7 生成修复建议...';
                const fixSuggestions = generateFixSuggestions([
                    authResult, userResult, dataResult,
                    endpointResult, dateResult, networkResult
                ]);

                diagProgress.textContent = '诊断完成！';

                // 显示诊断结果
                diagResults.innerHTML = `
                    <div class="diagnostic-section">
                        <h5>🔐 认证状态</h5>
                        <div class="diagnostic-item ${authResult.status}">
                            <strong>状态:</strong> ${authResult.message}<br>
                            ${authResult.details ? `<strong>详情:</strong> ${authResult.details}` : ''}
                        </div>
                    </div>

                    <div class="diagnostic-section">
                        <h5>👤 用户权限</h5>
                        <div class="diagnostic-item ${userResult.status}">
                            <strong>状态:</strong> ${userResult.message}<br>
                            ${userResult.details ? `<strong>详情:</strong> ${userResult.details}` : ''}
                        </div>
                    </div>

                    <div class="diagnostic-section">
                        <h5>📊 数据格式</h5>
                        <div class="diagnostic-item ${dataResult.status}">
                            <strong>状态:</strong> ${dataResult.message}<br>
                            ${dataResult.details ? `<strong>详情:</strong> ${dataResult.details}` : ''}
                        </div>
                    </div>

                    <div class="diagnostic-section">
                        <h5>🌐 API端点</h5>
                        <div class="diagnostic-item ${endpointResult.status}">
                            <strong>状态:</strong> ${endpointResult.message}<br>
                            ${endpointResult.details ? `<strong>详情:</strong> ${endpointResult.details}` : ''}
                        </div>
                    </div>

                    <div class="diagnostic-section">
                        <h5>📅 日期格式</h5>
                        <div class="diagnostic-item ${dateResult.status}">
                            <strong>状态:</strong> ${dateResult.message}<br>
                            ${dateResult.details ? `<strong>详情:</strong> ${dateResult.details}` : ''}
                        </div>
                    </div>

                    <div class="diagnostic-section">
                        <h5>🌍 网络连接</h5>
                        <div class="diagnostic-item ${networkResult.status}">
                            <strong>状态:</strong> ${networkResult.message}<br>
                            ${networkResult.details ? `<strong>详情:</strong> ${networkResult.details}` : ''}
                        </div>
                    </div>

                    <div class="diagnostic-section">
                        <h5>🔧 修复建议</h5>
                        <div class="fix-suggestions">
                            ${fixSuggestions.map(suggestion => `
                                <div class="fix-suggestion ${suggestion.priority}">
                                    <strong>${suggestion.title}</strong><br>
                                    ${suggestion.description}<br>
                                    ${suggestion.action ? `<em>操作: ${suggestion.action}</em>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <button type="button" class="btn btn-primary" onclick="applyAutoFixes()">🔧 应用自动修复</button>
                        <button type="button" class="btn btn-info" onclick="testWithFixedData()">🧪 使用修复数据测试</button>
                    </div>
                `;

            } catch (error) {
                console.error('500错误诊断失败:', error);
                diagResults.innerHTML = `
                    <div class="diagnostic-error">
                        <h5>❌ 诊断过程出错</h5>
                        <p>错误信息: ${error.message}</p>
                        <p>请手动检查API配置和网络连接</p>
                    </div>
                `;
            }
        }

        /**
         * @function diagnoseAuthentication - 诊断认证状态
         * @description 检查当前的认证状态和token有效性
         * @returns {object} 诊断结果
         */
        async function diagnoseAuthentication() {
            try {
                const currentAccount = getCurrentAccount();
                if (!currentAccount) {
                    return {
                        status: 'error',
                        message: '未找到当前账号信息',
                        details: '请先选择并登录账号'
                    };
                }

                // 检查是否已登录
                const token = localStorage.getItem('api_token');
                if (!token) {
                    return {
                        status: 'error',
                        message: '未找到认证token',
                        details: '请重新登录获取有效token'
                    };
                }

                // 尝试验证token
                try {
                    const response = await fetch('https://gomyhire.com.my/api/backend_users?search=', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        return {
                            status: 'success',
                            message: 'Token有效，认证正常',
                            details: `当前账号: ${currentAccount.email}`
                        };
                    } else if (response.status === 401) {
                        return {
                            status: 'error',
                            message: 'Token已过期或无效',
                            details: '请重新登录获取新的token'
                        };
                    } else {
                        return {
                            status: 'warning',
                            message: 'Token验证失败',
                            details: `HTTP ${response.status}: 可能是服务器问题`
                        };
                    }
                } catch (error) {
                    return {
                        status: 'error',
                        message: 'Token验证请求失败',
                        details: error.message
                    };
                }

            } catch (error) {
                return {
                    status: 'error',
                    message: '认证诊断失败',
                    details: error.message
                };
            }
        }

        /**
         * @function diagnoseUserPermissions - 诊断用户权限
         * @description 检查当前用户是否有创建订单的权限
         * @returns {object} 诊断结果
         */
        async function diagnoseUserPermissions() {
            try {
                const selectedUser = getSelectedBackendUser();
                if (!selectedUser) {
                    return {
                        status: 'warning',
                        message: '未选择后台用户',
                        details: '将使用默认用户ID 1'
                    };
                }

                // 检查用户ID 37的特殊情况
                if (selectedUser.id === 37) {
                    return {
                        status: 'warning',
                        message: '使用SMW用户 (ID: 37)',
                        details: '此用户可能权限受限，建议尝试其他用户'
                    };
                }

                // 检查是否是管理员用户
                if (selectedUser.id === 1) {
                    return {
                        status: 'success',
                        message: '使用管理员用户',
                        details: '管理员用户通常有完整权限'
                    };
                }

                return {
                    status: 'info',
                    message: `使用用户: ${selectedUser.name} (ID: ${selectedUser.id})`,
                    details: '权限状态未知，建议测试验证'
                };

            } catch (error) {
                return {
                    status: 'error',
                    message: '用户权限诊断失败',
                    details: error.message
                };
            }
        }

        /**
         * @function diagnoseDataFormat - 诊断数据格式
         * @description 检查订单数据格式是否符合API要求
         * @returns {object} 诊断结果
         */
        async function diagnoseDataFormat() {
            try {
                // 生成一个测试订单数据
                const testOrderData = {
                    sub_category_id: 2,
                    car_type_id: 5,
                    incharge_by_backend_user_id: getSelectedBackendUser()?.id || 1,
                    ota_reference_number: `DIAG_${Date.now()}`,
                    customer_name: 'Test Customer',
                    customer_contact: '+***********',
                    customer_email: '<EMAIL>',
                    pickup: 'KLIA Airport',
                    destination: 'KLCC',
                    date: formatDateToDDMMYYYY(new Date()),
                    time: '10:00',
                    passenger_number: 2,
                    driving_region_id: 1
                };

                const issues = [];

                // 检查必填字段
                const requiredFields = [
                    'sub_category_id', 'car_type_id', 'incharge_by_backend_user_id',
                    'customer_name', 'customer_contact', 'customer_email',
                    'pickup', 'destination', 'date', 'time',
                    'passenger_number', 'driving_region_id'
                ];

                requiredFields.forEach(field => {
                    if (!testOrderData[field] && testOrderData[field] !== 0) {
                        issues.push(`缺少必填字段: ${field}`);
                    }
                });

                // 检查数据类型
                const numericFields = ['sub_category_id', 'car_type_id', 'incharge_by_backend_user_id', 'passenger_number', 'driving_region_id'];
                numericFields.forEach(field => {
                    if (testOrderData[field] && typeof testOrderData[field] !== 'number') {
                        issues.push(`字段 ${field} 应为数字类型`);
                    }
                });

                // 检查日期格式
                const ddmmyyyyRegex = /^\d{2}-\d{2}-\d{4}$/;
                if (!ddmmyyyyRegex.test(testOrderData.date)) {
                    issues.push(`日期格式不正确，应为DD-MM-YYYY格式，当前: ${testOrderData.date}`);
                }

                // 检查时间格式
                const timeRegex = /^\d{2}:\d{2}$/;
                if (!timeRegex.test(testOrderData.time)) {
                    issues.push(`时间格式不正确，应为HH:MM格式，当前: ${testOrderData.time}`);
                }

                // 检查邮箱格式
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(testOrderData.customer_email)) {
                    issues.push(`邮箱格式不正确: ${testOrderData.customer_email}`);
                }

                if (issues.length === 0) {
                    return {
                        status: 'success',
                        message: '数据格式检查通过',
                        details: `所有必填字段格式正确，日期格式: ${testOrderData.date}`
                    };
                } else {
                    return {
                        status: 'error',
                        message: `发现 ${issues.length} 个数据格式问题`,
                        details: issues.join('; ')
                    };
                }

            } catch (error) {
                return {
                    status: 'error',
                    message: '数据格式诊断失败',
                    details: error.message
                };
            }
        }

        /**
         * @function diagnoseAPIEndpoint - 诊断API端点
         * @description 检查API端点是否可访问
         * @returns {object} 诊断结果
         */
        async function diagnoseAPIEndpoint() {
            try {
                const baseUrl = 'https://gomyhire.com.my/api';
                const endpoint = '/create_order';
                const fullUrl = baseUrl + endpoint;

                // 尝试OPTIONS请求检查端点
                try {
                    const response = await fetch(fullUrl, {
                        method: 'OPTIONS',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        return {
                            status: 'success',
                            message: 'API端点可访问',
                            details: `端点: ${fullUrl}`
                        };
                    } else {
                        return {
                            status: 'warning',
                            message: 'API端点响应异常',
                            details: `HTTP ${response.status}: ${fullUrl}`
                        };
                    }
                } catch (error) {
                    // OPTIONS可能被阻止，尝试其他方法
                    return {
                        status: 'info',
                        message: 'API端点状态未知',
                        details: `无法直接检测端点状态: ${fullUrl}`
                    };
                }

            } catch (error) {
                return {
                    status: 'error',
                    message: 'API端点诊断失败',
                    details: error.message
                };
            }
        }

        /**
         * @function diagnoseDateFormat - 诊断日期格式兼容性
         * @description 检查日期格式是否与服务器兼容
         * @returns {object} 诊断结果
         */
        async function diagnoseDateFormat() {
            try {
                const testDate = new Date();
                const ddmmyyyy = formatDateToDDMMYYYY(testDate);
                const yyyymmdd = testDate.toISOString().split('T')[0];

                // 检查格式转换是否正确
                const ddmmyyyyRegex = /^\d{2}-\d{2}-\d{4}$/;
                if (!ddmmyyyyRegex.test(ddmmyyyy)) {
                    return {
                        status: 'error',
                        message: 'DD-MM-YYYY格式生成失败',
                        details: `生成的日期格式不正确: ${ddmmyyyy}`
                    };
                }

                // 检查日期是否有效
                const parts = ddmmyyyy.split('-');
                const day = parseInt(parts[0]);
                const month = parseInt(parts[1]);
                const year = parseInt(parts[2]);

                if (day < 1 || day > 31 || month < 1 || month > 12 || year < 2020) {
                    return {
                        status: 'error',
                        message: '日期值无效',
                        details: `日期: ${ddmmyyyy} (日:${day}, 月:${month}, 年:${year})`
                    };
                }

                return {
                    status: 'success',
                    message: '日期格式兼容性正常',
                    details: `DD-MM-YYYY: ${ddmmyyyy}, YYYY-MM-DD: ${yyyymmdd}`
                };

            } catch (error) {
                return {
                    status: 'error',
                    message: '日期格式诊断失败',
                    details: error.message
                };
            }
        }

        /**
         * @function diagnoseNetworkConnection - 诊断网络连接
         * @description 检查网络连接状态
         * @returns {object} 诊断结果
         */
        async function diagnoseNetworkConnection() {
            try {
                const startTime = Date.now();

                // 测试基本网络连接
                const response = await fetch('https://gomyhire.com.my/api/languages', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                if (response.ok) {
                    return {
                        status: 'success',
                        message: '网络连接正常',
                        details: `响应时间: ${responseTime}ms, 状态: ${response.status}`
                    };
                } else {
                    return {
                        status: 'warning',
                        message: '网络连接异常',
                        details: `HTTP ${response.status}, 响应时间: ${responseTime}ms`
                    };
                }

            } catch (error) {
                return {
                    status: 'error',
                    message: '网络连接失败',
                    details: error.message
                };
            }
        }

        /**
         * @function generateFixSuggestions - 生成修复建议
         * @description 根据诊断结果生成修复建议
         * @param {Array} diagnosticResults - 诊断结果数组
         * @returns {Array} 修复建议数组
         */
        function generateFixSuggestions(diagnosticResults) {
            const suggestions = [];

            diagnosticResults.forEach(result => {
                if (result.status === 'error') {
                    if (result.message.includes('Token')) {
                        suggestions.push({
                            priority: 'urgent',
                            title: '🔐 重新登录',
                            description: 'Token无效或过期，需要重新登录获取有效认证',
                            action: '点击账号切换按钮重新登录'
                        });
                    }

                    if (result.message.includes('数据格式')) {
                        suggestions.push({
                            priority: 'urgent',
                            title: '📊 修复数据格式',
                            description: '订单数据格式不符合API要求',
                            action: '使用自动修复功能或手动调整数据格式'
                        });
                    }

                    if (result.message.includes('网络')) {
                        suggestions.push({
                            priority: 'urgent',
                            title: '🌍 检查网络连接',
                            description: '网络连接异常，可能影响API调用',
                            action: '检查网络设置或稍后重试'
                        });
                    }
                }

                if (result.status === 'warning') {
                    if (result.message.includes('用户')) {
                        suggestions.push({
                            priority: 'medium',
                            title: '👤 更换后台用户',
                            description: '当前用户可能权限不足',
                            action: '尝试使用管理员用户(ID: 1)或其他用户'
                        });
                    }
                }
            });

            // 通用建议
            suggestions.push({
                priority: 'low',
                title: '🧪 使用简化测试数据',
                description: '使用最基本的订单数据进行测试',
                action: '点击"使用修复数据测试"按钮'
            });

            return suggestions;
        }

        /**
         * @function applyAutoFixes - 应用自动修复
         * @description 自动应用可修复的问题
         */
        async function applyAutoFixes() {
            console.log('🔧 开始应用自动修复...');

            try {
                // 1. 重新获取token
                const currentAccount = getCurrentAccount();
                if (currentAccount) {
                    await switchAccount(currentAccount.id);
                    console.log('✅ 重新获取认证token');
                }

                // 2. 重置为管理员用户
                const adminUser = availableBackendUsers.find(u => u.id === 1);
                if (adminUser) {
                    selectedBackendUser = adminUser;
                    updateBackendUserSelector();
                    console.log('✅ 切换到管理员用户');
                }

                // 3. 重新加载系统数据
                await loadAllSystemData();
                console.log('✅ 重新加载系统数据');

                alert('🔧 自动修复完成！请重新尝试创建订单。');

            } catch (error) {
                console.error('自动修复失败:', error);
                alert('❌ 自动修复失败: ' + error.message);
            }
        }

        /**
         * @function testWithFixedData - 使用修复数据测试
         * @description 使用经过修复的数据进行测试
         */
        async function testWithFixedData() {
            console.log('🧪 使用修复数据进行测试...');

            try {
                // 创建一个经过修复的测试订单
                const fixedOrderData = {
                    sub_category_id: 2, // 接机服务
                    car_type_id: 5,     // 5座车
                    incharge_by_backend_user_id: 1, // 管理员用户
                    ota_reference_number: `FIXED_${Date.now()}`,
                    customer_name: 'Test Customer',
                    customer_contact: '+***********',
                    customer_email: '<EMAIL>',
                    pickup: 'KLIA Airport',
                    destination: 'KLCC',
                    date: formatDateToDDMMYYYY(new Date()),
                    time: '10:00',
                    passenger_number: 2,
                    driving_region_id: 1,
                    language_id: 2,
                    remark: '500错误修复测试订单'
                };

                console.log('🧪 测试订单数据:', fixedOrderData);

                // 执行测试
                const result = await testSingleOrder('500错误修复测试', fixedOrderData);

                if (result.success) {
                    alert('✅ 修复数据测试成功！500错误已解决。');
                } else {
                    alert('❌ 修复数据测试仍然失败: ' + result.error);
                }

            } catch (error) {
                console.error('修复数据测试失败:', error);
                alert('❌ 修复数据测试失败: ' + error.message);
            }
        }

        /**
         * @function simulateAPIFailure - 模拟API失败
         * @description 模拟各种API失败场景
         */
        async function simulateAPIFailure() {
            console.log('❌ 开始模拟API失败场景...');

            const scenarios = [
                {
                    name: '网络错误',
                    test: async () => {
                        // 模拟网络错误
                        const originalFetch = window.fetch;
                        window.fetch = () => Promise.reject(new Error('Network Error'));

                        try {
                            await dataManager.loadDataWithFallback('carTypes', '/car_types');
                            return { success: true, message: '降级成功' };
                        } catch (error) {
                            return { success: false, message: error.message };
                        } finally {
                            window.fetch = originalFetch;
                        }
                    }
                },
                {
                    name: 'HTTP 500错误',
                    test: async () => {
                        const originalFetch = window.fetch;
                        window.fetch = () => Promise.resolve({
                            ok: false,
                            status: 500,
                            statusText: 'Internal Server Error'
                        });

                        try {
                            await dataManager.loadDataWithFallback('subCategories', '/sub_categories');
                            return { success: true, message: '降级成功' };
                        } catch (error) {
                            return { success: false, message: error.message };
                        } finally {
                            window.fetch = originalFetch;
                        }
                    }
                },
                {
                    name: '空数据响应',
                    test: async () => {
                        const originalFetch = window.fetch;
                        window.fetch = () => Promise.resolve({
                            ok: true,
                            json: () => Promise.resolve({ data: [] })
                        });

                        try {
                            await dataManager.loadDataWithFallback('drivingRegions', '/driving_regions');
                            return { success: true, message: '降级成功' };
                        } catch (error) {
                            return { success: false, message: error.message };
                        } finally {
                            window.fetch = originalFetch;
                        }
                    }
                }
            ];

            const results = [];

            for (const scenario of scenarios) {
                try {
                    console.log(`🧪 测试场景: ${scenario.name}`);
                    const result = await scenario.test();
                    results.push({
                        name: scenario.name,
                        ...result
                    });
                    console.log(`${result.success ? '✅' : '❌'} ${scenario.name}: ${result.message}`);
                } catch (error) {
                    results.push({
                        name: scenario.name,
                        success: false,
                        message: error.message
                    });
                    console.error(`❌ ${scenario.name}: ${error.message}`);
                }
            }

            // 显示测试结果
            const resultDiv = document.getElementById('diagnosticResults');
            if (resultDiv) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>❌ API失败模拟测试结果</h4>
                    ${results.map(result => `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <strong>${result.success ? '✅' : '❌'} ${result.name}</strong><br>
                            ${result.message}
                        </div>
                    `).join('')}
                    <p><strong>总结:</strong> ${results.filter(r => r.success).length}/${results.length} 个场景处理正确</p>
                `;
            }

            console.log('❌ API失败模拟测试完成');
        }

        // ==================== 数据管理器测试功能结束 ====================

        // 页面初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('GoMyHire API测试工具初始化 - 精简版');
            try {
                initializeAccountSelector();
                initializeManualForm(); // 初始化手动输入表单
                updateStats();

                updateAuthStatus(false, '正在初始化...');
                updateCurrentAccountInfo('正在加载默认邮箱账号...');

                const defaultAccount = realLoginAccounts.find(acc => acc.isDefault);
                if (defaultAccount) {
                    await switchToAccount(defaultAccount.id);
                } else {
                    updateAuthStatus(false, '请选择邮箱账号');
                    updateCurrentAccountInfo('请从上方选择邮箱账号');
                    // 即使没有认证，也加载降级数据
                    console.log('📦 没有默认账号，使用数据管理器降级数据');
                    availableCarTypes = dataManager.fallbackData.carTypes;
                    availableSubCategories = dataManager.fallbackData.subCategories;
                    availableRegions = dataManager.fallbackData.drivingRegions;
                    availableLanguages = dataManager.fallbackData.languages;
                    availableBackendUsers = dataManager.fallbackData.backendUsers;

                    // 设置降级状态
                    dataManager.setDataStatus('carTypes', 'fallback', 'memory-bank', '无认证，使用降级数据');
                    dataManager.setDataStatus('subCategories', 'fallback', 'memory-bank', '无认证，使用降级数据');
                    dataManager.setDataStatus('drivingRegions', 'fallback', 'memory-bank', '无认证，使用降级数据');
                    dataManager.setDataStatus('languages', 'fallback', 'memory-bank', '无认证，使用降级数据');
                    dataManager.setDataStatus('backendUsers', 'fallback', 'memory-bank', '无认证，使用降级数据');

                    updateCarTypeSelectors();
                    updateRegionSelector();
                    backendUserManager.updateBackendUserSelector();
                }

                // 确保数据已加载（使用数据管理器双重保险）
                if (availableCarTypes.length === 0) {
                    console.log('🔧 检测到车型数据为空，使用降级数据');
                    availableCarTypes = dataManager.fallbackData.carTypes;
                    dataManager.setDataStatus('carTypes', 'fallback', 'memory-bank', '初始化时使用降级数据');
                    updateCarTypeSelectors();
                }
                if (availableRegions.length === 0) {
                    console.log('🔧 检测到地区数据为空，使用降级数据');
                    availableRegions = dataManager.fallbackData.drivingRegions;
                    dataManager.setDataStatus('drivingRegions', 'fallback', 'memory-bank', '初始化时使用降级数据');
                    updateRegionSelector();
                }
                if (availableBackendUsers.length === 0) {
                    console.log('🔧 检测到后台用户数据为空，使用降级数据');
                    availableBackendUsers = dataManager.fallbackData.backendUsers;
                    dataManager.setDataStatus('backendUsers', 'fallback', 'memory-bank', '初始化时使用降级数据');
                    backendUserManager.updateBackendUserSelector();
                }

                console.log('测试工具初始化完成');
                console.log(`🚗 当前可用车型数量: ${availableCarTypes.length}`);
                console.log(`👤 当前可用后台用户数量: ${availableBackendUsers.length}`);
                console.log(`📋 当前可用子分类数量: ${availableSubCategories.length}`);
                console.log(`🌍 当前可用地区数量: ${availableRegions.length}`);
                console.log(`🗣️ 当前可用语言数量: ${availableLanguages.length}`);
            } catch (error) {
                console.error('页面初始化失败:', error);
                updateAuthStatus(false, '❌ 初始化失败');
                updateCurrentAccountInfo('初始化失败');
            }
        });
    </script>
</body>
</html>
