/* 通知系统样式 */

/* 通知容器 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    pointer-events: none;
}

/* 通知项 */
.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    overflow: hidden;
    transform: translateX(100%);
    transition: all 0.3s ease;
    pointer-events: auto;
    border-left: 4px solid #ccc;
    max-width: 100%;
    word-wrap: break-word;
}

/* 显示动画 */
.notification-show {
    transform: translateX(0);
}

/* 隐藏动画 */
.notification-hide {
    transform: translateX(100%);
    opacity: 0;
}

/* 通知类型样式 */
.notification-success {
    border-left-color: #27ae60;
}

.notification-error {
    border-left-color: #e74c3c;
}

.notification-warning {
    border-left-color: #f39c12;
}

.notification-info {
    border-left-color: #3498db;
}

/* 通知头部 */
.notification-header {
    display: flex;
    align-items: center;
    padding: 12px 16px 8px;
    background: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.notification-icon {
    font-size: 16px;
    margin-right: 8px;
    flex-shrink: 0;
}

.notification-title {
    font-weight: 600;
    color: #2c3e50;
    flex: 1;
    font-size: 14px;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #7f8c8d;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #2c3e50;
}

/* 通知主体 */
.notification-body {
    padding: 8px 16px 12px;
}

.notification-message {
    color: #34495e;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 8px;
}

.notification-message pre {
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    font-size: 11px;
    overflow-x: auto;
    margin: 4px 0;
    border: 1px solid #e9ecef;
}

/* 详情按钮 */
.notification-details-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.notification-details-btn:hover {
    background: #2980b9;
}

/* 时间戳 */
.notification-time {
    padding: 4px 16px 8px;
    font-size: 11px;
    color: #7f8c8d;
    text-align: right;
    background: rgba(0, 0, 0, 0.02);
}

/* 模态框样式 */
.notification-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10001;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.notification-modal-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    max-height: 80vh;
    width: 90%;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.notification-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.notification-modal-header h2 {
    margin: 0;
    font-size: 18px;
    color: #2c3e50;
}

.notification-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #7f8c8d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-modal-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #2c3e50;
}

.notification-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.notification-modal-body h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
}

.response-details {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    border: 1px solid #e9ecef;
    color: #2c3e50;
}

.order-result-detail {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 12px;
    border-left: 4px solid #ccc;
}

.order-result-detail.success {
    border-left-color: #27ae60;
    background: #f8fff8;
}

.order-result-detail.error {
    border-left-color: #e74c3c;
    background: #fff8f8;
}

.order-result-detail h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #2c3e50;
}

.order-result-detail p {
    margin: 4px 0;
    font-size: 13px;
    color: #34495e;
}

.notification-modal-footer {
    padding: 16px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s ease;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .notification {
        margin-bottom: 8px;
    }
    
    .notification-modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .notification-modal-body {
        padding: 16px;
        max-height: 70vh;
    }
    
    .response-details {
        font-size: 11px;
        padding: 12px;
    }
}

/* 滚动条样式 */
.notification-modal-body::-webkit-scrollbar {
    width: 6px;
}

.notification-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.notification-modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notification-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.response-details::-webkit-scrollbar {
    height: 6px;
}

.response-details::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.response-details::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.response-details::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}