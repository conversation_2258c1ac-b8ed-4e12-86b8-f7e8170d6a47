/**
 * @file llm-service.js - LLM服务管理类
 * @description 管理Gemini AI服务，提供统一的LLM处理接口
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

/**
 * @class LLMService - LLM服务管理类
 * @description 管理Gemini AI服务
 */
class LLMService {
    /**
     * @function constructor - 构造函数
     */
    constructor() {
        // 初始化提示词管理器
        this.promptManager = new PromptManager();

        // Gemini 状态
        this.geminiStatus = {
            connectionStatus: 'checking',
            lastCheckTime: null,
            consecutiveFailures: 0,
            isChecking: false,
            lastSuccessTime: null
        };

        // 当前使用的 LLM
        this.currentLLM = 'gemini';
        
        // LRU缓存实现
        this.responseCache = new Map();
        this.cacheOrder = []; // 用于LRU排序
        this.cacheConfig = {
            maxSize: 100,
            ttl: 5 * 60 * 1000 // 5分钟TTL
        };
        this.cacheStats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalRequests: 0
        };
        
        // 定期清理过期缓存
        this.startCacheCleanup();
    }



    /**
     * @function checkGeminiConnection - 检测Gemini API连接状态
     * @returns {Promise<boolean>} 连接状态
     */
    async checkGeminiConnection() {
        if (this.geminiStatus.isChecking) {
            logger.debug('Gemini', '连接检测正在进行中，跳过重复检测');
            return this.geminiStatus.connectionStatus === 'connected';
        }

        this.geminiStatus.isChecking = true;
        const previousStatus = this.geminiStatus.connectionStatus;
        this.geminiStatus.connectionStatus = 'checking';

        logger.info('Gemini', '开始检测Gemini API连接状态', {
            previousStatus: previousStatus,
            consecutiveFailures: this.geminiStatus.consecutiveFailures
        });

        try {
            // 检查API Key是否存在
            if (!SYSTEM_CONFIG.API.GEMINI.API_KEY) {
                logger.warn('Gemini', 'Gemini API Key未配置');
                this.geminiStatus.connectionStatus = 'disconnected';
                this.geminiStatus.consecutiveFailures++;
                return false;
            }

            // 发送测试请求
            const startTime = Date.now();
            const response = await fetch(
                `${SYSTEM_CONFIG.API.GEMINI.API_URL}?key=${SYSTEM_CONFIG.API.GEMINI.API_KEY}`,
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: 'ping' }] }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 5
                        }
                    }),
                    signal: AbortSignal.timeout(30000) // 30秒超时
                }
            );

            const responseTime = Date.now() - startTime;

            if (response.ok) {
                const wasDisconnected = previousStatus === 'disconnected';
                this.geminiStatus.connectionStatus = 'connected';
                this.geminiStatus.lastCheckTime = new Date();
                this.geminiStatus.lastSuccessTime = new Date();
                this.geminiStatus.consecutiveFailures = 0;

                logger.success('Gemini', 'Gemini API连接成功', {
                    responseTime: `${responseTime}ms`,
                    statusChanged: wasDisconnected
                });

                return true;
            } else {
                this.geminiStatus.connectionStatus = 'disconnected';
                this.geminiStatus.consecutiveFailures++;

                logger.error('Gemini', `Gemini API连接失败: ${response.status}`, {
                    responseTime: `${responseTime}ms`,
                    consecutiveFailures: this.geminiStatus.consecutiveFailures
                });

                return false;
            }
        } catch (error) {
            this.geminiStatus.connectionStatus = 'disconnected';
            this.geminiStatus.consecutiveFailures++;

            logger.error('Gemini', 'Gemini API连接检测失败', {
                error: error.message,
                consecutiveFailures: this.geminiStatus.consecutiveFailures
            });

            return false;
        } finally {
            this.geminiStatus.isChecking = false;
            this.geminiStatus.lastCheckTime = new Date();
        }
    }

    /**
     * @function processOrderText - 处理订单文本
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Promise<object>} 处理结果
     */
    async processOrderText(text, otaType = 'auto') {
        const startTime = Date.now();
        
        // LLM优化：检查缓存
        const cacheKey = this.generateCacheKey(text, otaType);
        const cachedResult = this.getCachedResponse(cacheKey);
        
        if (cachedResult) {
            logger.info('LLM', '使用缓存结果', {
                textLength: text.length,
                otaType: otaType,
                cacheHit: true
            });
            
            return {
                ...cachedResult,
                processingTime: Date.now() - startTime,
                fromCache: true
            };
        }
        
        logger.info('LLM', '开始处理订单文本', {
            textLength: text.length,
            otaType: otaType
        });

        try {
            // 使用Gemini处理
            logger.info('LLM', '开始调用Gemini API');
            const geminiStartTime = Date.now();
            const geminiResult = await this.callGemini(text, otaType);
            const geminiProcessingTime = Date.now() - geminiStartTime;
            
            logger.info('LLM', 'Gemini API调用完成', {
                processingTime: geminiProcessingTime,
                success: geminiResult.success,
                error: geminiResult.error || null
            });
            
            if (geminiResult.success) {
                const result = {
                    success: true,
                    data: geminiResult.data,
                    provider: 'gemini',
                    processingTime: Date.now() - startTime,
                    apiCallTime: geminiProcessingTime
                };
                
                // LLM优化：缓存成功结果
                this.setCachedResponse(cacheKey, result);
                
                logger.success('LLM', 'Gemini处理成功', {
                    totalProcessingTime: result.processingTime,
                    apiCallTime: geminiProcessingTime,
                    provider: 'gemini'
                });
                
                return result;
            }

            // Gemini失败
            const totalFailTime = Date.now() - startTime;
            logger.error('LLM', 'Gemini处理失败', {
                geminiError: geminiResult.error,
                geminiProcessingTime: geminiProcessingTime,
                totalProcessingTime: totalFailTime
            });

            return {
                success: false,
                error: 'Gemini处理失败',
                processingTime: totalFailTime,
                details: {
                    geminiError: geminiResult.error,
                    geminiTime: geminiProcessingTime
                }
            };

        } catch (error) {
            logger.error('LLM', 'LLM处理异常', { error: error.message });
            return {
                success: false,
                error: error.message,
                processingTime: Date.now() - startTime
            };
        }
    }



    /**
     * @function callGemini - 调用Gemini API
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Promise<object>} 处理结果
     */
    async callGemini(text, otaType) {
        const requestStartTime = Date.now();
        
        try {
            const prompt = this.promptManager.getOTAPrompt(otaType, text, new Date().toISOString().split('T')[0]);
            
            const requestBody = {
                contents: [{ parts: [{ text: prompt }] }],
                generationConfig: SYSTEM_CONFIG.API.GEMINI.MODEL_CONFIG
            };
            
            logger.debug('Gemini', '发送API请求', {
                textLength: text.length,
                otaType: otaType,
                timeout: SYSTEM_CONFIG.API.GEMINI.TIMEOUT
            });
            
            const response = await fetch(
                `${SYSTEM_CONFIG.API.GEMINI.API_URL}?key=${SYSTEM_CONFIG.API.GEMINI.API_KEY}`,
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody),
                    signal: AbortSignal.timeout(SYSTEM_CONFIG.API.GEMINI.TIMEOUT)
                }
            );

            const responseTime = Date.now() - requestStartTime;
            
            if (!response.ok) {
                const errorMsg = `Gemini API请求失败: ${response.status} ${response.statusText}`;
                logger.error('Gemini', errorMsg, {
                    responseTime: responseTime,
                    status: response.status,
                    statusText: response.statusText
                });
                throw new Error(errorMsg);
            }

            const data = await response.json();
            
            const content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
            const parseResult = this.parseResponse(content);
            
            const totalTime = Date.now() - requestStartTime;
            logger.debug('Gemini', 'API请求成功', {
                responseTime: totalTime,
                contentLength: content.length
            });

            return {
                success: true,
                data: parseResult,
                requestTime: totalTime
            };

        } catch (error) {
            const errorTime = Date.now() - requestStartTime;
            const errorMsg = `Gemini调用失败: ${error.message}`;
            

            
            logger.error('Gemini', errorMsg, {
                requestTime: errorTime,
                errorType: error.name,
                isTimeout: error.message.includes('timeout') || error.message.includes('aborted')
            });
            
            return {
                success: false,
                error: errorMsg,
                requestTime: errorTime
            };
        }
    }

    /**
     * @function generateCacheKey - 生成缓存键
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {string} 缓存键
     */
    generateCacheKey(text, otaType) {
        // 使用文本内容和OTA类型生成简单哈希
        const content = `${text.trim()}_${otaType}`;
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return `llm_${Math.abs(hash).toString(36)}`;
    }

    /**
     * @function getCachedResponse - 获取缓存响应（LRU实现）
     * @param {string} cacheKey - 缓存键
     * @returns {object|null} 缓存的响应或null
     */
    getCachedResponse(cacheKey) {
        this.cacheStats.totalRequests++;
        
        const cached = this.responseCache.get(cacheKey);
        if (!cached) {
            this.cacheStats.misses++;
            return null;
        }

        // 检查TTL
        if (Date.now() - cached.timestamp > this.cacheConfig.ttl) {
            this.responseCache.delete(cacheKey);
            this.removeCacheOrder(cacheKey);
            this.cacheStats.misses++;
            return null;
        }

        // LRU：移动到最前面
        this.updateCacheOrder(cacheKey);
        this.cacheStats.hits++;
        
        return cached.data;
    }

    /**
     * @function setCachedResponse - 设置缓存响应（LRU实现）
     * @param {string} cacheKey - 缓存键
     * @param {object} data - 要缓存的数据
     */
    setCachedResponse(cacheKey, data) {
        // 如果已存在，更新数据和顺序
        if (this.responseCache.has(cacheKey)) {
            this.responseCache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });
            this.updateCacheOrder(cacheKey);
            return;
        }

        // 检查缓存大小限制
        if (this.responseCache.size >= this.cacheConfig.maxSize) {
            // LRU：删除最久未使用的项
            const oldestKey = this.cacheOrder.pop();
            if (oldestKey) {
                this.responseCache.delete(oldestKey);
                this.cacheStats.evictions++;
            }
        }

        this.responseCache.set(cacheKey, {
            data: data,
            timestamp: Date.now()
        });
        
        // 添加到最前面
        this.cacheOrder.unshift(cacheKey);
    }

    /**
     * @function updateCacheOrder - 更新缓存顺序（LRU）
     * @param {string} cacheKey - 缓存键
     */
    updateCacheOrder(cacheKey) {
        // 移除旧位置
        this.removeCacheOrder(cacheKey);
        // 添加到最前面
        this.cacheOrder.unshift(cacheKey);
    }

    /**
     * @function removeCacheOrder - 从缓存顺序中移除
     * @param {string} cacheKey - 缓存键
     */
    removeCacheOrder(cacheKey) {
        const index = this.cacheOrder.indexOf(cacheKey);
        if (index > -1) {
            this.cacheOrder.splice(index, 1);
        }
    }

    /**
     * @function startCacheCleanup - 启动缓存清理定时器
     */
    startCacheCleanup() {
        // 每5分钟清理一次过期缓存
        setInterval(() => {
            this.cleanupExpiredCache();
        }, 5 * 60 * 1000);
    }

    /**
     * @function cleanupExpiredCache - 清理过期缓存
     */
    cleanupExpiredCache() {
        const now = Date.now();
        let cleanedCount = 0;
        
        for (const [key, cached] of this.responseCache.entries()) {
            if (now - cached.timestamp > this.cacheConfig.ttl) {
                this.responseCache.delete(key);
                this.removeCacheOrder(key);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            logger.debug('LLM', `清理了${cleanedCount}个过期缓存项`);
        }
    }

    /**
     * @function getCacheStats - 获取缓存统计
     * @returns {object} 缓存统计信息
     */
    getCacheStats() {
        const hitRate = this.cacheStats.totalRequests > 0 ? 
            (this.cacheStats.hits / this.cacheStats.totalRequests * 100).toFixed(2) : 0;
            
        return {
            ...this.cacheStats,
            hitRate: `${hitRate}%`,
            currentSize: this.responseCache.size,
            maxSize: this.cacheConfig.maxSize
        };
    }

    /**
     * @function clearCache - 清空缓存
     */
    clearCache() {
        this.responseCache.clear();
        this.cacheOrder = [];
        this.cacheStats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalRequests: 0
        };
        logger.info('LLM', '缓存已清空');
    }

    /**
     * @function extractOrderNumber - 智能提取订单号
     * @param {string} orderText - 订单文本内容
     * @returns {Promise<object>} 提取结果
     */
    async extractOrderNumber(orderText) {
        const startTime = Date.now();

        logger.info('LLM', '开始智能提取订单号', {
            textLength: orderText.length
        });

        try {
            // 获取订单号识别提示词
            const prompt = this.promptManager.getOrderNumberExtractionPrompt(orderText);

            logger.debug('LLM', '订单号识别提示词已生成', {
                promptLength: prompt.length
            });

            // 调用Gemini进行订单号识别
            const response = await fetch(
                `${SYSTEM_CONFIG.API.GEMINI.API_URL}?key=${SYSTEM_CONFIG.API.GEMINI.API_KEY}`,
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: prompt }] }],
                        generationConfig: {
                            temperature: 0.1, // 低温度确保一致性
                            maxOutputTokens: 200 // 限制输出长度
                        }
                    }),
                    signal: AbortSignal.timeout(15000) // 15秒超时
                }
            );

            const responseTime = Date.now() - startTime;

            if (!response.ok) {
                throw new Error(`Gemini API请求失败: ${response.status}`);
            }

            const data = await response.json();
            const content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';

            logger.debug('LLM', 'Gemini订单号识别响应', {
                responseTime: responseTime,
                contentLength: content.length,
                rawContent: content
            });

            // 解析JSON响应
            const extractionResult = this.parseOrderNumberResponse(content);

            logger.info('LLM', '订单号提取完成', {
                success: extractionResult.success,
                orderNumber: extractionResult.orderNumber,
                confidence: extractionResult.confidence,
                processingTime: responseTime
            });

            return {
                success: extractionResult.success,
                orderNumber: extractionResult.orderNumber,
                confidence: extractionResult.confidence,
                reasoning: extractionResult.reasoning,
                processingTime: responseTime
            };

        } catch (error) {
            const errorTime = Date.now() - startTime;

            logger.error('LLM', '订单号提取失败', {
                error: error.message,
                processingTime: errorTime
            });

            return {
                success: false,
                error: error.message,
                processingTime: errorTime
            };
        }
    }

    /**
     * @function parseOrderNumberResponse - 解析订单号识别响应
     * @param {string} content - LLM响应内容
     * @returns {object} 解析结果
     */
    parseOrderNumberResponse(content) {
        try {
            // 尝试提取JSON内容
            const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
            let jsonStr = jsonMatch ? jsonMatch[1] : content;

            // 清理可能的额外内容
            jsonStr = jsonStr.trim();

            // 如果不是以{开头，尝试找到JSON部分
            if (!jsonStr.startsWith('{')) {
                const jsonStart = jsonStr.indexOf('{');
                const jsonEnd = jsonStr.lastIndexOf('}');
                if (jsonStart !== -1 && jsonEnd !== -1) {
                    jsonStr = jsonStr.substring(jsonStart, jsonEnd + 1);
                }
            }

            const result = JSON.parse(jsonStr);

            // 验证结果格式
            if (typeof result === 'object' && result !== null) {
                return {
                    success: true,
                    orderNumber: result.orderNumber || null,
                    confidence: parseFloat(result.confidence) || 0.0,
                    reasoning: result.reasoning || '无说明'
                };
            } else {
                throw new Error('响应格式不正确');
            }

        } catch (error) {
            logger.warn('LLM', '订单号响应解析失败，尝试备用解析', {
                error: error.message,
                content: content.substring(0, 200)
            });

            // 备用解析：尝试从文本中直接提取可能的订单号
            return this.fallbackOrderNumberExtraction(content);
        }
    }

    /**
     * @function fallbackOrderNumberExtraction - 备用订单号提取
     * @param {string} content - 响应内容
     * @returns {object} 提取结果
     */
    fallbackOrderNumberExtraction(content) {
        // 简单的正则表达式匹配
        const patterns = [
            /[A-Z]{2,4}\d{4,8}/g,           // 字母+数字组合
            /\d{8,16}/g,                    // 纯数字组合
            /[A-Z0-9]{8,12}/g               // 字母数字混合
        ];

        for (const pattern of patterns) {
            const matches = content.match(pattern);
            if (matches && matches.length > 0) {
                return {
                    success: true,
                    orderNumber: matches[0],
                    confidence: 0.3, // 低置信度
                    reasoning: '备用正则表达式提取'
                };
            }
        }

        return {
            success: false,
            orderNumber: null,
            confidence: 0.0,
            reasoning: '无法识别有效订单号'
        };
    }

    /**
     * @function parseResponse - 解析LLM响应
     * @param {string} content - LLM响应内容
     * @returns {object} 解析后的数据
     */
    parseResponse(content) {
        try {
            const orders = [];

            // 检查内容是否为空
            if (!content || content.trim() === '') {
                return {
                    rawContent: content,
                    orders: [],
                    metadata: { 
                        error: '内容为空',
                        parseSuccess: false,
                        totalOrders: 0
                    }
                };
            }
            
            // 预处理内容：移除可能的隐藏字符
            const cleanContent = content
                .replace(/\r\n/g, '\n')  // 统一换行符
                .replace(/\r/g, '\n')    // 统一换行符
                .replace(/[\u200B-\u200D\uFEFF]/g, '')  // 移除零宽字符
                .trim();

            // 解析结构化文本格式的订单数据
            const orderData = this.parseStructuredText(cleanContent);

            if (orderData && typeof orderData === 'object' && Object.keys(orderData).length > 0) {
                // 转换为标准订单格式
                const standardOrder = this.convertToStandardOrder(orderData);

                if (standardOrder && typeof standardOrder === 'object') {
                    orders.push(standardOrder);
                }
            }
            
            const result = {
                rawContent: content,
                orders: orders,
                metadata: {
                    totalOrders: orders.length,
                    parseSuccess: orders.length > 0,
                    cleanedContent: cleanContent,
                    orderDataKeys: orderData ? Object.keys(orderData) : []
                }
            };
            
            return result;

        } catch (error) {
            logger.error('LLM响应解析', '解析失败', { error: error.message });
            return {
                rawContent: content,
                orders: [],
                metadata: {
                    error: error.message,
                    parseSuccess: false,
                    totalOrders: 0,
                    errorStack: error.stack
                }
            };
        }
    }
    
    /**
     * @function parseStructuredText - 解析结构化文本
     * @param {string} text - 结构化文本
     * @returns {object} 解析后的订单数据
     */
    parseStructuredText(text) {
        const orderData = {};
        const lines = text.split('\n');

        for (let i = 0; i < lines.length; i++) {
            const originalLine = lines[i];
            const trimmedLine = originalLine.trim();

            // 跳过空行和分隔符行
            if (trimmedLine === '' || trimmedLine.startsWith('=')) {
                continue;
            }
            
            // 尝试多种冒号格式的解析
            let colonIndex = -1;
            const colonVariants = [':', '：', ' : ', ' ： '];
            
            for (const colon of colonVariants) {
                colonIndex = trimmedLine.indexOf(colon);
                if (colonIndex > 0) {
                    break;
                }
            }

            if (colonIndex > 0) {
                const key = trimmedLine.substring(0, colonIndex).trim();
                const value = trimmedLine.substring(colonIndex + (colonVariants.find(c => trimmedLine.indexOf(c) === colonIndex)?.length || 1)).trim();
                
                // 扩展的字段映射表
                const fieldMapping = {
                    // 中文字段
                    '日期': 'date',
                    '时间': 'time', 
                    '姓名': 'customer_name',
                    '航班': 'flight_number',
                    '航班号': 'flight_number',
                    '客户姓名': 'customer_name',
                    '联系方式': 'customer_contact',
                    '联系电话': 'customer_contact',
                    '邮箱': 'customer_email',
                    '电子邮箱': 'customer_email',
                    '服务类型': 'service_type',
                    '乘客人数': 'passenger_count',
                    '车型': 'car_type',
                    '价格': 'ota_price',
                    '司机费用': 'driver_fee',
                    '特殊要求': 'extra_requirement',
                    '其他': 'other',
                    '备注': 'other',
                    // 英文字段
                    'pickup': 'pickup_location',
                    'drop': 'drop_location',
                    'service_type': 'service_type',
                    'passenger_count': 'passenger_count',
                    'car_type': 'car_type',
                    'ota_price': 'ota_price',
                    'customer_contact': 'customer_contact',
                    'customer_email': 'customer_email',
                    'driver_fee': 'driver_fee',
                    'extra_requirement': 'extra_requirement',
                    'other': 'other',
                    'date': 'date',
                    'time': 'time',
                    'customer_name': 'customer_name',
                    'flight_number': 'flight_number'
                };
                
                const mappedKey = fieldMapping[key] || key.toLowerCase().replace(/\s+/g, '_');
                orderData[mappedKey] = value;
            }
        }

        return orderData;
    }
    
    /**
     * @function convertToStandardOrder - 转换为标准订单格式
     * @param {object} orderData - 原始订单数据
     * @returns {object} 标准格式的订单
     */
    convertToStandardOrder(orderData) {
        try {
            // 验证输入数据
            if (!orderData || typeof orderData !== 'object') {
                return null;
            }

            // 生成唯一的订单ID
            const orderId = `ORDER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // 处理日期时间 - 支持新旧字段名
            let orderDateTime = '';
            const serviceDate = orderData.service_date || orderData.date;
            const serviceTime = orderData.service_time || orderData.time;

            if (serviceDate && serviceTime) {
                // 移除时间中的 "-CD" 后缀
                const cleanTime = serviceTime.replace(/-CD$/, '').trim();
                orderDateTime = `${serviceDate} ${cleanTime}`;
            }
            const standardOrder = {
                id: orderId,
                customer_name: orderData.customer_name || '',
                customer_contact: orderData.customer_contact || '',
                customer_email: orderData.customer_email || '<EMAIL>',
                service_type: orderData.service_type || 'airport_pickup',
                pickup_location: orderData.pickup_location || '',
                drop_location: orderData.drop_location || '',
                service_date: serviceDate || '',
                service_time: serviceTime ? serviceTime.replace(/-CD$/, '').trim() : '',
                order_datetime: orderDateTime,
                flight_number: orderData.flight_number || '',
                passenger_count: parseInt(orderData.passenger_count) || 1,
                car_type: orderData.car_type || 'sedan',
                ota_price: parseFloat(orderData.ota_price) || 0,
                driver_fee: parseFloat(orderData.driver_fee) || 0,
                extra_requirement: orderData.extra_requirement || '',
                other: orderData.other || '',
                created_at: new Date().toISOString(),
                status: 'pending'
            };
            
            return standardOrder;
            
        } catch (error) {
            console.error('转换订单格式时出错:', error);
            console.error('错误堆栈:', error.stack);
            console.error('输入数据:', orderData);
            return null;
        }
    }

    /**
     * 检查单个LLM服务连接
     * @param {string} llmType - LLM类型
     * @returns {Promise<boolean>} 连接状态
     */
    async checkSingleConnection(llmType) {
        const status = this.geminiStatus;
        
        // 防止重复检查
        if (status.isChecking) {
            return status.connectionStatus === 'connected';
        }

        status.isChecking = true;

        try {
            // 检查API密钥
            const apiKey = window.config?.geminiApiKey;

            if (!apiKey) {
                logger.warn('LLM连接检查', 'Gemini API密钥未配置');
                status.connectionStatus = 'disconnected';
                status.consecutiveFailures++;
                return false;
            }
            
            // 发送测试请求
            const testPrompt = "请回复'连接正常'";
            const response = await this.callGemini(testPrompt, { timeout: 10000 });
            
            if (response && response.content) {
                status.connectionStatus = 'connected';
                status.consecutiveFailures = 0;
                status.lastSuccessTime = Date.now();
    
                return true;
            } else {
                throw new Error('响应内容为空');
            }
            
        } catch (error) {
            console.error(`[LLMService] Gemini 连接测试失败:`, error);
            status.connectionStatus = 'disconnected';
            status.consecutiveFailures++;
            return false;
        } finally {
            status.lastCheckTime = Date.now();
            status.isChecking = false;
        }
    }

    /**
     * 检查LLM连接状态
     * @param {string} llmType - LLM类型 ('gemini')
     * @returns {Promise<boolean>} 连接状态
     */
    async checkConnection(llmType = 'gemini') {
        try {
            // 检查Gemini服务
            const result = await this.checkSingleConnection('gemini');
            return result;
        } catch (error) {
            logger.error('LLMService', `连接检查失败: ${error.message}`);
            return false;
        }
    }

    /**
     * @function getStatus - 获取LLM服务状态
     * @returns {object} 服务状态
     */
    getStatus() {
        return {
            gemini: {
                ...this.geminiStatus,
                apiKeyConfigured: !!window.config?.geminiApiKey
            },
            currentLLM: this.currentLLM,
            cacheSize: this.responseCache.size
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LLMService;
} else if (typeof window !== 'undefined') {
    window.LLMService = LLMService;
}
